from . import db

class Question(db.Model):
    __tablename__ = "questions"

    id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, primary_key=True)
    question_text = db.Column(db.Text, nullable=False)
    question_type = db.Column(db.String(50), nullable=False)
    options = db.Column(db.Text)  # 存储选项的JSON字符串
    answer = db.Column(db.Text)   # 存储答案的JSON字符串
    explanation = db.Column(db.Text)
    difficulty = db.Column(db.String(20))
    category_level4_id = db.Column(db.Integer, db.ForeignKey("category_level4.id"))
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())
    is_active = db.Column(db.<PERSON><PERSON>, default=True)

    # 关联关系
    category_level4 = db.relationship("CategoryLevel4", backref="questions")

    def to_dict(self):
        # 处理options字段：如果是字符串则尝试解析，否则直接使用
        options = self.options
        if isinstance(options, str):
            try:
                import json
                options = json.loads(options)
            except (json.JSONDecodeError, TypeError):
                options = []
        
        # 处理answer字段：如果是字符串则尝试解析，否则直接使用
        answer = self.answer
        if isinstance(answer, str):
            try:
                import json
                answer = json.loads(answer)
            except (json.JSONDecodeError, TypeError):
                answer = None
        
        return {
            "id": self.id,
            "question_text": self.question_text,
            "question_type": self.question_type,
            "options": options,
            "answer": answer,
            "explanation": self.explanation,
            "difficulty": self.difficulty,
            "category_level4_id": self.category_level4_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_active": self.is_active
        }
