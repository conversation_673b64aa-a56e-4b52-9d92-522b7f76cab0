import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../constants/fonts.dart';

/// Apple Music风格的动态收缩标题AppBar
///
/// 特点：
/// - 大标题随滚动缩小并渐变透明
/// - 小标题随滚动渐变显示
/// - 支持毛玻璃效果
/// - 支持回弹效果
class AppleMusicSliverAppBar extends StatefulWidget {
  /// 标题文本
  final String title;

  /// 展开高度，决定大标题区域大小
  final double expandedHeight;

  /// 是否使用毛玻璃效果
  final bool useBlur;

  /// 背景颜色
  final Color backgroundColor;

  /// 大标题字体大小
  final double largeTitleFontSize;

  /// 小标题字体大小
  final double smallTitleFontSize;

  /// 返回按钮点击回调
  final VoidCallback? onBackPressed;

  /// 右侧动作按钮
  final List<Widget>? actions;

  /// 标题下方内容
  final Widget? bottomContent;

  /// 大标题颜色
  final Color titleColor;

  /// 大标题字重
  final FontWeight largeTitleFontWeight;

  /// 小标题字重
  final FontWeight smallTitleFontWeight;

  /// 构造函数
  const AppleMusicSliverAppBar({
    Key? key,
    required this.title,
    this.expandedHeight = 120.0,
    this.useBlur = true,
    this.backgroundColor = Colors.black,
    this.largeTitleFontSize = 34.0,
    this.smallTitleFontSize = 17.0,
    this.onBackPressed,
    this.actions,
    this.bottomContent,
    this.titleColor = Colors.white,
    this.largeTitleFontWeight = FontWeight.w700,
    this.smallTitleFontWeight = FontWeight.w600,
  }) : super(key: key);

  @override
  State<AppleMusicSliverAppBar> createState() => _AppleMusicSliverAppBarState();
}

class _AppleMusicSliverAppBarState extends State<AppleMusicSliverAppBar> {
  late ScrollNotificationObserverState _scrollNotificationObserver;
  double _scrollOffset = 0.0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _scrollNotificationObserver = ScrollNotificationObserver.of(context)!;
    _scrollNotificationObserver.addListener(_onScrollNotification);
  }

  @override
  void dispose() {
    _scrollNotificationObserver.removeListener(_onScrollNotification);
    super.dispose();
  }

  void _onScrollNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      setState(() {
        _scrollOffset = notification.metrics.pixels;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      pinned: true,
      floating: false,
      stretch: true,
      backgroundColor: widget.backgroundColor,
      elevation: 0,
      expandedHeight: widget.expandedHeight,
      leading: null,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: _buildLeadingTitle(context),
      actions: widget.actions,
      flexibleSpace: _buildFlexibleSpace(context),
      bottom:
          widget.bottomContent != null
              ? PreferredSize(
                preferredSize: const Size.fromHeight(50.0),
                child: widget.bottomContent!,
              )
              : null,
    );
  }

  /// 构建顶部导航栏标题区域（含返回按钮和小标题）
  Widget _buildLeadingTitle(BuildContext context) {
    // 计算小标题的不透明度
    double smallTitleOpacity = (_scrollOffset / (widget.expandedHeight * 0.4))
        .clamp(0.0, 1.0);

    return Stack(
      children: [
        // 返回按钮
        if (widget.onBackPressed != null)
          Container(
            height: kToolbarHeight,
            alignment: Alignment.centerLeft,
            child: GestureDetector(
              onTap: widget.onBackPressed,
              child: Container(
                padding: const EdgeInsets.only(left: 16.0, right: 12.0),
                child: Icon(
                  Icons.arrow_back_ios,
                  color: widget.titleColor,
                  size: 22,
                ),
              ),
            ),
          ),

        // 中间小标题（初始透明，滚动后显示）
        Positioned.fill(
          child: Center(
            child: AnimatedOpacity(
              opacity: smallTitleOpacity,
              duration: const Duration(milliseconds: 200),
              child: Text(
                widget.title,
                style: TextStyle(
                  fontSize: widget.smallTitleFontSize,
                  fontWeight: widget.smallTitleFontWeight,
                  color: widget.titleColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建灵活空间（包含大标题和背景效果）
  Widget _buildFlexibleSpace(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // 计算滚动比例（0.0 - 1.0）
        double maxExtent = math.max(
          widget.expandedHeight,
          constraints.maxHeight,
        );
        double minExtent = kToolbarHeight;
        double ratio =
            (constraints.maxHeight - minExtent) / (maxExtent - minExtent);
        ratio = ratio.clamp(0.0, 1.0);

        // 计算大标题不透明度
        double largeTitleOpacity = ratio;

        // 计算大标题位置
        double titleLeftPadding = 16.0;
        double titleBottomPadding = 16.0;

        // 背景容器
        return Stack(
          children: [
            // 背景效果
            Positioned.fill(child: _buildBackground(ratio)),

            // 大标题文本
            Positioned(
              left: titleLeftPadding,
              bottom: titleBottomPadding,
              child: Opacity(
                opacity: largeTitleOpacity,
                child: Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: widget.largeTitleFontSize,
                    fontWeight: widget.largeTitleFontWeight,
                    color: widget.titleColor,
                    letterSpacing: -0.5,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建背景效果（支持毛玻璃效果）
  Widget _buildBackground(double ratio) {
    Widget background = Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            widget.backgroundColor,
            widget.backgroundColor.withOpacity(0.8),
          ],
        ),
      ),
    );

    if (widget.useBlur) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: background,
        ),
      );
    } else {
      return background;
    }
  }
}
