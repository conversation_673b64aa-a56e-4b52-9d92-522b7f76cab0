import 'dart:ui';
import 'package:flutter/material.dart';

class GradientCourseCard extends StatefulWidget {
  final String courseName;
  final String? description;
  final String? highlight;
  final VoidCallback? onTap;
  final double borderRadius;
  final double height;
  final int? categoryId;

  const GradientCourseCard({
    Key? key,
    required this.courseName,
    this.description,
    this.highlight,
    this.onTap,
    this.borderRadius = 18.0,
    this.height = 140.0,
    this.categoryId,
  }) : super(key: key);

  @override
  _GradientCourseCardState createState() => _GradientCourseCardState();
}

class _GradientCourseCardState extends State<GradientCourseCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  // 极光渐变配色系统 - 仿照极光的动态光效
  Map<String, dynamic> get gradientTheme {
    final List<Map<String, dynamic>> themes = [
      // 紫色极光系 - 经典紫色极光效果
      {
        'colors': [
          Color(0xFF000000), // 0% - 纯黑色（顶部）
          Color(0xFF0F0520), // 30% - 极深紫
          Color(0xFF1A0B3D), // 70% - 深紫过渡
          Color(0xFF2D1B69), // 100% - 紫色基底
        ],
        'auroraColors': [
          Color(0xFFBF5AF2), // 亮紫色极光
          Color(0xFFE91E63), // 粉紫色极光
          Color(0xFF9C27B0), // 深紫色极光
        ],
        'shadowColor': Color(0xFF1A0B3D),
        'glowColor': Color(0xFFBF5AF2).withOpacity(0.6),
        'highlightColor': Color(0xFFE91E63).withOpacity(0.8),
        'blurOverlay': Color(0xFF2D1B69),
      },
      // 蓝色极光系 - 冰蓝色极光效果
      {
        'colors': [
          Color(0xFF000000), // 0% - 纯黑色（顶部）
          Color(0xFF020A1F), // 30% - 极深蓝
          Color(0xFF0B1A3D), // 70% - 深蓝过渡
          Color(0xFF1B2D69), // 100% - 蓝色基底
        ],
        'auroraColors': [
          Color(0xFF00D4FF), // 亮青蓝极光
          Color(0xFF2196F3), // 蓝色极光
          Color(0xFF3F51B5), // 深蓝色极光
        ],
        'shadowColor': Color(0xFF0B1A3D),
        'glowColor': Color(0xFF00D4FF).withOpacity(0.6),
        'highlightColor': Color(0xFF2196F3).withOpacity(0.8),
        'blurOverlay': Color(0xFF1B2D69),
      },
      // 绿色极光系 - 翡翠绿极光效果
      {
        'colors': [
          Color(0xFF000000), // 0% - 纯黑色（顶部）
          Color(0xFF051F0A), // 30% - 极深绿
          Color(0xFF0B3D1A), // 70% - 深绿过渡
          Color(0xFF1B692D), // 100% - 绿色基底
        ],
        'auroraColors': [
          Color(0xFF00FF7F), // 亮绿色极光
          Color(0xFF4CAF50), // 绿色极光
          Color(0xFF388E3C), // 深绿色极光
        ],
        'shadowColor': Color(0xFF0B3D1A),
        'glowColor': Color(0xFF00FF7F).withOpacity(0.6),
        'highlightColor': Color(0xFF4CAF50).withOpacity(0.8),
        'blurOverlay': Color(0xFF1B692D),
      },
      // 橙色极光系 - 暖橙色极光效果
      {
        'colors': [
          Color(0xFF000000), // 0% - 纯黑色（顶部）
          Color(0xFF1F0F02), // 30% - 极深橙
          Color(0xFF3D1F0B), // 70% - 深橙过渡
          Color(0xFF69391B), // 100% - 橙色基底
        ],
        'auroraColors': [
          Color(0xFFFF6B35), // 亮橙色极光
          Color(0xFFFF9800), // 橙色极光
          Color(0xFFE65100), // 深橙色极光
        ],
        'shadowColor': Color(0xFF3D1F0B),
        'glowColor': Color(0xFFFF6B35).withOpacity(0.6),
        'highlightColor': Color(0xFFFF9800).withOpacity(0.8),
        'blurOverlay': Color(0xFF69391B),
      },
      // 粉色极光系 - 玫瑰粉极光效果
      {
        'colors': [
          Color(0xFF000000), // 0% - 纯黑色（顶部）
          Color(0xFF1F0514), // 30% - 极深粉
          Color(0xFF3D0B2A), // 70% - 深粉过渡
          Color(0xFF691B54), // 100% - 粉色基底
        ],
        'auroraColors': [
          Color(0xFFFF1493), // 亮粉色极光
          Color(0xFFE91E63), // 玫瑰粉极光
          Color(0xFFC2185B), // 深粉色极光
        ],
        'shadowColor': Color(0xFF3D0B2A),
        'glowColor': Color(0xFFFF1493).withOpacity(0.6),
        'highlightColor': Color(0xFFE91E63).withOpacity(0.8),
        'blurOverlay': Color(0xFF691B54),
      },
      // 青色极光系 - 青绿色极光效果
      {
        'colors': [
          Color(0xFF000000), // 0% - 纯黑色（顶部）
          Color(0xFF021F1A), // 30% - 极深青
          Color(0xFF0B3D35), // 70% - 深青过渡
          Color(0xFF1B6B5F), // 100% - 青色基底
        ],
        'auroraColors': [
          Color(0xFF00FFE0), // 亮青色极光
          Color(0xFF00BCD4), // 青色极光
          Color(0xFF0097A7), // 深青色极光
        ],
        'shadowColor': Color(0xFF0B3D35),
        'glowColor': Color(0xFF00FFE0).withOpacity(0.6),
        'highlightColor': Color(0xFF00BCD4).withOpacity(0.8),
        'blurOverlay': Color(0xFF1B6B5F),
      },
    ];

    // 根据课程名称或分类ID选择主题
    int index = 0;
    if (widget.categoryId != null) {
      index = widget.categoryId! % themes.length;
    } else {
      index = widget.courseName.hashCode.abs() % themes.length;
    }

    return themes[index];
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = gradientTheme;
    final colors = theme['colors'] as List<Color>;
    final auroraColors = theme['auroraColors'] as List<Color>;
    final shadowColor = theme['shadowColor'] as Color;
    final glowColor = theme['glowColor'] as Color;
    final highlightColor = theme['highlightColor'] as Color;
    final blurOverlay = theme['blurOverlay'] as Color;

    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) {
        _animationController.reverse();
        widget.onTap?.call();
      },
      onTapCancel: () => _animationController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                // 极光投影系统 - 神秘深邃的极光阴影
                boxShadow: [
                  // 1. 极光主投影 - 深邃神秘感
                  BoxShadow(
                    color: shadowColor.withOpacity(0.4), 
                    blurRadius: 25.0,
                    offset: const Offset(0, 12),
                    spreadRadius: -1,
                  ),
                  // 2. 极光发光投影 - 底部光晕
                  BoxShadow(
                    color: auroraColors[0].withOpacity(0.3), // 使用极光色
                    blurRadius: 20.0,
                    offset: const Offset(0, 8),
                    spreadRadius: -3,
                  ),
                  // 3. 次要极光投影
                  BoxShadow(
                    color: auroraColors[1].withOpacity(0.2),
                    blurRadius: 15.0,
                    offset: const Offset(0, 6),
                    spreadRadius: -4,
                  ),
                  // 4. 深度基础阴影
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3), // 增强深度感
                    blurRadius: 18.0,
                    offset: const Offset(0, 10),
                    spreadRadius: -2,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: Stack(
                  children: [
                    // 主渐变背景 - 精确的4断点渐变系统
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: colors,
                          stops: const [0.0, 0.30, 0.70, 1.0], // 光影弥散断点
                        ),
                      ),
                    ),
                    
                    // 柔和顶部光效 - 轻微内发光
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: const Alignment(0, -0.4), // 顶部光源
                            radius: 2.0,
                            colors: [
                              blurOverlay.withOpacity(0.08), // 大幅降低透明度
                              blurOverlay.withOpacity(0.05),
                              blurOverlay.withOpacity(0.02),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.4, 0.7, 1.0],
                          ),
                        ),
                      ),
                    ),
                    
                    // 柔和对角光效 - 轻微弥散
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.transparent,
                              blurOverlay.withOpacity(0.06), // 大幅降低透明度
                              blurOverlay.withOpacity(0.12),
                              blurOverlay.withOpacity(0.18),
                            ],
                            stops: const [0.0, 0.3, 0.7, 1.0], // 光影弥散断点
                          ),
                        ),
                      ),
                    ),
                    
                    // 顶部深沉过渡 - 强化顶部深色
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      height: widget.height * 0.3,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(0.4), // 强化顶部深色
                              Colors.black.withOpacity(0.2),
                              Colors.black.withOpacity(0.05),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.3, 0.7, 1.0], // 光影弥散断点
                          ),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(widget.borderRadius),
                            topRight: Radius.circular(widget.borderRadius),
                          ),
                        ),
                      ),
                    ),
                    
                    // 细腻边缘光效 - 轻微边界定义
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(widget.borderRadius),
                          border: Border.all(
                            color: highlightColor.withOpacity(0.1), // 更加细腻
                            width: 0.8,
                          ),
                        ),
                      ),
                    ),
                    
                    // 极光光带1 - 主要极光效果（中左位置）
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: widget.height * 0.5,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: const Alignment(-0.3, 1.2), // 中左底部
                            radius: 1.2,
                            colors: [
                              auroraColors[0].withOpacity(0.8), // 主极光色
                              auroraColors[0].withOpacity(0.4),
                              auroraColors[1].withOpacity(0.2),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.4, 0.7, 1.0],
                          ),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(widget.borderRadius),
                            bottomRight: Radius.circular(widget.borderRadius),
                          ),
                        ),
                      ),
                    ),
                    
                    // 极光光带2 - 次要极光效果（中右位置）
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: widget.height * 0.4,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: const Alignment(0.4, 1.1), // 中右底部
                            radius: 0.9,
                            colors: [
                              auroraColors[1].withOpacity(0.6), // 次极光色
                              auroraColors[2].withOpacity(0.3),
                              auroraColors[0].withOpacity(0.1),
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.5, 0.8, 1.0],
                          ),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(widget.borderRadius),
                            bottomRight: Radius.circular(widget.borderRadius),
                          ),
                        ),
                      ),
                    ),
                    
                    // 极光光带3 - 波浪状光效（左侧延伸）
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: widget.height * 0.35,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: const Alignment(-0.8, 0.9), // 左侧
                            radius: 1.5,
                            colors: [
                              auroraColors[2].withOpacity(0.4), // 第三极光色
                              auroraColors[1].withOpacity(0.2),
                              Colors.transparent,
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.3, 0.6, 1.0],
                          ),
                        ),
                      ),
                    ),
                    
                    // 极光光带4 - 右侧弥散光效
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: widget.height * 0.3,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            center: const Alignment(0.7, 0.8), // 右侧
                            radius: 1.0,
                            colors: [
                              auroraColors[0].withOpacity(0.3),
                              auroraColors[2].withOpacity(0.15),
                              Colors.transparent,
                              Colors.transparent,
                            ],
                            stops: const [0.0, 0.4, 0.7, 1.0],
                          ),
                        ),
                      ),
                    ),
                    
                    // 内容区域
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 18,
                        vertical: 16,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        widget.courseName,
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w700,
                                          color: Colors.white,
                                          shadows: [
                                            Shadow(
                                              color: shadowColor.withOpacity(0.3),
                                              offset: const Offset(0, 1),
                                              blurRadius: 3,
                                            ),
                                          ],
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.25),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: Colors.white.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Icon(
                                        Icons.arrow_forward,
                                        size: 20,
                                        color: Colors.white,
                                        shadows: [
                                          Shadow(
                                            color: shadowColor.withOpacity(0.5),
                                            offset: const Offset(0, 1),
                                            blurRadius: 2,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                if (widget.description != null) ...[
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 1,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.transparent,
                                          Colors.white.withOpacity(0.4),
                                          Colors.transparent,
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    widget.description!,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.95),
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500,
                                      shadows: [
                                        Shadow(
                                          color: shadowColor.withOpacity(0.2),
                                          offset: const Offset(0, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                                if (widget.highlight != null) ...[
                                  const SizedBox(height: 6),
                                  Text(
                                    widget.highlight!,
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                                const Spacer(),
                                Text(
                                  "点击卡片查看详情",
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.7),
                                    fontSize: 11,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}