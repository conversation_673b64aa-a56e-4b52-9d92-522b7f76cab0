import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';

/// 能量充值底部表单
class EnergyRechargeSheet {
  /// 显示能量充值底部表单
  static Future<void> show({
    required BuildContext context,
    required VoidCallback onWatchAd,
    required VoidCallback onSubscribe,
  }) async {
    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
          child: _EnergyRechargeSheetContent(
            onWatchAd: onWatchAd,
            onSubscribe: onSubscribe,
          ),
        );
      },
    );
  }
}

class _EnergyRechargeSheetContent extends StatelessWidget {
  final VoidCallback onWatchAd;
  final VoidCallback onSubscribe;

  const _EnergyRechargeSheetContent({
    required this.onWatchAd,
    required this.onSubscribe,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 24,
        right: 24,
        top: 16,
        bottom: 24 + MediaQuery.of(context).padding.bottom,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF121212),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 顶部拖动条
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
              margin: const EdgeInsets.only(bottom: 20),
            ),
          ),

          // 标题
          const Text(
            "Recharge Energy",
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // 副标题
          Text(
            "Choose a recharge method to continue",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // 观看广告选项
          _buildOption(
            title: "Watch Ad to Recharge",
            subtitle: "Watch a short video ad to get full energy",
            icon: Icons.play_circle_outline,
            onTap: () {
              Navigator.pop(context);
              onWatchAd();
            },
          ),
          const SizedBox(height: 16),

          // 订阅会员选项
          _buildOption(
            title: "Subscribe for Unlimited Energy",
            subtitle: "Monthly subscription: \$18/month",
            icon: Icons.star,
            onTap: () {
              Navigator.pop(context);
              onSubscribe();
            },
            isPremium: true,
          ),
          const SizedBox(height: 24),

          // 底部说明
          Text(
            "Ad viewing provides instant 15 energy points. Subscription removes all energy limitations.",
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 构建选项
  Widget _buildOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isPremium = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color:
              isPremium
                  ? Colors.amber.withOpacity(0.15)
                  : Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isPremium
                    ? Colors.amber.withOpacity(0.5)
                    : Colors.white.withOpacity(0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color:
                  isPremium
                      ? Colors.amber.withOpacity(0.1)
                      : Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color:
                    isPremium
                        ? Colors.amber.withOpacity(0.2)
                        : Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isPremium ? Colors.amber : Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isPremium ? Colors.amber : Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: isPremium ? Colors.amber : Colors.white.withOpacity(0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
