import 'package:flutter/material.dart';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import 'choice_option_widget.dart';
import 'fill_blank_widget.dart';
import 'short_answer_widget.dart';
import 'matching_widget.dart';
import 'ordering_widget.dart';
import 'programming_widget.dart';
// 使用现有的constants中的枚举定义

/// 答题区域工厂
/// 根据题目类型返回对应的答题组件
class AnswerAreaFactory {
  /// 根据题目类型创建对应的答题组件
  static Widget createAnswerAreaWidget({
    required Question question,
    required Function(dynamic) onAnswerSelected,
    dynamic selectedAnswer,
    QuestionInteractionState interactionState =
        QuestionInteractionState.waitingForSelection,
  }) {
    // 根据题目类型进行适当的类型转换
    switch (question.type) {
      case QuestionType.singleChoice:
      case QuestionType.trueFalse:
      case QuestionType.caseAnalysis:
        // 对于单选题，确保传递的是int?类型
        int? typedAnswer;
        if (selectedAnswer is int) {
          typedAnswer = selectedAnswer;
        } else if (selectedAnswer is List && selectedAnswer.isNotEmpty) {
          // 如果传入的是列表，尝试获取第一个元素
          if (selectedAnswer[0] is int) {
            typedAnswer = selectedAnswer[0];
          }
        }

        if (question.type == QuestionType.singleChoice) {
          return _buildSingleChoiceWidget(
            question,
            onAnswerSelected,
            typedAnswer,
            interactionState,
          );
        } else if (question.type == QuestionType.trueFalse) {
          return _buildTrueFalseWidget(
            question,
            onAnswerSelected,
            typedAnswer,
            interactionState,
          );
        } else {
          return _buildCaseAnalysisWidget(
            question,
            onAnswerSelected,
            typedAnswer,
            interactionState,
          );
        }

      case QuestionType.multipleChoice:
        // 对于多选题，确保传递的是List<int>类型
        return _buildMultipleChoiceWidget(
          question,
          onAnswerSelected,
          selectedAnswer, // 已有类型处理逻辑
          interactionState,
        );

      case QuestionType.fillBlank:
      case QuestionType.calculation:
        // 填空题和计算题需要String?类型
        String? typedAnswer;
        if (selectedAnswer is String) {
          typedAnswer = selectedAnswer;
        } else if (selectedAnswer != null) {
          typedAnswer = selectedAnswer.toString();
        }

        return _buildFillBlankWidget(
          question,
          onAnswerSelected,
          typedAnswer,
          interactionState,
        );

      case QuestionType.shortAnswer:
        // 简答题需要String?类型
        String? typedAnswer;
        if (selectedAnswer is String) {
          typedAnswer = selectedAnswer;
        } else if (selectedAnswer != null) {
          typedAnswer = selectedAnswer.toString();
        }

        return _buildShortAnswerWidget(
          question,
          onAnswerSelected,
          typedAnswer,
          interactionState,
        );

      case QuestionType.matching:
        // 匹配题需要Map<String,String>?类型
        Map<String, String>? typedAnswer;
        if (selectedAnswer is Map<String, String>) {
          typedAnswer = selectedAnswer;
        } else if (selectedAnswer is Map) {
          typedAnswer = {};
          selectedAnswer.forEach((key, value) {
            if (key is String && value is String) {
              typedAnswer![key] = value;
            }
          });
        }

        return _buildMatchingWidget(
          question,
          onAnswerSelected,
          typedAnswer ?? <String, String>{},
          interactionState,
        );

      case QuestionType.ordering:
        // 排序题需要List<OrderingItem>?类型
        return _buildOrderingWidget(
          question,
          onAnswerSelected,
          selectedAnswer is List<OrderingItem>
              ? selectedAnswer
              : question.orderingItems,
          interactionState,
        );

      case QuestionType.programming:
        // 编程题需要String?类型
        String? typedAnswer;
        if (selectedAnswer is String) {
          typedAnswer = selectedAnswer;
        } else if (selectedAnswer != null) {
          typedAnswer = selectedAnswer.toString();
        }

        return _buildProgrammingWidget(
          question,
          onAnswerSelected,
          typedAnswer,
          interactionState,
        );

      default:
        return const Center(
          child: Text("不支持的题型", style: TextStyle(color: Colors.white)),
        );
    }
  }

  /// 构建单选题组件
  static Widget _buildSingleChoiceWidget(
    Question question,
    Function(int?) onAnswerSelected,
    int? selectedOptionId,
    QuestionInteractionState interactionState,
  ) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: question.options.length,
      itemBuilder: (context, index) {
        final option = question.options[index];
        final isSelected = selectedOptionId == option.id;
        final isCorrect = option.isCorrect;

        // 不同状态下展示不同的选项样式
        bool showCorrect = false;
        bool showIncorrect = false;

        if (interactionState == QuestionInteractionState.answerCorrect) {
          showCorrect = isSelected && isCorrect; // 选中且正确
        } else if (interactionState ==
            QuestionInteractionState.answerIncorrect) {
          showCorrect = isCorrect; // 显示正确答案
          showIncorrect = isSelected && !isCorrect; // 选中但错误
        }

        return ChoiceOptionWidget(
          optionText: option.content,
          isSelected: isSelected,
          showCorrect: showCorrect,
          showIncorrect: showIncorrect,
          onTap:
              interactionState == QuestionInteractionState.waitingForSelection
                  ? () => onAnswerSelected(option.id)
                  : null, // 仅在等待选择状态允许点击
        );
      },
    );
  }

  /// 构建多选题组件
  static Widget _buildMultipleChoiceWidget(
    Question question,
    Function(List<int>) onAnswerSelected,
    dynamic selectedOptionIds,
    QuestionInteractionState interactionState,
  ) {
    // 处理类型转换，确保无论传入什么类型都能正确处理
    final List<int> selectedIds = [];
    if (selectedOptionIds != null) {
      if (selectedOptionIds is List<int>) {
        selectedIds.addAll(selectedOptionIds);
      } else if (selectedOptionIds is List) {
        for (var id in selectedOptionIds) {
          if (id is int) {
            selectedIds.add(id);
          }
        }
      } else if (selectedOptionIds is int) {
        // 处理单个整数值的情况
        selectedIds.add(selectedOptionIds);
      }
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: question.options.length,
      itemBuilder: (context, index) {
        final option = question.options[index];
        final isSelected = selectedIds.contains(option.id);
        final isCorrect = option.isCorrect;

        // 不同状态下展示不同的选项样式
        bool showCorrect = false;
        bool showIncorrect = false;

        if (interactionState != QuestionInteractionState.waitingForSelection) {
          showCorrect = isCorrect; // 显示所有正确答案
          showIncorrect = isSelected && !isCorrect; // 选中但错误
        }

        return ChoiceOptionWidget(
          optionText: option.content,
          isSelected: isSelected,
          showCorrect: showCorrect,
          showIncorrect: showIncorrect,
          isCheckbox: true, // 多选题使用复选框样式
          onTap:
              interactionState == QuestionInteractionState.waitingForSelection
                  ? () {
                    // 切换选中状态
                    final newSelectedIds = List<int>.from(selectedIds);
                    if (isSelected) {
                      newSelectedIds.remove(option.id);
                    } else {
                      newSelectedIds.add(option.id);
                    }
                    onAnswerSelected(newSelectedIds);
                  }
                  : null, // 仅在等待选择状态允许点击
        );
      },
    );
  }

  /// 构建判断题组件
  static Widget _buildTrueFalseWidget(
    Question question,
    Function(int?) onAnswerSelected,
    int? selectedOptionId,
    QuestionInteractionState interactionState,
  ) {
    // 判断题的UI与单选题相似，但样式可能有所不同
    return _buildSingleChoiceWidget(
      question,
      onAnswerSelected,
      selectedOptionId,
      interactionState,
    );
  }

  /// 构建填空题组件
  static Widget _buildFillBlankWidget(
    Question question,
    Function(String?) onAnswerSelected,
    String? userAnswer,
    QuestionInteractionState interactionState,
  ) {
    // 由于尚未创建填空题组件，先返回占位符
    return FillBlankWidget(
      question: question,
      userAnswer: userAnswer,
      onAnswerChanged: onAnswerSelected,
      interactionState: interactionState,
    );
  }

  /// 构建简答题组件
  static Widget _buildShortAnswerWidget(
    Question question,
    Function(String?) onAnswerSelected,
    String? userAnswer,
    QuestionInteractionState interactionState,
  ) {
    // 由于尚未创建简答题组件，先返回占位符
    return ShortAnswerWidget(
      question: question,
      userAnswer: userAnswer,
      onAnswerChanged: onAnswerSelected,
      interactionState: interactionState,
    );
  }

  /// 构建匹配题组件
  static Widget _buildMatchingWidget(
    Question question,
    Function(Map<String, String>) onAnswerSelected,
    Map<String, String>? userAnswers,
    QuestionInteractionState interactionState,
  ) {
    // 由于尚未创建匹配题组件，先返回占位符
    return MatchingWidget(
      question: question,
      userAnswers: userAnswers,
      onAnswerChanged: onAnswerSelected,
      interactionState: interactionState,
    );
  }

  /// 构建排序题组件
  static Widget _buildOrderingWidget(
    Question question,
    Function(List<OrderingItem>) onAnswerSelected,
    List<OrderingItem>? userOrder,
    QuestionInteractionState interactionState,
  ) {
    // 由于尚未创建排序题组件，先返回占位符
    return OrderingWidget(
      question: question,
      userOrder: userOrder,
      onAnswerChanged: onAnswerSelected,
      interactionState: interactionState,
    );
  }

  /// 构建编程题组件
  static Widget _buildProgrammingWidget(
    Question question,
    Function(String?) onAnswerSelected,
    String? userCode,
    QuestionInteractionState interactionState,
  ) {
    // 由于尚未创建编程题组件，先返回占位符
    return ProgrammingWidget(
      question: question,
      userCode: userCode,
      onAnswerChanged: onAnswerSelected,
      interactionState: interactionState,
    );
  }

  /// 构建计算题组件
  static Widget _buildCalculationWidget(
    Question question,
    Function(String?) onAnswerSelected,
    String? userAnswer,
    QuestionInteractionState interactionState,
  ) {
    // 计算题使用与填空题类似的UI，但只允许输入数字
    return FillBlankWidget(
      question: question,
      userAnswer: userAnswer,
      onAnswerChanged: onAnswerSelected,
      interactionState: interactionState,
      keyboardType: TextInputType.number,
    );
  }

  /// 构建案例分析题组件
  static Widget _buildCaseAnalysisWidget(
    Question question,
    Function(int?) onAnswerSelected,
    int? selectedOptionId,
    QuestionInteractionState interactionState,
  ) {
    // 案例分析题的选项部分与单选题相似，直接使用单选题选项部分，不再显示案例内容
    return _buildSingleChoiceWidget(
      question,
      onAnswerSelected,
      selectedOptionId,
      interactionState,
    );
  }
}
