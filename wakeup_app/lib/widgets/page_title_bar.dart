import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:io';
import '../constants/fonts.dart';
import 'animated_avatar_widget.dart';

/// 统一页面标题栏组件
///
/// 包含页面大标题和动画头像按钮
class PageTitleBar extends StatelessWidget {
  /// 标题文本
  final String title;

  /// 副标题文本（可选）
  final String? subtitle;

  /// 是否显示头像按钮
  final bool showAvatar;

  /// 页面标题栏的高度
  final double height;

  /// 标题文本的大小
  final double titleFontSize;

  /// 左侧内边距
  final double leftPadding;

  /// 右侧内边距
  final double rightPadding;

  /// 标题栏背景是否透明
  final bool transparent;

  /// 是否为英文标题
  final bool isLatinTitle;

  const PageTitleBar({
    Key? key,
    required this.title,
    this.subtitle,
    this.showAvatar = true,
    this.height = 70,
    this.titleFontSize = 32,
    this.leftPadding = 20,
    this.rightPadding = 16,
    this.transparent = true,
    this.isLatinTitle = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: EdgeInsets.only(
        left: leftPadding,
        right: rightPadding,
        top: 10,
        bottom: 10,
      ),
      decoration: BoxDecoration(
        color: transparent ? Colors.transparent : Colors.black,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 标题部分
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 主标题 - 根据平台和语言选择不同的标题渲染方式
                Align(
                  alignment: Alignment.centerLeft,
                  child:
                      (Platform.isIOS || Platform.isMacOS) && isLatinTitle
                          ? AppFonts.createBoldTextStack(
                            title,
                            fontSize: titleFontSize,
                            letterSpacing: -0.5,
                          )
                          : Text(
                            title,
                            style: AppFonts.createTitleStyle(
                              fontSize: titleFontSize,
                              isLatinText: isLatinTitle,
                            ),
                          ),
                ),
                // 副标题（如果有）
                if (subtitle != null)
                  Text(
                    subtitle!,
                    style: AppFonts.createMixedStyle(
                      fontSize: titleFontSize / 2,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
              ],
            ),
          ),

          // 头像按钮
          if (showAvatar) const AnimatedAvatarWidget(size: 36),
        ],
      ),
    );
  }
}
