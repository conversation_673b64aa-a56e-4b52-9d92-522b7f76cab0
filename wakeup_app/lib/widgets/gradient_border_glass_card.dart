import 'dart:ui';
import 'package:flutter/material.dart';

class GradientBorderGlassCard extends StatefulWidget {
  final String text;
  final double borderRadius;
  final double borderWidth;
  final double height;

  const GradientBorderGlassCard({
    Key? key,
    required this.text,
    this.borderRadius = 24.0,
    this.borderWidth = 3.0,
    this.height = 200,
  }) : super(key: key);

  @override
  _GradientBorderGlassCardState createState() =>
      _GradientBorderGlassCardState();
}

class _GradientBorderGlassCardState extends State<GradientBorderGlassCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  final List<Color> gradientColors = [
    Color(0xFF8A00FF),
    Color(0xFFCB3EFF),
    Color(0xFFFF5C93),
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: 0, end: 1).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: _GradientBorderPainter(
            animationValue: _animation.value,
            borderRadius: widget.borderRadius,
            borderWidth: widget.borderWidth,
            gradientColors: gradientColors,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                height: widget.height,
                width: double.infinity,
                padding: EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                child: Center(
                  child: Text(
                    widget.text,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _GradientBorderPainter extends CustomPainter {
  final double animationValue;
  final double borderRadius;
  final double borderWidth;
  final List<Color> gradientColors;

  _GradientBorderPainter({
    required this.animationValue,
    required this.borderRadius,
    required this.borderWidth,
    required this.gradientColors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    final gradient = LinearGradient(
      colors: gradientColors,
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      transform: GradientRotation(animationValue * 2 * 3.1416),
    );

    final paint =
        Paint()
          ..shader = gradient.createShader(rect)
          ..style = PaintingStyle.stroke
          ..strokeWidth = borderWidth;

    final rRect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));
    canvas.drawRRect(rRect, paint);
  }

  @override
  bool shouldRepaint(covariant _GradientBorderPainter oldDelegate) =>
      animationValue != oldDelegate.animationValue;
}
