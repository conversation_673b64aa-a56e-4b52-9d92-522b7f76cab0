import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// 气泡类型
enum BubbleType {
  ai, // AI消息
  user, // 用户消息
  question, // 题目
  answer, // 答案
  explanation, // 解析
  encouragement, // 鼓励
  system, // 系统消息
}

/// 聊天气泡组件
class ChatBubble extends StatelessWidget {
  final String message;
  final BubbleType type;
  final bool isTyping;
  final List<String>? options;
  final Function(String)? onOptionSelected;

  const ChatBubble({
    super.key,
    required this.message,
    required this.type,
    this.isTyping = false,
    this.options,
    this.onOptionSelected,
  });

  @override
  Widget build(BuildContext context) {
    // 判断当前主题模式
    final isDarkMode =
        MediaQuery.of(context).platformBrightness == Brightness.dark;

    // 气泡的方向（左/右）
    final isUserMessage = type == BubbleType.user;

    // 消息内容的颜色
    Color textColor;
    switch (type) {
      case BubbleType.user:
        textColor = Colors.white;
        break;
      case BubbleType.question:
        textColor = Colors.white;
        break;
      case BubbleType.encouragement:
        textColor = Colors.white;
        break;
      default:
        textColor = isDarkMode ? Colors.white : Colors.black87;
    }

    // 气泡的主体部分
    Widget bubbleContent = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 消息文本
        Text(
          message,
          style: TextStyle(color: textColor, fontSize: 16, height: 1.3),
        ),

        // 如果有打字动画，显示光标
        if (isTyping)
          Container(
            margin: const EdgeInsets.only(top: 4),
            height: 16,
            width: 2,
            color: textColor.withOpacity(0.7),
          ),

        // 如果有选项（用于选择题），显示选项按钮
        if (options != null && options!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children:
                  options!.map((option) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: CupertinoButton(
                        padding: EdgeInsets.zero,
                        minSize: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isDarkMode
                                    ? CupertinoColors.systemGrey6.darkColor
                                        .withOpacity(0.8)
                                    : CupertinoColors.systemGrey6.withOpacity(
                                      0.8,
                                    ),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color:
                                  isDarkMode
                                      ? Colors.white.withOpacity(0.2)
                                      : Colors.black.withOpacity(0.1),
                              width: 0.5,
                            ),
                          ),
                          child: Text(
                            option,
                            style: TextStyle(
                              color: isDarkMode ? Colors.white : Colors.black87,
                              fontSize: 15,
                            ),
                          ),
                        ),
                        onPressed: () {
                          if (onOptionSelected != null) {
                            onOptionSelected!(option);
                          }
                        },
                      ),
                    );
                  }).toList(),
            ),
          ),
      ],
    );

    // 气泡容器的装饰和颜色
    BoxDecoration bubbleDecoration;

    switch (type) {
      case BubbleType.user:
        // 用户消息：蓝紫色渐变背景
        bubbleDecoration = BoxDecoration(
          gradient: LinearGradient(
            colors: [CupertinoColors.systemIndigo, CupertinoColors.systemBlue],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(
            18,
          ).copyWith(bottomRight: const Radius.circular(5)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        );
        break;

      case BubbleType.question:
        // 题目：深色背景
        bubbleDecoration = BoxDecoration(
          color: isDarkMode ? const Color(0xFF1C1C1E) : const Color(0xFF2C2C2E),
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        );
        break;

      case BubbleType.explanation:
        // 解析：浅蓝色背景
        bubbleDecoration = BoxDecoration(
          color: isDarkMode ? const Color(0xFF0A3A5A) : const Color(0xFFD1E8FA),
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        );
        break;

      case BubbleType.encouragement:
        // 鼓励消息：绿色渐变背景
        bubbleDecoration = BoxDecoration(
          gradient: LinearGradient(
            colors: [CupertinoColors.systemGreen, CupertinoColors.activeGreen],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        );
        break;

      case BubbleType.system:
        // 系统消息：居中的半透明背景
        bubbleDecoration = BoxDecoration(
          color:
              isDarkMode
                  ? Colors.white.withOpacity(0.15)
                  : Colors.black.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
        );
        break;

      default:
        // AI消息和其他：毛玻璃效果
        bubbleDecoration = BoxDecoration(
          color:
              isDarkMode
                  ? Colors.white.withOpacity(0.12)
                  : Colors.black.withOpacity(0.05),
          borderRadius: BorderRadius.circular(
            18,
          ).copyWith(bottomLeft: const Radius.circular(5)),
        );
    }

    // 系统消息居中显示
    if (type == BubbleType.system) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            decoration: bubbleDecoration,
            child: Text(
              message,
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.black54,
                fontSize: 14,
              ),
            ),
          ),
        ),
      );
    }

    // 其他气泡正常左右排列显示
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            isUserMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 气泡主体
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              margin: const EdgeInsets.symmetric(vertical: 4),
              padding: const EdgeInsets.all(12),
              decoration: bubbleDecoration,
              child:
                  type == BubbleType.ai || type == BubbleType.explanation
                      ? ClipRRect(
                        borderRadius: BorderRadius.circular(14),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                          child: bubbleContent,
                        ),
                      )
                      : bubbleContent,
            ),
          ),
        ],
      ),
    );
  }
}
