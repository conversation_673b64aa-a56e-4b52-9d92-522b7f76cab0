import 'package:flutter/material.dart';

/// 一体化返回按钮组件
class BackButtonWidget extends StatefulWidget {
  final String text;
  final Color color;
  final VoidCallback onPressed;

  const BackButtonWidget({
    super.key,
    required this.text,
    required this.color,
    required this.onPressed,
  });

  @override
  State<BackButtonWidget> createState() => _BackButtonWidgetState();
}

class _BackButtonWidgetState extends State<BackButtonWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 适配深色模式
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final arrowColor = isDarkMode ? widget.color : widget.color;

    // 是否显示文本
    final bool showText = widget.text.isNotEmpty;

    return GestureDetector(
      onTap: widget.onPressed,
      onTapDown: (_) {
        setState(() {
          _isPressed = true;
        });
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() {
          _isPressed = false;
        });
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final scale = 1.0 - (_animationController.value * 0.05);
          return Transform.scale(
            scale: scale,
            child: Container(
              width: showText ? null : 30.0, // 有文本时自适应宽度
              height: 30.0, // 固定高度
              padding: EdgeInsets.symmetric(
                horizontal: showText ? 8.0 : 5.0,
                vertical: 5.0,
              ),
              decoration: BoxDecoration(
                color:
                    _isPressed
                        ? arrowColor.withOpacity(0.1)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(15.0),
              ),
              child:
                  showText
                      ? Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomPaint(
                            size: const Size(18, 18),
                            painter: BackArrowPainter(
                              color: arrowColor,
                              strokeWidth: 2.5,
                            ),
                          ),
                          const SizedBox(width: 4.0),
                          Text(
                            widget.text,
                            style: TextStyle(
                              fontSize: 16, // 减小字体大小
                              fontWeight: FontWeight.w600,
                              color: arrowColor,
                              letterSpacing: -0.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.5),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      )
                      : Center(
                        child: CustomPaint(
                          size: const Size(18, 18),
                          painter: BackArrowPainter(
                            color: arrowColor,
                            strokeWidth: 2.5,
                          ),
                        ),
                      ),
            ),
          );
        },
      ),
    );
  }
}

/// 自定义绘制返回箭头
class BackArrowPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;

  BackArrowPainter({required this.color, this.strokeWidth = 2.5});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.round
          ..strokeJoin = StrokeJoin.round
          ..style = PaintingStyle.stroke;

    final path = Path();
    // 从右向左画一个短而紧凑的箭头
    path.moveTo(size.width * 0.7, size.height * 0.2); // 起点在右上
    path.lineTo(size.width * 0.3, size.height * 0.5); // 到中间点
    path.lineTo(size.width * 0.7, size.height * 0.8); // 到右下

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
