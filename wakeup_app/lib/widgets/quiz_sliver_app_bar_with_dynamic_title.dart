import 'dart:ui';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../constants/fonts.dart';

/// Apple Music风格的动态收缩标题AppBar
///
/// 支持大标题随滚动缩小并渐变透明，同时小标题渐变显示
class QuizSliverAppBarWithDynamicTitle extends StatefulWidget {
  /// 标题文本
  final String title;

  /// 展开高度，决定大标题区域大小
  final double expandedHeight;

  /// 是否使用毛玻璃效果
  final bool useBlur;

  /// 背景颜色
  final Color backgroundColor;

  /// 大标题字体大小
  final double largeTitleFontSize;

  /// 小标题字体大小
  final double smallTitleFontSize;

  /// 返回按钮点击回调
  final VoidCallback? onBackPressed;

  /// 菜单按钮点击回调
  final VoidCallback? onMenuPressed;

  /// 构造函数
  const QuizSliverAppBarWithDynamicTitle({
    Key? key,
    required this.title,
    this.expandedHeight = 200.0,
    this.useBlur = true,
    this.backgroundColor = Colors.transparent,
    this.largeTitleFontSize = 36.0,
    this.smallTitleFontSize = 20.0,
    this.onBackPressed,
    this.onMenuPressed,
  }) : super(key: key);

  @override
  State<QuizSliverAppBarWithDynamicTitle> createState() =>
      _QuizSliverAppBarWithDynamicTitleState();
}

class _QuizSliverAppBarWithDynamicTitleState
    extends State<QuizSliverAppBarWithDynamicTitle> {
  late ScrollNotificationObserverState _scrollNotificationObserver;
  double _scrollOffset = 0.0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _scrollNotificationObserver = ScrollNotificationObserver.of(context)!;
    _scrollNotificationObserver.addListener(_onScrollNotification);
  }

  @override
  void dispose() {
    _scrollNotificationObserver.removeListener(_onScrollNotification);
    super.dispose();
  }

  void _onScrollNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      setState(() {
        _scrollOffset = notification.metrics.pixels;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      pinned: true,
      floating: false,
      snap: false,
      stretch: true,
      backgroundColor: widget.backgroundColor,
      elevation: 0,
      expandedHeight: widget.expandedHeight,
      leading: null,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: _buildLeadingTitle(context),
      actions: [
        IconButton(
          padding: EdgeInsets.zero,
          icon: const Icon(
            CupertinoIcons.line_horizontal_3,
            color: Colors.white,
            size: 22,
          ),
          onPressed: widget.onMenuPressed,
        ),
      ],
      flexibleSpace: _buildFlexibleSpace(context),
    );
  }

  /// 构建顶部导航栏标题区域（含返回按钮和小标题）
  Widget _buildLeadingTitle(BuildContext context) {
    // 计算大标题被AppBar完全遮盖的临界高度
    final double thresholdOffset =
        widget.expandedHeight - kToolbarHeight - 30; // 30为大标题bottom padding

    // 计算小标题的不透明度 (0.0-1.0)
    double smallTitleOpacity = (_scrollOffset / thresholdOffset).clamp(
      0.0,
      1.0,
    );

    return Stack(
      children: [
        // 返回按钮
        Container(
          height: kToolbarHeight,
          alignment: Alignment.centerLeft,
          child: GestureDetector(
            onTap: widget.onBackPressed,
            child: Container(
              padding: const EdgeInsets.only(left: 16.0, right: 12.0),
              margin: const EdgeInsets.only(top: 1.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    CupertinoIcons.back,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 6.0),
                  AnimatedDefaultTextStyle(
                    duration: const Duration(milliseconds: 150),
                    style: _getTextStyle(context, widget.smallTitleFontSize),
                    child: const Text("返回"),
                  ),
                ],
              ),
            ),
          ),
        ),

        // 中间小标题（初始透明，滚动后显示）
        Positioned.fill(
          child: Center(
            child: AnimatedOpacity(
              opacity: smallTitleOpacity,
              duration: const Duration(milliseconds: 200),
              child: Text(
                widget.title,
                style: _getTextStyle(context, widget.smallTitleFontSize),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建灵活空间（包含大标题和背景效果）
  Widget _buildFlexibleSpace(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // 计算滚动比例（0.0 - 1.0）
        double maxExtent = widget.expandedHeight;
        double minExtent = kToolbarHeight;
        double ratio =
            (constraints.maxHeight - minExtent) / (maxExtent - minExtent);
        ratio = ratio.clamp(0.0, 1.0);

        // 计算大标题不透明度
        double largeTitleOpacity = ratio;

        // 计算大标题位置
        double titleOffset = 16.0 + (ratio * 8.0); // 水平位置微调

        // 背景容器
        return Stack(
          children: [
            // 背景效果
            Positioned.fill(child: _buildBackground(ratio)),

            // 大标题文本
            Positioned(
              left: titleOffset,
              bottom: 16.0 + (ratio * 14.0), // 底部位置随滚动微调
              child: Opacity(
                opacity: largeTitleOpacity, // 透明度随滚动变化
                child: Text(
                  widget.title,
                  style: _getLargeTitleStyle(
                    context,
                    widget.largeTitleFontSize,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 构建背景效果（支持毛玻璃效果）
  Widget _buildBackground(double ratio) {
    // 背景透明度随滚动变化，强化视觉效果
    final double backgroundOpacity = (1.0 - ratio * 0.7).clamp(0.3, 1.0);

    Widget background = Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            widget.backgroundColor.withOpacity(backgroundOpacity),
            widget.backgroundColor.withOpacity(backgroundOpacity * 0.8),
          ],
        ),
      ),
    );

    if (widget.useBlur) {
      return ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 10 * (1.0 - ratio * 0.5), // 滚动时减少模糊效果
            sigmaY: 10 * (1.0 - ratio * 0.5),
          ),
          child: background,
        ),
      );
    } else {
      return background;
    }
  }

  /// 获取标题文本样式，根据系统平台选择合适的字体
  TextStyle _getTextStyle(BuildContext context, double fontSize) {
    return Platform.isIOS &&
            Localizations.localeOf(context).languageCode != 'zh'
        ? TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: -0.5,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.5),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        )
        : TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: -0.5,
          fontFamily: AppFonts.platformLatinDisplayFont,
        );
  }

  /// 获取大标题样式，适用于展开状态
  TextStyle _getLargeTitleStyle(BuildContext context, double fontSize) {
    return Platform.isIOS &&
            Localizations.localeOf(context).languageCode != 'zh'
        ? TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w700, // 大标题使用更粗的字重
          color: Colors.white,
          letterSpacing: -0.5,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.5),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        )
        : TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.w700,
          color: Colors.white,
          letterSpacing: -0.5,
          fontFamily: AppFonts.platformLatinDisplayFont,
        );
  }
}
