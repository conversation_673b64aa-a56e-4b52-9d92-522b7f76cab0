import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import 'dart:io';

class EnergyModal extends StatelessWidget {
  final int energyValue;
  final VoidCallback onWatchAd;
  final VoidCallback onClose;

  const EnergyModal({
    Key? key,
    required this.energyValue,
    required this.onWatchAd,
    required this.onClose,
  }) : super(key: key);

  // 显示能量弹窗的静态方法
  static void show({
    required BuildContext context,
    required int energyValue,
    required VoidCallback onWatchAd,
  }) {
    showCupertinoModalPopup(
      context: context,
      barrierDismissible: true,
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      builder: (BuildContext context) {
        return EnergyModal(
          energyValue: energyValue,
          onWatchAd: onWatchAd,
          onClose: () => Navigator.of(context).pop(),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool isEnergyLow = energyValue <= 0;
    final DateTime nextResetTime = _getNextResetTime();
    final String formattedTime = _formatTime(nextResetTime);

    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: CupertinoPopupSurface(
        isSurfacePainted: true,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width * 0.85,
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: CupertinoTheme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(14),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                '能量状态',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: CupertinoTheme.of(context).textTheme.textStyle.color,
                ),
              ),
              const SizedBox(height: 16),

              // 能量图标
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      isEnergyLow
                          ? Colors.red.withOpacity(0.1)
                          : Colors.blue.withOpacity(0.1),
                ),
                child: Center(
                  child: Image.asset(
                    'assets/animations/default_avatar/avatar_animation.webp',
                    width: 60,
                    height: 60,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 能量值
              Text(
                '$energyValue',
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color:
                      isEnergyLow
                          ? Colors.red
                          : CupertinoTheme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 8),

              // 能量状态描述
              Text(
                isEnergyLow ? '能量不足，无法继续答题' : '当前剩余能量',
                style: TextStyle(
                  fontSize: 16,
                  color:
                      isEnergyLow
                          ? Colors.red
                          : CupertinoTheme.of(
                            context,
                          ).textTheme.textStyle.color?.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 16),

              // 分隔线
              Container(
                height: 1,
                color: CupertinoColors.systemGrey5,
                margin: const EdgeInsets.symmetric(vertical: 8),
              ),

              // 下次重置时间
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      CupertinoIcons.refresh,
                      size: 16,
                      color: CupertinoTheme.of(
                        context,
                      ).textTheme.textStyle.color?.withOpacity(0.5),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '下次刷新：$formattedTime',
                      style: TextStyle(
                        fontSize: 14,
                        color: CupertinoTheme.of(
                          context,
                        ).textTheme.textStyle.color?.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 广告按钮
              SizedBox(
                width: double.infinity,
                child: CupertinoButton(
                  color:
                      isEnergyLow
                          ? CupertinoColors.systemRed
                          : CupertinoTheme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(10),
                  onPressed: onWatchAd,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        CupertinoIcons.play_circle,
                        color: CupertinoColors.white,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '观看广告补充能量',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: CupertinoColors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // 关闭按钮
              CupertinoButton(
                onPressed: onClose,
                child: Text(
                  '关闭',
                  style: TextStyle(
                    fontSize: 16,
                    color: CupertinoTheme.of(
                      context,
                    ).textTheme.textStyle.color?.withOpacity(0.7),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 获取下一个重置时间（每日0点）
  DateTime _getNextResetTime() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day + 1);
  }

  // 格式化时间
  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
