import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:math' as math;

/// 能量耗尽对话框
class EnergyDialog {
  /// 显示能量耗尽对话框
  static Future<void> show({
    required BuildContext context,
    required VoidCallback onContinueChallenge,
  }) async {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.black,
      barrierColor: Colors.black,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: _EnergyDialogContent(onContinueChallenge: onContinueChallenge),
        );
      },
    );
  }
}

class _EnergyDialogContent extends StatelessWidget {
  final VoidCallback onContinueChallenge;

  const _EnergyDialogContent({required this.onContinueChallenge});

  @override
  Widget build(BuildContext context) {
    final maxHeight = MediaQuery.of(context).size.height * 0.8;
    return Center(
      child: Stack(
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 400, maxHeight: maxHeight),
            child: Container(
              width: 360,
              height: maxHeight,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              decoration: BoxDecoration(
                color: Colors.black, // 保持纯黑色不透明
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 30,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // 顶部大图标
                          Container(
                            width: 80,
                            height: 80,
                            child: CustomPaint(painter: _RoundedStarPainter()),
                          ),
                          const SizedBox(height: 28),
                          // 主标题
                          const Text(
                            "Energy Depleted",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                              height: 1.2,
                            ),
                          ),
                          const SizedBox(height: 10),
                          // 副标题
                          const Text(
                            "Recharge to continue your challenge",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 15,
                              color: Colors.white70,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 28),
                          // 权益/规则列表
                          _buildFeatureList(),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                  // 操作按钮区域和订阅价格文本固定在底部
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          // 观看广告充能按钮
                          Flexible(
                            fit: FlexFit.loose,
                            child: SizedBox(
                              height: 48,
                              child: OutlinedButton(
                                onPressed: () {
                                  // TODO: 实现观看广告充能逻辑
                                },
                                style: OutlinedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  foregroundColor: Colors.black,
                                  side: BorderSide.none,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                  textStyle: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                child: const Text("观看广告充能"),
                              ),
                            ),
                          ),
                          const SizedBox(width: 18),
                          // 订阅会员免广告按钮
                          Flexible(
                            fit: FlexFit.loose,
                            child: SizedBox(
                              height: 48,
                              child: ElevatedButton(
                                onPressed: () {
                                  // TODO: 实现订阅会员逻辑
                                },
                                style: ElevatedButton.styleFrom(
                                  elevation: 0,
                                  padding: EdgeInsets.zero,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                ).copyWith(
                                  backgroundColor:
                                      MaterialStateProperty.resolveWith<Color>((
                                        states,
                                      ) {
                                        return Colors.transparent;
                                      }),
                                  foregroundColor: MaterialStateProperty.all(
                                    Colors.white,
                                  ),
                                  shadowColor: MaterialStateProperty.all(
                                    Colors.transparent,
                                  ),
                                ),
                                child: Ink(
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFF5B7CFA),
                                        Color(0xFF8F5CFF),
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    ),
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                  child: Container(
                                    alignment: Alignment.center,
                                    height: 48,
                                    child: const Text(
                                      "订阅会员免广告",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      const Text(
                        "自动续订价格：¥8.9/月，直到您取消为止。",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 18),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // 右上角关闭按钮（深灰色圆形背景+白色icon，尺寸较小）
          Positioned(
            top: 10,
            right: 10,
            child: Material(
              color: Colors.transparent,
              child: Ink(
                decoration: const BoxDecoration(
                  color: Color(0xFF232326), // 深灰色背景
                  shape: BoxShape.circle,
                ),
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: const SizedBox(
                    width: 32,
                    height: 32,
                    child: Icon(Icons.close, color: Colors.white, size: 18),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureList() {
    final features = [
      "初始能量为15点，每日刷新",
      "每答错一道题，能量扣除1点",
      "向AI提问消耗1点能量",
      "能量归零后可观看广告充能",
      "订阅会员可免广告无限能量",
    ];
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.04),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Colors.white.withOpacity(0.12), width: 1.2),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: features.map((f) => _buildFeatureItem(f)).toList(),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomPaint(size: const Size(22, 22), painter: _CheckMarkPainter()),
          const SizedBox(width: 12),
          Flexible(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}

class _CheckMarkPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = const Color(0xFF4EC490)
          ..strokeWidth = 2.5
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;
    final path = Path();
    path.moveTo(size.width * 0.18, size.height * 0.55);
    path.lineTo(size.width * 0.45, size.height * 0.80);
    path.lineTo(size.width * 0.82, size.height * 0.22);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _RoundedStarPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;
    final centerX = width / 2;
    final centerY = height / 2;
    final radius = width * 0.45; // 主半径
    final innerRadius = radius * 0.4; // 内部凹陷半径

    // 创建圆润的星形
    for (int i = 0; i < 5; i++) {
      final outerAngle = i * 2 * math.pi / 5 - math.pi / 2;
      final innerAngle = outerAngle + math.pi / 5;

      final outerX = centerX + radius * math.cos(outerAngle);
      final outerY = centerY + radius * math.sin(outerAngle);
      final innerX = centerX + innerRadius * math.cos(innerAngle);
      final innerY = centerY + innerRadius * math.sin(innerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      }

      // 添加控制点来创建圆润效果
      final nextI = (i + 1) % 5;
      final nextOuterAngle = nextI * 2 * math.pi / 5 - math.pi / 2;
      final nextOuterX = centerX + radius * math.cos(nextOuterAngle);
      final nextOuterY = centerY + radius * math.sin(nextOuterAngle);

      // 外部点到内部点的圆润过渡
      final controlPoint1X = outerX + (innerX - outerX) * 0.5;
      final controlPoint1Y = outerY + (innerY - outerY) * 0.5;

      // 内部点到下一个外部点的圆润过渡
      final controlPoint2X = innerX + (nextOuterX - innerX) * 0.5;
      final controlPoint2Y = innerY + (nextOuterY - innerY) * 0.5;

      path.quadraticBezierTo(controlPoint1X, controlPoint1Y, innerX, innerY);
      path.quadraticBezierTo(
        controlPoint2X,
        controlPoint2Y,
        nextOuterX,
        nextOuterY,
      );
    }

    path.close();

    // 创建渐变
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color(0xFFB57BFF), // 浅紫色
        Color(0xFF8B4CFF), // 深紫色
      ],
    );

    final rect = Rect.fromLTWH(0, 0, width, height);
    final paint =
        Paint()
          ..shader = gradient.createShader(rect)
          ..style = PaintingStyle.fill;

    // 添加阴影效果
    canvas.drawShadow(path, Colors.black.withOpacity(0.3), 8, true);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
