import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../widgets/answer_area_factory.dart';

/// 编程题组件
class ProgrammingWidget extends StatefulWidget {
  final Question question;
  final String? userCode;
  final Function(String?) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const ProgrammingWidget({
    Key? key,
    required this.question,
    this.userCode,
    required this.onAnswerChanged,
    required this.interactionState,
  }) : super(key: key);

  @override
  State<ProgrammingWidget> createState() => _ProgrammingWidgetState();
}

class _ProgrammingWidgetState extends State<ProgrammingWidget> {
  late TextEditingController _controller;
  bool _showAnswer = false;

  @override
  void initState() {
    super.initState();
    // 初始化代码编辑器，如果有提供编程提示，使用提示作为初始代码；否则使用用户已有的代码或空字符串
    _controller = TextEditingController(
      text: widget.userCode ?? widget.question.programmingPrompt ?? '',
    );
    _showAnswer =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
  }

  @override
  void didUpdateWidget(ProgrammingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userCode != widget.userCode && widget.userCode != null) {
      _controller.text = widget.userCode!;
    }

    if (oldWidget.interactionState != widget.interactionState) {
      setState(() {
        _showAnswer =
            widget.interactionState !=
            QuestionInteractionState.waitingForSelection;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final correctAnswer = widget.question.answer ?? '';
    final bool isCorrect =
        widget.interactionState == QuestionInteractionState.answerCorrect;

    // 样式颜色
    Color borderColor = Colors.white.withOpacity(0.3);
    if (isShowingFeedback) {
      borderColor =
          isCorrect ? Colors.green : Colors.purple.shade300; // 编程题通常需要更复杂的评价
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 代码编辑器
        ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(color: borderColor, width: 2),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 编辑器顶部栏
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '编程区域',
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Consolas',
                          ),
                        ),
                        const Spacer(),
                      ],
                    ),
                  ),

                  // 代码编辑区
                  TextField(
                    controller: _controller,
                    enabled: !isShowingFeedback,
                    maxLines: 10,
                    minLines: 6,
                    keyboardType: TextInputType.multiline,
                    textInputAction: TextInputAction.newline,
                    textCapitalization: TextCapitalization.none,
                    autocorrect: false,
                    enableSuggestions: false,
                    enableIMEPersonalizedLearning: false,
                    onChanged: null,
                    onEditingComplete: () {
                      widget.onAnswerChanged(_controller.text);
                    },
                    onTap: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _controller.addListener(() {
                          if (_controller.selection.isValid &&
                              !_controller.value.composing.isValid) {
                            widget.onAnswerChanged(_controller.text);
                          }
                        });
                      });
                    },
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: 'Consolas',
                    ),
                    decoration: InputDecoration(
                      hintText: '请在此处编写代码...',
                      hintStyle: TextStyle(
                        color: Colors.white.withOpacity(0.3),
                        fontSize: 14,
                        fontFamily: 'Consolas',
                      ),
                      contentPadding: const EdgeInsets.all(16),
                      border: InputBorder.none,
                      fillColor: Colors.black.withOpacity(0.5),
                      filled: true,
                    ),
                  ),

                  // 编辑器底部栏
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      border: Border(
                        top: BorderSide(
                          color: Colors.grey.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
