import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wakeup_app/providers/user_provider.dart';
import 'package:wakeup_app/widgets/account_bottom_sheet.dart';
import 'package:flutter/foundation.dart';
import 'package:extended_image/extended_image.dart';

/// 动画头像组件
///
/// 如果用户设置了头像，则显示用户头像
/// 否则显示WebP默认动画头像
class AnimatedAvatarWidget extends StatefulWidget {
  /// 头像尺寸，默认为48.0
  final double size;

  const AnimatedAvatarWidget({Key? key, required this.size}) : super(key: key);

  @override
  State<AnimatedAvatarWidget> createState() => _AnimatedAvatarWidgetState();
}

class _AnimatedAvatarWidgetState extends State<AnimatedAvatarWidget> {
  // WebP动图路径
  static const String _webpAnimationPath =
      'assets/animations/default_avatar/avatar_animation.webp';

  // 动图加载错误标记
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final avatarUrl = userProvider.avatarUrl;

    if (kDebugMode) {
      print('👤 构建头像组件: 用户登录状态=${userProvider.isLoggedIn}, 头像URL=$avatarUrl');
    }

    // 如果用户已登录且设置了头像
    if (userProvider.isLoggedIn && avatarUrl != null && avatarUrl.isNotEmpty) {
      return GestureDetector(
        onTap: () => showAccountBottomSheet(context),
        child: ClipOval(
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child: Image.network(
              avatarUrl,
              width: widget.size,
              height: widget.size,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                if (kDebugMode) {
                  print('❌ 头像加载失败: $error');
                }
                // 加载失败时显示默认动画头像
                return _buildDefaultAvatar();
              },
            ),
          ),
        ),
      );
    } else {
      // 未登录或未设置头像时显示默认动画头像
      return _buildDefaultAvatar();
    }
  }

  Widget _buildDefaultAvatar() {
    return GestureDetector(
      onTap: () => showAccountBottomSheet(context),
      child: ClipOval(
        child: SizedBox(
          width: widget.size,
          height: widget.size,
          child: _hasError ? _buildFallbackAvatar() : _buildWebpAnimation(),
        ),
      ),
    );
  }

  Widget _buildWebpAnimation() {
    try {
      return ExtendedImage.asset(
        _webpAnimationPath,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        enableLoadState: false,
        loadStateChanged: (ExtendedImageState state) {
          if (state.extendedImageLoadState == LoadState.failed) {
            if (kDebugMode) {
              print('❌ WebP动图加载失败');
            }

            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _hasError = true;
                });
              }
            });

            return _buildFallbackAvatar();
          }
          return null;
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ WebP动图显示错误: $e');
      }

      setState(() {
        _hasError = true;
      });

      return _buildFallbackAvatar();
    }
  }

  // 备用头像显示
  Widget _buildFallbackAvatar() {
    if (kDebugMode) {
      print('⚠️ 使用备用静态头像图标');
    }

    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.red.shade300,
      ),
      child: Center(
        child: Icon(Icons.person, size: widget.size * 0.6, color: Colors.white),
      ),
    );
  }
}
