import 'dart:async';
import 'dart:ui';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/physics_engine.dart' as physics;
import '../utils/sensor_manager.dart' as sensors;
import '../utils/particle_system.dart' as particles;

/// iOS 26 高级液态玻璃组件
/// 集成物理引擎、传感器响应、粒子系统的完整液态玻璃效果
class AdvancedLiquidGlass extends StatefulWidget {
  final Widget child;
  final double viscosity; // 粘滞度 (0.1-2.0)
  final double refractionIndex; // 折射率 (1.0-1.8)
  final double surfaceTension; // 表面张力 (0.0-1.0)
  final double opticalDensity; // 光学密度 (0.0-1.0)
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool enableSensorResponse; // 是否启用传感器响应
  final bool enableParticleSystem; // 是否启用粒子系统
  final bool enableTouchResponse; // 是否启用触摸响应
  final double performanceLevel; // 性能等级 (0.0-1.0)

  const AdvancedLiquidGlass({
    super.key,
    required this.child,
    this.viscosity = 1.2,
    this.refractionIndex = 1.33,
    this.surfaceTension = 0.6,
    this.opticalDensity = 0.85,
    this.borderRadius,
    this.padding,
    this.margin,
    this.enableSensorResponse = true,
    this.enableParticleSystem = true,
    this.enableTouchResponse = true,
    this.performanceLevel = 1.0,
  });

  @override
  State<AdvancedLiquidGlass> createState() => _AdvancedLiquidGlassState();
}

class _AdvancedLiquidGlassState extends State<AdvancedLiquidGlass>
    with TickerProviderStateMixin {
  
  // 核心系统
  late physics.LiquidPhysicsEngine _physicsEngine;
  late particles.ParticleSystem _particleSystem;
  late sensors.SensorManager _sensorManager;
  
  // 动画控制器
  late AnimationController _animationController;
  late AnimationController _touchAnimationController;
  late Timer _updateTimer;
  
  // 状态变量
  bool _isInitialized = false;
  Size _lastSize = Size.zero;
  DateTime _lastFrameTime = DateTime.now();
  
  // 触摸状态
  final List<TouchPoint> _activeTouches = [];
  
  // 性能监控
  int _frameCount = 0;
  double _currentFPS = 60.0;
  DateTime _fpsUpdateTime = DateTime.now();
  
  // 动态参数
  double _currentViscosity = 1.2;
  double _currentBlur = 20.0;
  double _currentOpacity = 0.15;
  Color _dominantColor = Colors.white;

  @override
  void initState() {
    super.initState();
    // 延迟初始化，等待第一帧渲染完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeSystems();
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 备用初始化方案，确保 MediaQuery 可用时初始化
    if (!_isInitialized) {
      _initializeSystems();
    }
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  void _initializeSystems() async {
    // 防止重复初始化
    if (_isInitialized) return;
    
    // 确保 context 可用
    if (!mounted) return;
    
    // 初始化物理引擎
    _physicsEngine = physics.LiquidPhysicsEngine(
      viscosity: widget.viscosity,
      refractionIndex: widget.refractionIndex,
      surfaceTension: widget.surfaceTension,
      opticalDensity: widget.opticalDensity,
    );

    // 初始化粒子系统
    _particleSystem = particles.ParticleSystem(
      maxParticles: (150 * widget.performanceLevel).round(),
      maxCaustics: (20 * widget.performanceLevel).round(),
      screenSize: MediaQuery.of(context).size,
    );

    // 初始化传感器管理器
    if (widget.enableSensorResponse) {
      try {
        _sensorManager = sensors.SensorManager();
        await _sensorManager.initialize();
        _setupSensorCallbacks();
        print('✅ 传感器管理器初始化完成');
      } catch (e) {
        print('⚠️ 传感器初始化失败，将使用降级模式: $e');
        // 创建一个模拟的传感器管理器来避免空指针
        _sensorManager = sensors.SensorManager();
        _setupDegradedMode();
      }
    } else {
      print('ℹ️ 传感器响应已禁用，仅使用静态效果');
    }

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 16), // ~60fps
      vsync: this,
    )..repeat();

    _touchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 设置更新定时器
    _updateTimer = Timer.periodic(
      Duration(milliseconds: (16 * (2.0 - widget.performanceLevel)).round()),
      _updateSystems,
    );

    // 监听动画
    _animationController.addListener(_onAnimationFrame);

    setState(() {
      _isInitialized = true;
      _currentViscosity = widget.viscosity;
    });
  }

  void _setupSensorCallbacks() {
    if (!widget.enableSensorResponse) return;

    // 检查传感器是否可用，不可用时使用降级策略
    if (!_sensorManager.sensorsAvailable) {
      print('🔄 传感器不可用，启用降级模式的回调');
      _setupDegradedMode();
      return;
    }

    try {
      // 重力变化回调
      _sensorManager.setOnGravityChanged((gravity) {
        if (mounted) {
          _physicsEngine.updateDeviceTilt(gravity.x, gravity.y);
          
          if (widget.enableParticleSystem) {
            _particleSystem.addGravityParticles(
              particles.Vector2(gravity.x, gravity.y),
              _sensorManager.motionIntensity,
            );
          }
        }
      });

      // 运动强度回调
      _sensorManager.setOnMotionIntensityChanged((intensity) {
        if (mounted) {
          // 根据运动调整粘滞度
          final motionBasedViscosity = _sensorManager.calculateMotionBasedViscosity(widget.viscosity);
          setState(() {
            _currentViscosity = motionBasedViscosity;
            _currentBlur = 20.0 + intensity * 10.0;
          });
        }
      });

      // 环境亮度回调
      _sensorManager.setOnBrightnessChanged((brightness) {
        if (mounted) {
          final lightBasedOpacity = _sensorManager.calculateLightBasedOpacity(widget.opticalDensity);
          setState(() {
            _currentOpacity = lightBasedOpacity;
          });
        }
      });
      
      print('✅ 传感器回调设置完成');
    } catch (e) {
      print('❌ 设置传感器回调失败: $e，启用降级模式');
      _setupDegradedMode();
    }
  }
  
  void _setupDegradedMode() {
    print('🔧 设置降级模式 - 仅使用触摸响应和静态效果');
    
    // 在降级模式下，仍然保持基本的液态玻璃视觉效果
    // 但不依赖于传感器数据
    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!mounted || !_isInitialized) {
        timer.cancel();
        return;
      }
      
      // 轻微的静态动画效果
      setState(() {
        _currentBlur = 18.0 + (DateTime.now().millisecondsSinceEpoch % 4000) / 4000.0 * 4.0;
        _currentOpacity = widget.opticalDensity * (0.9 + sin(DateTime.now().millisecondsSinceEpoch / 3000.0) * 0.1);
      });
    });
  }

  void _updateSystems(Timer timer) {
    if (!_isInitialized || !mounted) return;

    final now = DateTime.now();
    final deltaTime = now.difference(_lastFrameTime);
    _lastFrameTime = now;

    // 更新物理引擎
    _physicsEngine.update(deltaTime);

    // 更新粒子系统
    if (widget.enableParticleSystem) {
      _particleSystem.update(
        deltaTime,
        ambientBrightness: widget.enableSensorResponse ? _sensorManager.ambientBrightness : 0.5,
        dominantColor: _dominantColor,
        liquidDensity: _currentViscosity / 2.0,
        motionIntensity: widget.enableSensorResponse ? _sensorManager.motionIntensity : 0.0,
      );
    }

    // 更新性能监控
    _updatePerformanceMetrics();
  }

  void _onAnimationFrame() {
    if (mounted) {
      setState(() {
        _frameCount++;
      });
    }
  }

  void _updatePerformanceMetrics() {
    final now = DateTime.now();
    if (now.difference(_fpsUpdateTime).inMilliseconds > 1000) {
      _currentFPS = _frameCount.toDouble();
      _frameCount = 0;
      _fpsUpdateTime = now;

      // 自适应性能调整
      if (_currentFPS < 30 && widget.performanceLevel > 0.5) {
        // 降低效果质量以提升性能
        _particleSystem.maxParticles = (_particleSystem.maxParticles * 0.8).round();
        _particleSystem.maxCaustics = (_particleSystem.maxCaustics * 0.8).round();
      }
    }
  }

  void _onTouchStart(Offset position) {
    if (!widget.enableTouchResponse) return;

    HapticFeedback.lightImpact();

    final touchPoint = TouchPoint(
      position: position,
      startTime: DateTime.now(),
      intensity: 1.0,
    );
    _activeTouches.add(touchPoint);

    // 添加物理扰动
    _physicsEngine.addTouchDisturbance(position, 1.0);

    // 添加粒子效果
    if (widget.enableParticleSystem) {
      _particleSystem.addTouchLight(position, 1.0);
    }

    // 启动触摸动画
    _touchAnimationController.reset();
    _touchAnimationController.forward();

    // 颜色反馈
    setState(() {
      _dominantColor = _calculateTouchColor(position);
    });
  }

  void _onTouchMove(Offset position) {
    if (!widget.enableTouchResponse || _activeTouches.isEmpty) return;

    // 更新最后一个触摸点
    if (_activeTouches.isNotEmpty) {
      _activeTouches.last.position = position;
      
      // 添加连续扰动
      _physicsEngine.addTouchDisturbance(position, 0.5);
      
      if (widget.enableParticleSystem) {
        _particleSystem.addTouchLight(position, 0.3);
      }
    }
  }

  void _onTouchEnd() {
    if (!widget.enableTouchResponse) return;

    _activeTouches.clear();
    
    // 恢复默认颜色
    setState(() {
      _dominantColor = Colors.white;
    });
  }

  Color _calculateTouchColor(Offset position) {
    final size = MediaQuery.of(context).size;
    final hue = (position.dx / size.width) * 360;
    return HSLColor.fromAHSL(0.8, hue, 0.7, 0.6).toColor();
  }

  void _cleanup() {
    if (!_isInitialized) return;
    
    _updateTimer.cancel();
    _animationController.dispose();
    _touchAnimationController.dispose();
    
    if (widget.enableSensorResponse) {
      _sensorManager.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildLoadingState();
    }

    return Container(
      margin: widget.margin,
      child: GestureDetector(
        onTapDown: (details) => _onTouchStart(details.localPosition),
        onPanStart: (details) => _onTouchStart(details.localPosition),
        onPanUpdate: (details) => _onTouchMove(details.localPosition),
        onPanEnd: (_) => _onTouchEnd(),
        onTapUp: (_) => _onTouchEnd(),
        child: ClipRRect(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
          child: Stack(
            children: [
              // 背景模糊层
              _buildBlurLayer(),
              
              // 粒子系统层
              if (widget.enableParticleSystem) _buildParticleLayer(),
              
              // 主要内容层
              _buildContentLayer(),
              
              // 液态玻璃效果层
              _buildLiquidGlassLayer(),
              
              // 触摸反馈层
              if (widget.enableTouchResponse) _buildTouchFeedbackLayer(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      margin: widget.margin,
      padding: widget.padding,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: widget.child,
    );
  }

  Widget _buildBlurLayer() {
    return Positioned.fill(
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: _currentBlur,
          sigmaY: _currentBlur,
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(_currentOpacity * 1.3),
                Colors.white.withOpacity(_currentOpacity * 0.7),
                Colors.white.withOpacity(_currentOpacity * 1.1),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 0.5,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildParticleLayer() {
    return Positioned.fill(
      child: CustomPaint(
        painter: ParticleSystemPainter(_particleSystem),
      ),
    );
  }

  Widget _buildContentLayer() {
    return Padding(
      padding: widget.padding ?? EdgeInsets.zero,
      child: widget.child,
    );
  }

  Widget _buildLiquidGlassLayer() {
    return Positioned.fill(
      child: CustomPaint(
        painter: LiquidGlassEffectPainter(
          physicsEngine: _physicsEngine,
          viscosity: _currentViscosity,
          dominantColor: _dominantColor,
          time: _animationController.value,
        ),
      ),
    );
  }

  Widget _buildTouchFeedbackLayer() {
    return AnimatedBuilder(
      animation: _touchAnimationController,
      builder: (context, child) {
        if (_activeTouches.isEmpty) return const SizedBox.shrink();

        return Positioned.fill(
          child: CustomPaint(
            painter: TouchFeedbackPainter(
              touches: _activeTouches,
              animationProgress: _touchAnimationController.value,
            ),
          ),
        );
      },
    );
  }
}

/// 粒子系统绘制器
class ParticleSystemPainter extends CustomPainter {
  final particles.ParticleSystem particleSystem;

  ParticleSystemPainter(this.particleSystem);

  @override
  void paint(Canvas canvas, Size size) {
    particleSystem.paint(canvas, size);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 液态玻璃效果绘制器
class LiquidGlassEffectPainter extends CustomPainter {
  final physics.LiquidPhysicsEngine physicsEngine;
  final double viscosity;
  final Color dominantColor;
  final double time;

  LiquidGlassEffectPainter({
    required this.physicsEngine,
    required this.viscosity,
    required this.dominantColor,
    required this.time,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制液态扭曲效果
    _drawLiquidDistortion(canvas, size);
    
    // 绘制边缘光晕
    _drawEdgeGlow(canvas, size);
    
    // 绘制深度层次
    _drawDepthLayers(canvas, size);
  }

  void _drawLiquidDistortion(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = RadialGradient(
        center: Alignment.center,
        radius: 1.0,
        colors: [
          dominantColor.withOpacity(0.1),
          Colors.transparent,
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    // 基于物理引擎的扭曲点
    for (final wave in physicsEngine.waves) {
      final center = Offset(wave.center.x, wave.center.y);
      final radius = wave.radius * (1.0 + sin(time * 4 + wave.phase) * 0.2);
      
      canvas.drawCircle(center, radius, paint);
    }
  }

  void _drawEdgeGlow(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = dominantColor.withOpacity(0.15)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 8.0);

    canvas.drawRRect(
      RRect.fromLTRBR(1, 1, size.width - 1, size.height - 1, const Radius.circular(24)),
      paint,
    );
  }

  void _drawDepthLayers(Canvas canvas, Size size) {
    // 多层深度效果
    for (int i = 0; i < 3; i++) {
      final opacity = 0.05 * (3 - i);
      final offset = i * 2.0;
      
      final paint = Paint()
        ..color = Colors.white.withOpacity(opacity)
        ..style = PaintingStyle.fill;

      canvas.drawRRect(
        RRect.fromLTRBR(
          offset, 
          offset, 
          size.width - offset, 
          size.height - offset, 
          const Radius.circular(24)
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 触摸反馈绘制器
class TouchFeedbackPainter extends CustomPainter {
  final List<TouchPoint> touches;
  final double animationProgress;

  TouchFeedbackPainter({
    required this.touches,
    required this.animationProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (final touch in touches) {
      final center = touch.position;
      final radius = 30.0 * animationProgress;
      
      final paint = Paint()
        ..color = Colors.white.withOpacity(0.3 * (1.0 - animationProgress))
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 触摸点数据结构
class TouchPoint {
  Offset position;
  DateTime startTime;
  double intensity;

  TouchPoint({
    required this.position,
    required this.startTime,
    required this.intensity,
  });
}