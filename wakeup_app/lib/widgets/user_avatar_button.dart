import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wakeup_app/providers/user_provider.dart';
import 'package:wakeup_app/widgets/account_bottom_sheet.dart';
import 'package:wakeup_app/widgets/animated_avatar_widget.dart';

class UserAvatarButton extends StatelessWidget {
  final double size;

  const UserAvatarButton({Key? key, this.size = 48}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => showAccountBottomSheet(context),
      child: AnimatedAvatarWidget(size: size),
    );
  }
}
