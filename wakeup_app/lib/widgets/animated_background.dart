import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 动态背景组件，提供Apple Music风格的动态线条背景
class AnimatedBackground extends StatefulWidget {
  final Color? color1;
  final Color? color2;
  final Color? color3;

  const AnimatedBackground({super.key, this.color1, this.color2, this.color3});

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller1;
  late AnimationController _controller2;
  late AnimationController _controller3;

  @override
  void initState() {
    super.initState();

    // 创建三个不同速度的动画控制器，形成更自然的流动效果
    _controller1 = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _controller2 = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 30),
    )..repeat();

    _controller3 = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 25),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller1.dispose();
    _controller2.dispose();
    _controller3.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 判断当前主题模式
    final isDarkMode =
        MediaQuery.of(context).platformBrightness == Brightness.dark;

    // 根据主题模式设置不同的颜色
    final color1 =
        widget.color1 ??
        (isDarkMode ? const Color(0xFF101010) : const Color(0xFFF5F5F7));

    final color2 =
        widget.color2 ??
        (isDarkMode ? const Color(0xFF222222) : const Color(0xFFE0E0E0));

    final color3 =
        widget.color3 ??
        (isDarkMode ? const Color(0xFF333333) : const Color(0xFFCCCCCC));

    return Container(
      decoration: BoxDecoration(
        // 基础渐变背景
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [color1, color3],
        ),
      ),
      child: Stack(
        children: [
          // 第一个动画渐变
          AnimatedBuilder(
            animation: _controller1,
            builder: (context, child) {
              return CustomPaint(
                painter: _AnimatedGradientPainter(
                  color1: color2.withOpacity(0.5),
                  color2: color3.withOpacity(0.3),
                  progress: _controller1.value,
                ),
                child: Container(),
              );
            },
          ),

          // 第二个动画渐变
          AnimatedBuilder(
            animation: _controller2,
            builder: (context, child) {
              return CustomPaint(
                painter: _AnimatedGradientPainter(
                  color1: color1.withOpacity(0.3),
                  color2: color2.withOpacity(0.2),
                  progress: _controller2.value,
                  angleFactor: 0.7,
                ),
                child: Container(),
              );
            },
          ),

          // 第三个动画渐变
          AnimatedBuilder(
            animation: _controller3,
            builder: (context, child) {
              return CustomPaint(
                painter: _AnimatedGradientPainter(
                  color1: color3.withOpacity(0.2),
                  color2: color1.withOpacity(0.1),
                  progress: _controller3.value,
                  angleFactor: 1.3,
                ),
                child: Container(),
              );
            },
          ),

          // 半透明叠加层，使背景不那么突兀
          Container(
            color:
                isDarkMode
                    ? Colors.black.withOpacity(0.4)
                    : Colors.white.withOpacity(0.5),
          ),
        ],
      ),
    );
  }
}

/// 动画渐变绘制器
class _AnimatedGradientPainter extends CustomPainter {
  final Color color1;
  final Color color2;
  final double progress;
  final double angleFactor;

  _AnimatedGradientPainter({
    required this.color1,
    required this.color2,
    required this.progress,
    this.angleFactor = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Offset.zero & size;

    // 计算动态旋转角度和位置
    final angle = 2 * math.pi * progress * angleFactor;
    final double x = math.cos(angle) * size.width * 0.5 + size.width * 0.5;
    final double y = math.sin(angle) * size.height * 0.5 + size.height * 0.5;

    // 创建渐变效果
    final paint =
        Paint()
          ..shader = RadialGradient(
            colors: [color1, color2.withOpacity(0.0)],
            stops: const [0.2, 1.0],
          ).createShader(
            Rect.fromCenter(
              center: Offset(x, y),
              width: size.width * 1.5,
              height: size.height * 1.5,
            ),
          );

    // 绘制渐变
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(_AnimatedGradientPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
