import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:ui';
import '../constants/colors.dart';
import '../constants/fonts.dart';
import 'dart:math' as math;

/// 筛选选项
class FilterOption {
  final String id;
  final String title;
  bool isSelected;

  FilterOption({
    required this.id,
    required this.title,
    this.isSelected = false,
  });

  // 用于深拷贝
  FilterOption copyWith({String? id, String? title, bool? isSelected}) {
    return FilterOption(
      id: id ?? this.id,
      title: title ?? this.title,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

/// 筛选组
class FilterGroup {
  final String title;
  final List<FilterOption> options;
  final bool isMultiSelect;

  FilterGroup({
    required this.title,
    required this.options,
    this.isMultiSelect = false,
  });

  // 用于深拷贝
  FilterGroup copyWith({
    String? title,
    List<FilterOption>? options,
    bool? isMultiSelect,
  }) {
    return FilterGroup(
      title: title ?? this.title,
      options: options ?? this.options.map((o) => o.copyWith()).toList(),
      isMultiSelect: isMultiSelect ?? this.isMultiSelect,
    );
  }
}

/// 筛选结果
class FilterResult {
  // 每个组的选中项目 Map<组名, List<选项ID>>
  final Map<String, List<String>> selectedFilters;

  // 是否为智能推荐模式
  final bool isSmartRecommendation;

  // 检查是否为空筛选
  bool get isEmpty =>
      selectedFilters.isEmpty ||
      selectedFilters.values.every(
        (ids) => ids.isEmpty || (ids.length == 1 && ids.first == 'smart'),
      );

  const FilterResult({
    required this.selectedFilters,
    this.isSmartRecommendation = false,
  });
}

/// 筛选弹窗
class FilterPopup {
  /// 显示筛选弹窗
  static Future<FilterResult?> show({
    required BuildContext context,
    required List<FilterGroup> filterGroups,
    bool defaultToSmartRecommendation = true,
  }) async {
    // 创建一个深拷贝，避免修改原始数据
    final groups = filterGroups.map((g) => g.copyWith()).toList();

    // 展示弹窗
    return await showModalBottomSheet<FilterResult?>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      useRootNavigator: true,
      builder:
          (context) => _FilterPopupContent(
            filterGroups: groups,
            defaultToSmartRecommendation: defaultToSmartRecommendation,
          ),
    );
  }
}

class _FilterPopupContent extends StatefulWidget {
  final List<FilterGroup> filterGroups;
  final bool defaultToSmartRecommendation;

  const _FilterPopupContent({
    Key? key,
    required this.filterGroups,
    this.defaultToSmartRecommendation = true,
  }) : super(key: key);

  @override
  _FilterPopupContentState createState() => _FilterPopupContentState();
}

class _FilterPopupContentState extends State<_FilterPopupContent>
    with SingleTickerProviderStateMixin {
  late List<FilterGroup> _filterGroups;
  bool _isSmartRecommendation = true;
  bool _isSmartModeActive = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // 修复初始化方式
  final SparkleIconPainter _sparkleIconPainter = SparkleIconPainter();

  @override
  void initState() {
    super.initState();
    _filterGroups = widget.filterGroups;
    // 确保智能推荐默认为激活状态
    _isSmartRecommendation = true;
    _isSmartModeActive = true;

    // 创建动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // 创建淡入和缩放动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    // 默认选择所有组中的"smart"选项
    _resetToSmartRecommendation();

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // 选择筛选选项
  void _selectOption(FilterGroup group, FilterOption option) {
    setState(() {
      // 如果是多选模式
      if (group.isMultiSelect) {
        option.isSelected = !option.isSelected;

        // 如果选中的是智能推荐，取消其他选项
        if (option.id == 'smart' && option.isSelected) {
          for (var o in group.options) {
            if (o.id != 'smart') {
              o.isSelected = false;
            }
          }
        }
        // 如果选中的是其他选项，取消智能推荐
        else if (option.id != 'smart') {
          for (var o in group.options) {
            if (o.id == 'smart') {
              o.isSelected = false;
            }
          }
        }
      }
      // 单选模式
      else {
        for (var o in group.options) {
          o.isSelected = (o.id == option.id);
        }
      }

      // 更新智能推荐状态
      _updateSmartRecommendationStatus();
    });
  }

  // 更新智能推荐状态
  void _updateSmartRecommendationStatus() {
    bool isSmartMode = true;

    for (var group in _filterGroups) {
      final selectedOptions = group.options.where((o) => o.isSelected).toList();

      // 如果未选择任何选项或者只选择了"智能推荐"
      if (selectedOptions.isEmpty ||
          (selectedOptions.length == 1 &&
              selectedOptions.first.id == 'smart')) {
        continue;
      } else {
        isSmartMode = false;
        break;
      }
    }

    _isSmartRecommendation = isSmartMode;
  }

  // 恢复智能推荐
  void _resetToSmartRecommendation() {
    setState(() {
      for (var group in _filterGroups) {
        for (var option in group.options) {
          option.isSelected = (option.id == 'smart');
        }
      }
      _isSmartRecommendation = true;
    });
  }

  // 收集所有选中的筛选条件
  FilterResult _collectSelectedFilters() {
    final Map<String, List<String>> selectedFilters = {};

    // 如果智能模式被激活，则返回智能推荐结果
    if (_isSmartModeActive) {
      return FilterResult(
        selectedFilters: {
          'smartMode': ['enabled'],
        },
        isSmartRecommendation: true,
      );
    }
    // 如果智能模式被关闭，则收集用户手动选择的筛选条件
    else {
      for (var group in _filterGroups) {
        final selectedOptions =
            group.options.where((o) => o.isSelected).map((o) => o.id).toList();

        if (selectedOptions.isNotEmpty) {
          selectedFilters[group.title] = selectedOptions;
        }
      }
    }

    return FilterResult(
      selectedFilters: selectedFilters,
      isSmartRecommendation: _isSmartModeActive,
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.8;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            alignment: Alignment.bottomCenter,
            child: _buildPopupContent(maxHeight),
          ),
        );
      },
    );
  }

  Widget _buildPopupContent(double maxHeight) {
    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(12),
        topRight: Radius.circular(12),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          constraints: BoxConstraints(maxHeight: maxHeight),
          decoration: BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.25),
                blurRadius: 10,
                spreadRadius: 0,
                offset: Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              _buildSmartModeSwitch(),
              Flexible(
                child: ListView(
                  physics: const ClampingScrollPhysics(),
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  children: [
                    ..._filterGroups.map(_buildFilterGroup).toList(),
                    SizedBox(height: 16),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建筛选弹窗头部
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade900, width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text(
              '取消',
              style: TextStyle(color: Colors.grey, fontSize: 15),
            ),
          ),
          Text(
            '筛选',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(_collectSelectedFilters());
            },
            child: Text(
              '完成',
              style: TextStyle(
                color: Colors.white,
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建智能模式开关
  Widget _buildSmartModeSwitch() {
    // 智能模式激活时使用多彩渐变背景
    final BoxDecoration containerDecoration =
        _isSmartModeActive
            ? BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF8B5CF6).withOpacity(0.95), // 蓝紫色，增加透明度
                  Color(0xFFEC4899).withOpacity(0.95), // 深红橘色，增加透明度
                  Color(0xFFF472B6).withOpacity(0.95), // 粉色，增加透明度
                ],
              ),
              border: Border(
                bottom: BorderSide(
                  color: Colors.transparent,
                  width: 0.5,
                ), // 减细边框
              ),
              // 添加内部阴影效果替代外部阴影
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF8B5CF6).withOpacity(0.2), // 减小阴影不透明度
                  blurRadius: 8, // 减小阴影模糊
                  spreadRadius: -3, // 减小阴影扩散
                  offset: Offset(0, 0),
                ),
              ],
            )
            : BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade900,
                  width: 0.5,
                ), // 减细边框
              ),
            );

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      // 移除外部margin，保持一致的布局
      decoration: containerDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      '智能出题',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    // 智能激活时显示灵光图标
                    if (_isSmartModeActive)
                      Padding(
                        padding: const EdgeInsets.only(left: 6.0),
                        child: CustomPaint(
                          size: Size(14, 14),
                          painter: _sparkleIconPainter,
                        ),
                      ),
                  ],
                ),
              ),
              Transform.scale(
                scale: 0.8,
                child: CupertinoSwitch(
                  value: _isSmartModeActive,
                  activeColor: Color(0xFFD4F5D4),
                  onChanged: (value) {
                    setState(() {
                      _isSmartModeActive = value;

                      // 如果启用智能模式，重置所有筛选条件为智能推荐
                      if (_isSmartModeActive) {
                        _resetToSmartRecommendation();
                      }
                      // 如果关闭智能模式，清除所有智能推荐的选项，允许用户手动选择
                      else {
                        for (var group in _filterGroups) {
                          for (var option in group.options) {
                            if (option.id == 'smart') {
                              option.isSelected = false;
                            }
                          }
                        }
                      }
                    });
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 4),
          Text(
            '基于你过往的做题记录与掌握情况，智能分析你的薄弱环节，为你精准推荐强化题目。',
            style: TextStyle(
              fontSize: 11,
              color: _isSmartModeActive ? Colors.white : Colors.grey,
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  // 构建筛选组
  Widget _buildFilterGroup(FilterGroup group) {
    // 如果智能模式开启，则使过滤组看起来被禁用
    final bool isDisabled = _isSmartModeActive;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(16, 14, 16, 8),
          child: Text(
            group.title,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: isDisabled ? Colors.grey : Colors.white,
            ),
          ),
        ),
        // 使用Wrap组件来水平排列选项
        Opacity(
          opacity: isDisabled ? 0.5 : 1.0,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: Wrap(
              spacing: 6,
              runSpacing: 8,
              children:
                  group.options
                      .where((option) => option.id != 'smart')
                      .map(
                        (option) =>
                            _buildFilterOption(group, option, isDisabled),
                      )
                      .toList(),
            ),
          ),
        ),
        SizedBox(height: 6),
      ],
    );
  }

  // 构建筛选选项 - 现在是小卡片样式
  Widget _buildFilterOption(
    FilterGroup group,
    FilterOption option,
    bool isDisabled,
  ) {
    bool isSelected = option.isSelected;

    return GestureDetector(
      onTap: isDisabled ? null : () => _selectOption(group, option),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Colors.white.withOpacity(isDisabled ? 0.08 : 0.15)
                  : Colors.grey.withOpacity(0.08),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected
                    ? (isDisabled
                        ? Colors.grey.withOpacity(0.5)
                        : Colors.white.withOpacity(0.7))
                    : Colors.transparent,
            width: 1.0,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              option.title,
              style: TextStyle(
                fontSize: 13,
                color:
                    isSelected
                        ? (isDisabled ? Colors.grey : Colors.white)
                        : Colors.white.withOpacity(0.9),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (isSelected)
              Padding(
                padding: const EdgeInsets.only(left: 3),
                child: Icon(
                  CupertinoIcons.checkmark_alt,
                  color: isDisabled ? Colors.grey : Colors.white,
                  size: 12,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// 灵光图标绘制类
class SparkleIconPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double radius = size.width / 2;

    // 绘制主星形
    _drawSparkle(canvas, centerX, centerY, radius, 0);

    // 绘制小星形
    _drawSparkle(
      canvas,
      centerX + radius * 0.7,
      centerY - radius * 0.7,
      radius * 0.4,
      15,
    );
  }

  void _drawSparkle(
    Canvas canvas,
    double centerX,
    double centerY,
    double radius,
    double rotationDegrees,
  ) {
    final paint =
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.fill;

    final path = Path();

    // 转换为弧度
    final rotationRadians = rotationDegrees * 3.14159 / 180;

    // 4个主点
    for (int i = 0; i < 4; i++) {
      final angle = i * 3.14159 / 2 + rotationRadians;
      final x = centerX + radius * 0.9 * math.cos(angle);
      final y = centerY + radius * 0.9 * math.sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // 添加内点
      final innerAngle = angle + 3.14159 / 4;
      final innerX = centerX + radius * 0.3 * math.cos(innerAngle);
      final innerY = centerY + radius * 0.3 * math.sin(innerAngle);

      path.lineTo(innerX, innerY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// 添加cos和sin函数用于角度计算
double cos(double angle) => math.cos(angle);
double sin(double angle) => math.sin(angle);
