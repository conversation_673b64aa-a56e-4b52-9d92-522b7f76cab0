import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../widgets/answer_area_factory.dart';

/// 简答题组件
class ShortAnswerWidget extends StatefulWidget {
  final Question question;
  final String? userAnswer;
  final Function(String?) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const ShortAnswerWidget({
    Key? key,
    required this.question,
    this.userAnswer,
    required this.onAnswerChanged,
    required this.interactionState,
  }) : super(key: key);

  @override
  State<ShortAnswerWidget> createState() => _ShortAnswerWidgetState();
}

class _ShortAnswerWidgetState extends State<ShortAnswerWidget> {
  late TextEditingController _controller;
  bool _showAnswer = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.userAnswer);
    _showAnswer =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
  }

  @override
  void didUpdateWidget(ShortAnswerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userAnswer != widget.userAnswer) {
      _controller.text = widget.userAnswer ?? '';
    }

    if (oldWidget.interactionState != widget.interactionState) {
      setState(() {
        _showAnswer =
            widget.interactionState !=
            QuestionInteractionState.waitingForSelection;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 确定是否显示正确/错误状态
    final bool isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final correctAnswer = widget.question.answer ?? '';

    // 简答题通常需要人工评分，所以这里假设系统自动比对的结果
    // 实际应用中可能需要更复杂的逻辑或人工介入
    final bool isCorrect =
        widget.interactionState == QuestionInteractionState.answerCorrect;

    // 样式颜色
    Color borderColor = Colors.white.withOpacity(0.3);
    if (isShowingFeedback) {
      borderColor =
          isCorrect ? Colors.green : Colors.purple.shade300; // 简答题用紫色表示"已回答"
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 文本区域
        ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(color: borderColor, width: 2),
              ),
              child: TextField(
                controller: _controller,
                enabled: !isShowingFeedback,
                maxLines: 5, // 多行文本输入
                minLines: 3,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.newline,
                textCapitalization: TextCapitalization.sentences,
                autocorrect: false,
                enableSuggestions: false,
                enableIMEPersonalizedLearning: false,
                onChanged: null,
                onEditingComplete: () {
                  widget.onAnswerChanged(_controller.text);
                },
                onTap: () {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _controller.addListener(() {
                      if (_controller.selection.isValid &&
                          !_controller.value.composing.isValid) {
                        widget.onAnswerChanged(_controller.text);
                      }
                    });
                  });
                },
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Noto Sans SC',
                ),
                decoration: InputDecoration(
                  hintText: '请在此处输入你的回答...',
                  hintStyle: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 16,
                    fontFamily: 'Noto Sans SC',
                  ),
                  contentPadding: const EdgeInsets.all(16),
                  border: InputBorder.none,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
