import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// 可折叠区域组件
class CollapsibleSection extends StatelessWidget {
  final String title;
  final Widget child;
  final bool isExpanded;
  final VoidCallback onToggle;

  const CollapsibleSection({
    super.key,
    required this.title,
    required this.child,
    required this.isExpanded,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        GestureDetector(
          onTap: onToggle,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            margin: const EdgeInsets.only(top: 16, bottom: 4),
            child: Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Noto Sans SC',
                  ),
                ),
                const Spacer(),
                Icon(
                  isExpanded
                      ? CupertinoIcons.chevron_up
                      : CupertinoIcons.chevron_down,
                  color: Colors.white,
                  size: 18,
                ),
              ],
            ),
          ),
        ),

        // 内容区域 - 使用AnimatedCrossFade实现优雅的展开/收起动画
        AnimatedCrossFade(
          firstChild: const SizedBox(height: 0),
          secondChild: child,
          crossFadeState:
              isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 300),
          sizeCurve: Curves.easeInOut,
        ),
      ],
    );
  }
}
