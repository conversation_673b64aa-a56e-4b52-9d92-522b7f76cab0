import 'dart:async';
import 'dart:ui';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Apple's Liquid Glass Material
/// Implements the true Apple Liquid Glass design as per WWDC 2025
/// Features lensing, adaptive transparency, specular highlights, and morphing
class AppleLiquidGlass extends StatefulWidget {
  final Widget child;
  final LiquidGlassVariant variant;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool enableMotionEffects;
  final bool enableTouchResponse;
  final Color? tintColor;
  final double customOpacity;
  final bool enableMorphing;

  const AppleLiquidGlass({
    super.key,
    required this.child,
    this.variant = LiquidGlassVariant.regular,
    this.borderRadius,
    this.padding,
    this.margin,
    this.enableMotionEffects = true,
    this.enableTouchResponse = true,
    this.tintColor,
    this.customOpacity = 1.0,
    this.enableMorphing = false,
  });

  @override
  State<AppleLiquidGlass> createState() => _AppleLiquidGlassState();
}

enum LiquidGlassVariant {
  regular, // Adaptive with full lensing effects
  clear, // Permanently transparent with dimming
}

class _AppleLiquidGlassState extends State<AppleLiquidGlass>
    with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _frameController;
  late AnimationController _touchController;
  late AnimationController _highlightController;
  late AnimationController _morphController;

  // Animations
  late Animation<double> _frameAnimation;
  late Animation<double> _touchAnimation;
  late Animation<double> _highlightAnimation;
  late Animation<double> _morphAnimation;

  // State tracking
  final GlobalKey _backgroundKey = GlobalKey();
  bool _isDarkContent = false;
  Color _adaptiveColor = Colors.white;
  double _contentBrightness = 0.5;

  // Touch state
  Offset? _lastTouchPosition;
  bool _isTouched = false;

  // Light and highlight state
  Offset _lightPosition = const Offset(0.3, 0.2);
  double _lightIntensity = 0.8;

  // Timers
  Timer? _backgroundAnalysisTimer;
  Timer? _lightMovementTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startLightMovement();

    // Analyze background after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startBackgroundAnalysis();
    });
  }

  @override
  void dispose() {
    _frameController.dispose();
    _touchController.dispose();
    _highlightController.dispose();
    _morphController.dispose();
    _backgroundAnalysisTimer?.cancel();
    _lightMovementTimer?.cancel();
    super.dispose();
  }

  void _initializeAnimations() {
    _frameController = AnimationController(
      duration: const Duration(milliseconds: 16),
      vsync: this,
    );

    _touchController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _morphController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _frameAnimation = Tween<double>(begin: 0, end: 1).animate(_frameController);

    _touchAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _touchController, curve: Curves.elasticOut),
    );

    _highlightAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _highlightController, curve: Curves.easeInOut),
    );

    _morphAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _morphController, curve: Curves.elasticOut),
    );

    _frameController.repeat();

    if (widget.enableMorphing) {
      _morphController.repeat(reverse: true);
    }
  }

  void _startLightMovement() {
    if (!widget.enableMotionEffects) return;

    _lightMovementTimer = Timer.periodic(const Duration(milliseconds: 100), (
      timer,
    ) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        final time = DateTime.now().millisecondsSinceEpoch / 1000.0;
        _lightPosition = Offset(
          0.5 + sin(time * 0.5) * 0.3,
          0.3 + cos(time * 0.3) * 0.2,
        );
        _lightIntensity = 0.6 + sin(time * 0.8) * 0.2;
      });
    });
  }

  void _startBackgroundAnalysis() {
    _backgroundAnalysisTimer = Timer.periodic(
      const Duration(milliseconds: 500),
      (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }
        _analyzeBackground();
      },
    );
  }

  void _analyzeBackground() {
    // Simulate background content analysis
    // In real implementation, this would analyze the actual background

    final time = DateTime.now().millisecondsSinceEpoch / 1000.0;
    final brightness = 0.5 + sin(time * 0.2) * 0.3;

    setState(() {
      _contentBrightness = brightness;
      _isDarkContent = brightness < 0.4;

      // Adaptive color selection
      if (_isDarkContent) {
        _adaptiveColor = Colors.white.withOpacity(0.9);
      } else {
        _adaptiveColor = Colors.black.withOpacity(0.8);
      }
    });
  }

  void _handleTouchStart(Offset position) {
    if (!widget.enableTouchResponse) return;

    setState(() {
      _lastTouchPosition = position;
      _isTouched = true;
    });

    _touchController.forward();
    _highlightController.forward();

    HapticFeedback.lightImpact();
  }

  void _handleTouchEnd() {
    if (!widget.enableTouchResponse) return;

    setState(() {
      _isTouched = false;
    });

    _touchController.reverse();
    _highlightController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _frameAnimation,
            _touchAnimation,
            _highlightAnimation,
            _morphAnimation,
          ]),
          builder: (context, child) {
            return GestureDetector(
              onTapDown: (details) => _handleTouchStart(details.localPosition),
              onTapUp: (_) => _handleTouchEnd(),
              onTapCancel: _handleTouchEnd,
              onPanStart: (details) => _handleTouchStart(details.localPosition),
              onPanEnd: (_) => _handleTouchEnd(),
              child: Stack(
                children: [
                  // Background content capture layer
                  RepaintBoundary(
                    key: _backgroundKey,
                    child: Container(
                      width: double.infinity,
                      height: double.infinity,
                      color: Colors.transparent,
                    ),
                  ),

                  // Main liquid glass material
                  _buildLiquidGlassMaterial(),

                  // Content layer
                  Container(padding: widget.padding, child: widget.child),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLiquidGlassMaterial() {
    return Stack(
      children: [
        // Base glass layer with lensing
        _buildBaseLensingLayer(),

        // Specular highlights
        if (widget.enableMotionEffects) _buildSpecularHighlights(),

        // Touch response glow
        if (widget.enableTouchResponse && _isTouched) _buildTouchGlow(),

        // Adaptive shadows
        _buildAdaptiveShadows(),

        // Tint layer
        if (widget.tintColor != null) _buildTintLayer(),
      ],
    );
  }

  Widget _buildBaseLensingLayer() {
    final variant = widget.variant;

    // Calculate adaptive properties
    final baseOpacity = variant == LiquidGlassVariant.clear ? 0.05 : 0.15;
    final adaptiveOpacity = _calculateAdaptiveOpacity(baseOpacity);
    final blurSigma = _calculateAdaptiveBlur();

    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
      child: Container(
        decoration: BoxDecoration(
          // Lensing effect through multiple gradient layers
          gradient: _buildLensingGradient(),
          border: Border.all(
            color: _adaptiveColor.withOpacity(0.2),
            width: 0.5,
          ),
          borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
        ),
        child: ClipRRect(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
            child: Container(
              decoration: BoxDecoration(
                color: _adaptiveColor.withOpacity(adaptiveOpacity),
                borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Gradient _buildLensingGradient() {
    final centerX = _lightPosition.dx;
    final centerY = _lightPosition.dy;

    // Add morphing effects if enabled
    final morphFactor = widget.enableMorphing ? _morphAnimation.value : 0.0;
    final morphOffset = sin(morphFactor * 2 * pi) * 0.1;
    final radiusMorph = 0.8 + morphOffset;

    // Create lensing effect through radial gradient with morphing
    return RadialGradient(
      center: Alignment(
        (centerX * 2 - 1) + morphOffset,
        (centerY * 2 - 1) + morphOffset * 0.5,
      ),
      radius: radiusMorph,
      colors: [
        _adaptiveColor.withOpacity(
          0.25 * _lightIntensity * (1 + morphFactor * 0.2),
        ),
        _adaptiveColor.withOpacity(0.15 * (1 + morphFactor * 0.1)),
        _adaptiveColor.withOpacity(0.05),
        Colors.transparent,
      ],
      stops: [0.0, 0.3 + morphFactor * 0.1, 0.7 - morphFactor * 0.1, 1.0],
    );
  }

  Widget _buildSpecularHighlights() {
    final animation = _frameAnimation.value;
    final time = animation * 2 * pi;

    return Positioned.fill(
      child: CustomPaint(
        painter: SpecularHighlightsPainter(
          lightPosition: _lightPosition,
          lightIntensity: _lightIntensity,
          time: time,
          adaptiveColor: _adaptiveColor,
        ),
      ),
    );
  }

  Widget _buildTouchGlow() {
    if (_lastTouchPosition == null) return const SizedBox.shrink();

    final glowRadius = 60.0 * _touchAnimation.value;
    final glowOpacity = (1.0 - _touchAnimation.value) * 0.3;

    return Positioned.fill(
      child: CustomPaint(
        painter: TouchGlowPainter(
          touchPosition: _lastTouchPosition!,
          radius: glowRadius,
          opacity: glowOpacity,
          color: widget.tintColor ?? _adaptiveColor,
        ),
      ),
    );
  }

  Widget _buildAdaptiveShadows() {
    // Shadow intensity adapts to content brightness
    final shadowOpacity = _isDarkContent ? 0.1 : 0.3;
    final shadowBlur = _isDarkContent ? 2.0 : 8.0;

    return Container(
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(shadowOpacity),
            blurRadius: shadowBlur,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
    );
  }

  Widget _buildTintLayer() {
    if (widget.tintColor == null) return const SizedBox.shrink();

    // Apple's tinting system - adapts to background content
    final tintOpacity = _calculateTintOpacity();
    final adjustedTint = _adjustTintForContent(widget.tintColor!);

    return Container(
      decoration: BoxDecoration(
        color: adjustedTint.withOpacity(tintOpacity),
        borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
      ),
    );
  }

  double _calculateAdaptiveOpacity(double baseOpacity) {
    // Opacity adapts to content brightness
    if (widget.variant == LiquidGlassVariant.clear) {
      return baseOpacity * widget.customOpacity;
    }

    // Regular variant adapts based on content
    final adaptiveFactor = _isDarkContent ? 0.8 : 1.2;
    return (baseOpacity * adaptiveFactor * widget.customOpacity).clamp(
      0.0,
      1.0,
    );
  }

  double _calculateAdaptiveBlur() {
    // Blur adapts to content and touch state
    var baseBlur = widget.variant == LiquidGlassVariant.clear ? 8.0 : 12.0;

    // Reduce blur on touch
    if (_isTouched) {
      baseBlur *= 0.7;
    }

    // Adapt to content brightness
    baseBlur *= _isDarkContent ? 0.8 : 1.0;

    return baseBlur;
  }

  double _calculateTintOpacity() {
    // Tint opacity adapts to background brightness
    final baseTintOpacity = 0.15;
    final brightnessAdaptation = _contentBrightness > 0.5 ? 1.2 : 0.8;
    return (baseTintOpacity * brightnessAdaptation).clamp(0.0, 0.3);
  }

  Color _adjustTintForContent(Color tintColor) {
    // Adjust tint based on background content - Apple's approach
    final hsl = HSLColor.fromColor(tintColor);

    if (_isDarkContent) {
      // Lighten and increase saturation for dark content
      return hsl
          .withLightness((hsl.lightness + 0.2).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation + 0.1).clamp(0.0, 1.0))
          .toColor();
    } else {
      // Darken and adjust saturation for light content
      return hsl
          .withLightness((hsl.lightness - 0.1).clamp(0.0, 1.0))
          .withSaturation((hsl.saturation * 0.9).clamp(0.0, 1.0))
          .toColor();
    }
  }
}

/// Custom painter for specular highlights
class SpecularHighlightsPainter extends CustomPainter {
  final Offset lightPosition;
  final double lightIntensity;
  final double time;
  final Color adaptiveColor;

  SpecularHighlightsPainter({
    required this.lightPosition,
    required this.lightIntensity,
    required this.time,
    required this.adaptiveColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.overlay;

    // Primary specular highlight
    final highlightCenter = Offset(
      lightPosition.dx * size.width,
      lightPosition.dy * size.height,
    );

    final highlightGradient = RadialGradient(
      center: Alignment.center,
      radius: 0.3,
      colors: [
        adaptiveColor.withOpacity(0.4 * lightIntensity),
        adaptiveColor.withOpacity(0.2 * lightIntensity),
        Colors.transparent,
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    paint.shader = highlightGradient.createShader(
      Rect.fromCircle(center: highlightCenter, radius: size.width * 0.3),
    );

    canvas.drawCircle(highlightCenter, size.width * 0.3, paint);

    // Secondary highlights for more depth
    _drawSecondaryHighlights(canvas, size, paint);
  }

  void _drawSecondaryHighlights(Canvas canvas, Size size, Paint paint) {
    // Smaller moving highlights
    for (int i = 0; i < 3; i++) {
      final angle = time + (i * 2.0);
      final radius = size.width * 0.1;
      final center = Offset(
        (lightPosition.dx + sin(angle) * 0.2) * size.width,
        (lightPosition.dy + cos(angle) * 0.15) * size.height,
      );

      final gradient = RadialGradient(
        colors: [
          adaptiveColor.withOpacity(0.2 * lightIntensity),
          Colors.transparent,
        ],
      );

      paint.shader = gradient.createShader(
        Rect.fromCircle(center: center, radius: radius),
      );

      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant SpecularHighlightsPainter oldDelegate) {
    return oldDelegate.lightPosition != lightPosition ||
        oldDelegate.lightIntensity != lightIntensity ||
        oldDelegate.time != time;
  }
}

/// Custom painter for touch glow effect
class TouchGlowPainter extends CustomPainter {
  final Offset touchPosition;
  final double radius;
  final double opacity;
  final Color color;

  TouchGlowPainter({
    required this.touchPosition,
    required this.radius,
    required this.opacity,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (opacity <= 0) return;

    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.overlay;

    final gradient = RadialGradient(
      colors: [
        color.withOpacity(opacity),
        color.withOpacity(opacity * 0.5),
        Colors.transparent,
      ],
      stops: const [0.0, 0.7, 1.0],
    );

    paint.shader = gradient.createShader(
      Rect.fromCircle(center: touchPosition, radius: radius),
    );

    canvas.drawCircle(touchPosition, radius, paint);

    // Add inner bright core
    final corePaint =
        Paint()
          ..color = color.withOpacity(opacity * 0.8)
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.screen;

    canvas.drawCircle(touchPosition, radius * 0.3, corePaint);
  }

  @override
  bool shouldRepaint(covariant TouchGlowPainter oldDelegate) {
    return oldDelegate.touchPosition != touchPosition ||
        oldDelegate.radius != radius ||
        oldDelegate.opacity != opacity;
  }
}

/// Scroll edge effect for dissolving content
class LiquidGlassScrollEdgeEffect extends StatelessWidget {
  final Widget child;
  final bool isScrolling;
  final double scrollOffset;
  final Color? backgroundColor;

  const LiquidGlassScrollEdgeEffect({
    super.key,
    required this.child,
    this.isScrolling = false,
    this.scrollOffset = 0.0,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final effectOpacity = isScrolling ? 0.8 : 0.0;

    return Stack(
      children: [
        child,
        if (isScrolling) ...[
          // Top edge fade
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 60,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    (backgroundColor ?? Colors.black).withOpacity(
                      effectOpacity,
                    ),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),

          // Bottom edge fade
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: 60,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    (backgroundColor ?? Colors.black).withOpacity(
                      effectOpacity,
                    ),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
