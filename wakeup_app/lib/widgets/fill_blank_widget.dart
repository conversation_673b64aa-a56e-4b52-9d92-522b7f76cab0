import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart'; // 正确引入QuestionInteractionState

/// 填空题组件
/// 用于填空题和计算题
class FillBlankWidget extends StatefulWidget {
  final Question question;
  final String? userAnswer;
  final Function(String?) onAnswerChanged;
  final QuestionInteractionState interactionState;
  final TextInputType keyboardType;

  const FillBlankWidget({
    Key? key,
    required this.question,
    this.userAnswer,
    required this.onAnswerChanged,
    required this.interactionState,
    this.keyboardType = TextInputType.text,
  }) : super(key: key);

  @override
  State<FillBlankWidget> createState() => _FillBlankWidgetState();
}

class _FillBlankWidgetState extends State<FillBlankWidget> {
  late TextEditingController _controller;
  bool _showAnswer = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.userAnswer);
    _showAnswer =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
  }

  @override
  void didUpdateWidget(FillBlankWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userAnswer != widget.userAnswer) {
      _controller.text = widget.userAnswer ?? '';
    }

    if (oldWidget.interactionState != widget.interactionState) {
      setState(() {
        _showAnswer =
            widget.interactionState !=
            QuestionInteractionState.waitingForSelection;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 确定是否显示正确/错误状态
    final bool isCorrect =
        widget.interactionState == QuestionInteractionState.answerCorrect;
    final bool isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final correctAnswer = widget.question.answer ?? '';

    // 样式颜色
    Color borderColor = Colors.white.withOpacity(0.3);
    if (isShowingFeedback) {
      borderColor = isCorrect ? Colors.green : Colors.red;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 输入框区域
        ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(color: borderColor, width: 2),
              ),
              child: TextField(
                controller: _controller,
                enabled: !isShowingFeedback,
                keyboardType: widget.keyboardType,
                textInputAction: TextInputAction.done,
                textCapitalization: TextCapitalization.sentences,
                autocorrect: false,
                enableSuggestions: false,
                enableIMEPersonalizedLearning: false,
                onChanged: null,
                onSubmitted: (value) {
                  widget.onAnswerChanged(value);
                  FocusScope.of(context).unfocus();
                },
                onEditingComplete: () {
                  widget.onAnswerChanged(_controller.text);
                },
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Noto Sans SC',
                ),
                decoration: InputDecoration(
                  hintText: '请在此处输入答案',
                  hintStyle: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 16,
                    fontFamily: 'Noto Sans SC',
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  border: InputBorder.none,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
