import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../widgets/answer_area_factory.dart';

/// 匹配题组件
class MatchingWidget extends StatefulWidget {
  final Question question;
  final Map<String, String>? userAnswers;
  final Function(Map<String, String>) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const MatchingWidget({
    Key? key,
    required this.question,
    this.userAnswers,
    required this.onAnswerChanged,
    required this.interactionState,
  }) : super(key: key);

  @override
  State<MatchingWidget> createState() => _MatchingWidgetState();
}

class _MatchingWidgetState extends State<MatchingWidget> {
  late Map<String, String> _userMatches;
  late List<String> _availableRightItems;
  late List<MatchingItem> _matchingItems;
  String? _selectedLeftItem;
  String? _lastSelectedItem;

  @override
  void initState() {
    super.initState();
    _initializeMatchingData();
  }

  @override
  void didUpdateWidget(MatchingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userAnswers != widget.userAnswers) {
      _initializeMatchingData(keepSelection: true);
    }

    // 重新应用最后选中的项目
    if (_lastSelectedItem != null) {
      setState(() {
        _selectedLeftItem = _lastSelectedItem;
      });
    }
  }

  @override
  void dispose() {
    _lastSelectedItem = null;
    super.dispose();
  }

  void _initializeMatchingData({bool keepSelection = false}) {
    // 初始化匹配项
    _matchingItems = widget.question.matchingItems ?? [];

    // 在初始化前先保存选中状态
    if (_selectedLeftItem != null) {
      _lastSelectedItem = _selectedLeftItem;
    }

    // 初始化用户匹配
    _userMatches = Map<String, String>.from(widget.userAnswers ?? {});

    // 获取所有右侧选项
    List<String> allRightItems =
        _matchingItems.map((item) => item.right).toList();

    // 获取剩余可用的右侧选项（尚未被匹配的）
    _availableRightItems = List<String>.from(allRightItems);
    _userMatches.values.forEach((rightItem) {
      _availableRightItems.remove(rightItem);
    });

    // 只有在不需要保持选择状态时才清除
    if (!keepSelection) {
      _selectedLeftItem = null;
      _lastSelectedItem = null;
    } else if (_lastSelectedItem != null) {
      _selectedLeftItem = _lastSelectedItem;
    }
  }

  void _updateSelectedItem(String? newSelectedItem) {
    setState(() {
      _selectedLeftItem = newSelectedItem;
      if (newSelectedItem != null) {
        _lastSelectedItem = newSelectedItem;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;

    return Column(
      children: [
        // 指导文字
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Text(
            "请将左侧项目与右侧选项匹配：",
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: 'Noto Sans SC',
            ),
          ),
        ),

        // 匹配区域 - 使用左右布局
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左右布局
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧列表
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: Text(
                            "待匹配项",
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        ...List.generate(_matchingItems.length, (index) {
                          final item = _matchingItems[index];
                          final isSelected = _selectedLeftItem == item.left;
                          final hasMatch = _userMatches.containsKey(item.left);
                          final matchedRight = _userMatches[item.left] ?? '';

                          // 评判是否匹配正确
                          bool isCorrect = false;
                          if (isShowingFeedback && hasMatch) {
                            // 找到对应的正确匹配项
                            final correctItem = _matchingItems.firstWhere(
                              (element) => element.left == item.left,
                              orElse:
                                  () =>
                                      MatchingItem(id: -1, left: '', right: ''),
                            );
                            isCorrect = correctItem.right == matchedRight;
                          }

                          return GestureDetector(
                            onTap:
                                isShowingFeedback
                                    ? null
                                    : () {
                                      setState(() {
                                        // 如果已经匹配，则取消匹配
                                        if (hasMatch) {
                                          _availableRightItems.add(
                                            _userMatches[item.left]!,
                                          );
                                          _userMatches.remove(item.left);
                                          _selectedLeftItem = null;
                                          _lastSelectedItem = null;
                                        } else {
                                          // 选择新项目或取消选择
                                          String? newSelection =
                                              _selectedLeftItem == item.left
                                                  ? null
                                                  : item.left;
                                          _selectedLeftItem = newSelection;
                                          _lastSelectedItem = newSelection;
                                        }
                                      });
                                    },
                            child: Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              padding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? Colors.purple.withOpacity(0.3)
                                        : Colors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color:
                                      isSelected
                                          ? Colors.purple.shade300
                                          : isShowingFeedback && hasMatch
                                          ? isCorrect
                                              ? Colors.green
                                              : Colors.red
                                          : Colors.white.withOpacity(0.2),
                                  width: 2,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      item.left,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 16,
                                        fontFamily: 'Noto Sans SC',
                                      ),
                                    ),
                                  ),
                                  if (hasMatch)
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            isShowingFeedback
                                                ? isCorrect
                                                    ? Colors.green.withOpacity(
                                                      0.2,
                                                    )
                                                    : Colors.red.withOpacity(
                                                      0.2,
                                                    )
                                                : Colors.purple.withOpacity(
                                                  0.2,
                                                ),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color:
                                              isShowingFeedback
                                                  ? isCorrect
                                                      ? Colors.green
                                                      : Colors.red
                                                  : Colors.purple.shade200,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            matchedRight,
                                            style: TextStyle(
                                              color:
                                                  isShowingFeedback
                                                      ? isCorrect
                                                          ? Colors.green
                                                          : Colors.red
                                                      : Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14,
                                              fontFamily: 'Noto Sans SC',
                                            ),
                                          ),
                                          if (!isShowingFeedback)
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                left: 6,
                                              ),
                                              child: Icon(
                                                CupertinoIcons.xmark_circle,
                                                color: Colors.white.withOpacity(
                                                  0.8,
                                                ),
                                                size: 16,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  if (isShowingFeedback && !hasMatch)
                                    Padding(
                                      padding: const EdgeInsets.only(left: 8),
                                      child: Icon(
                                        CupertinoIcons.xmark_circle_fill,
                                        color: Colors.red,
                                        size: 18,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),

                  // 中间分隔线
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    width: 1,
                    height: _matchingItems.length > 3 ? 350 : 200,
                    color: Colors.white.withOpacity(0.3),
                  ),

                  // 右侧选项列表
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: Text(
                            "可选答案",
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (!isShowingFeedback)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children:
                                _availableRightItems.map((rightItem) {
                                  return GestureDetector(
                                    onTap:
                                        _selectedLeftItem != null
                                            ? () {
                                              // 保存当前选中项，确保不会被清除
                                              final currentSelected =
                                                  _selectedLeftItem;

                                              setState(() {
                                                // 记录匹配关系
                                                _userMatches[currentSelected!] =
                                                    rightItem;

                                                // 从可用选项中移除
                                                _availableRightItems.remove(
                                                  rightItem,
                                                );

                                                // 通知父组件
                                                widget.onAnswerChanged(
                                                  _userMatches,
                                                );

                                                // 关键修复：确保选中状态在匹配完成后不会被清除
                                                _lastSelectedItem =
                                                    currentSelected;
                                                // 保持选中状态
                                                _selectedLeftItem =
                                                    currentSelected;
                                              });
                                            }
                                            : null,
                                    child: Container(
                                      width: double.infinity,
                                      margin: const EdgeInsets.only(bottom: 8),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 12,
                                        horizontal: 16,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            _selectedLeftItem != null
                                                ? Colors.purple.withOpacity(0.3)
                                                : Colors.black.withOpacity(0.5),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color:
                                              _selectedLeftItem != null
                                                  ? Colors.purple.shade300
                                                  : Colors.white.withOpacity(
                                                    0.2,
                                                  ),
                                          width: 2,
                                        ),
                                      ),
                                      child: Text(
                                        rightItem,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontFamily: 'Noto Sans SC',
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
