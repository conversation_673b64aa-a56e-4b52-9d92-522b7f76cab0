import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../widgets/answer_area_factory.dart';

/// 排序题组件
class OrderingWidget extends StatefulWidget {
  final Question question;
  final List<OrderingItem>? userOrder;
  final Function(List<OrderingItem>) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const OrderingWidget({
    Key? key,
    required this.question,
    this.userOrder,
    required this.onAnswerChanged,
    required this.interactionState,
  }) : super(key: key);

  @override
  State<OrderingWidget> createState() => _OrderingWidgetState();
}

class _OrderingWidgetState extends State<OrderingWidget> {
  late List<OrderingItem> _userOrderItems;

  @override
  void initState() {
    super.initState();
    _initializeOrderingData();
  }

  @override
  void didUpdateWidget(OrderingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userOrder != widget.userOrder) {
      _initializeOrderingData();
    }
  }

  void _initializeOrderingData() {
    // 如果已经有用户排序，则使用；否则使用打乱后的题目排序项
    if (widget.userOrder != null && widget.userOrder!.isNotEmpty) {
      _userOrderItems = List<OrderingItem>.from(widget.userOrder!);
    } else {
      // 获取题目的排序项，并随机打乱顺序
      _userOrderItems = List<OrderingItem>.from(
        widget.question.orderingItems ?? [],
      )..shuffle();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final orderedItems = List<OrderingItem>.from(_userOrderItems);

    // 获取正确排序的项目（用于显示答案）
    final correctItems = List<OrderingItem>.from(
      widget.question.orderingItems ?? [],
    )..sort((a, b) => a.correctOrder.compareTo(b.correctOrder));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Text(
            "请拖拽下列选项，按正确顺序排列：",
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: 'Noto Sans SC',
            ),
          ),
        ),

        // 排序区域
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(16),
          ),
          child: ReorderableListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _userOrderItems.length,
            onReorder: (int oldIndex, int newIndex) {
              // 只在等待选择状态下允许排序
              if (widget.interactionState ==
                  QuestionInteractionState.waitingForSelection) {
                setState(() {
                  if (oldIndex < newIndex) {
                    newIndex -= 1;
                  }
                  final item = _userOrderItems.removeAt(oldIndex);
                  _userOrderItems.insert(newIndex, item);

                  // 通知父组件
                  widget.onAnswerChanged(_userOrderItems);
                });
              }
            },
            itemBuilder: (context, index) {
              final item = _userOrderItems[index];

              // 判断当前位置是否正确
              bool isCorrect = false;
              if (isShowingFeedback) {
                isCorrect = item.correctOrder == index + 1;
              }

              // 边框颜色
              Color borderColor = Colors.white.withOpacity(0.2);
              if (isShowingFeedback) {
                borderColor = isCorrect ? Colors.green : Colors.red;
              }

              return Container(
                key: ValueKey(item.id),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: borderColor, width: 2),
                ),
                child: ListTile(
                  leading: Container(
                    width: 32,
                    height: 32,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.3),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.purple.shade300,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        fontFamily: 'Noto Sans SC',
                      ),
                    ),
                  ),
                  title: Text(
                    item.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Noto Sans SC',
                    ),
                  ),
                  trailing:
                      isShowingFeedback
                          ? Icon(
                            isCorrect
                                ? CupertinoIcons.check_mark_circled_solid
                                : CupertinoIcons.xmark_circle_fill,
                            color: isCorrect ? Colors.green : Colors.red,
                            size: 24,
                          )
                          : const Icon(CupertinoIcons.bars, color: Colors.grey),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
