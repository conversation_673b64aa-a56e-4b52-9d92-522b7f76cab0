import 'dart:math';
import 'dart:ui';

/// iOS 26 液态玻璃物理引擎
/// 模拟真实液体的物理特性：粘滞度、表面张力、折射等
class LiquidPhysicsEngine {
  // 物理参数
  double viscosity; // 粘滞度 (0.1-2.0)
  double refractionIndex; // 折射率 (1.0-1.8)
  double surfaceTension; // 表面张力 (0.0-1.0)
  double opticalDensity; // 光学密度 (0.0-1.0)
  double gravity; // 重力影响 (0.0-1.0)
  
  // 动态状态
  List<LiquidParticle> particles;
  List<LiquidWave> waves;
  Vector2 deviceTilt;
  DateTime lastUpdate;
  
  LiquidPhysicsEngine({
    this.viscosity = 1.2,
    this.refractionIndex = 1.33,
    this.surfaceTension = 0.6,
    this.opticalDensity = 0.85,
    this.gravity = 0.3,
  }) : particles = [],
       waves = [],
       deviceTilt = Vector2.zero(),
       lastUpdate = DateTime.now();

  /// 更新物理系统
  void update(Duration deltaTime) {
    final dt = deltaTime.inMicroseconds / 1000000.0;
    
    // 更新粒子
    _updateParticles(dt);
    
    // 更新波浪
    _updateWaves(dt);
    
    // 清理过期效果
    _cleanup();
    
    lastUpdate = DateTime.now();
  }

  /// 添加触摸扰动
  void addTouchDisturbance(Offset position, double intensity) {
    // 创建触摸波浪
    waves.add(LiquidWave(
      center: Vector2(position.dx, position.dy),
      intensity: intensity * (1.0 - viscosity * 0.3),
      frequency: _calculateTouchFrequency(intensity),
      amplitude: _calculateTouchAmplitude(intensity),
      decay: viscosity * 0.5 + 0.3,
    ));
    
    // 生成粒子效果
    _generateTouchParticles(position, intensity);
  }

  /// 更新设备倾斜
  void updateDeviceTilt(double x, double y) {
    deviceTilt = Vector2(x * gravity, y * gravity);
    
    // 倾斜时生成流动效果
    if (deviceTilt.magnitude > 0.1) {
      _generateTiltParticles();
    }
  }

  /// 计算光线折射
  Vector2 calculateRefraction(Vector2 incident, Vector2 normal) {
    final n1 = 1.0; // 空气折射率
    final n2 = refractionIndex;
    final ratio = n1 / n2;
    
    final cosI = -normal.dot(incident);
    final sinT2 = ratio * ratio * (1.0 - cosI * cosI);
    
    if (sinT2 > 1.0) {
      // 全内反射
      return _calculateReflection(incident, normal);
    }
    
    final cosT = sqrt(1.0 - sinT2);
    return incident * ratio + normal * (ratio * cosI - cosT);
  }

  /// 计算菲涅尔反射
  double calculateFresnelReflectance(Vector2 incident, Vector2 normal) {
    final cosI = -normal.dot(incident);
    final n1 = 1.0;
    final n2 = refractionIndex;
    
    final sinT2 = (n1 / n2) * (n1 / n2) * (1.0 - cosI * cosI);
    
    if (sinT2 > 1.0) return 1.0; // 全内反射
    
    final cosT = sqrt(1.0 - sinT2);
    final rs = ((n1 * cosI - n2 * cosT) / (n1 * cosI + n2 * cosT));
    final rp = ((n2 * cosI - n1 * cosT) / (n2 * cosI + n1 * cosT));
    
    return (rs * rs + rp * rp) * 0.5;
  }

  /// 计算表面张力效果
  double calculateSurfaceTension(Vector2 position, List<Vector2> neighborPositions) {
    double tension = 0.0;
    
    for (final neighbor in neighborPositions) {
      final distance = (position - neighbor).magnitude;
      if (distance > 0 && distance < 50.0) {
        tension += surfaceTension / (distance * distance + 1.0);
      }
    }
    
    return min(tension, 1.0);
  }

  /// 获取当前的视觉扭曲强度
  double getDistortionIntensity(Vector2 position) {
    double intensity = 0.0;
    
    // 来自波浪的扭曲
    for (final wave in waves) {
      final distance = (position - wave.center).magnitude;
      if (distance < wave.radius) {
        final normalizedDistance = distance / wave.radius;
        final waveContribution = wave.amplitude * 
            sin(normalizedDistance * wave.frequency + wave.phase) *
            exp(-normalizedDistance * wave.decay);
        intensity += waveContribution;
      }
    }
    
    // 来自设备倾斜的扭曲
    intensity += deviceTilt.magnitude * 0.2;
    
    // 基于粘滞度的平滑
    intensity *= (2.0 - viscosity) * 0.5;
    
    return clampDouble(intensity, -1.0, 1.0);
  }

  /// 获取光学属性
  OpticalProperties getOpticalProperties(Vector2 position) {
    final distortion = getDistortionIntensity(position);
    final localDensity = _calculateLocalDensity(position);
    
    return OpticalProperties(
      refractionIndex: refractionIndex * (0.9 + localDensity * 0.2),
      transparency: opticalDensity * (0.8 + distortion.abs() * 0.3),
      blur: viscosity * 2.0 + distortion.abs() * 1.5,
      dispersion: _calculateDispersion(position),
    );
  }

  // 私有方法

  void _updateParticles(double dt) {
    for (int i = particles.length - 1; i >= 0; i--) {
      final particle = particles[i];
      
      // 应用重力和设备倾斜
      particle.velocity += (Vector2(0, 100) + deviceTilt * 50) * dt;
      
      // 应用粘滞阻力
      particle.velocity *= pow(1.0 - viscosity * 0.1, dt * 60).toDouble();
      
      // 更新位置
      particle.position += particle.velocity * dt;
      
      // 更新生命周期
      particle.life -= dt;
      
      // 移除死亡粒子
      if (particle.life <= 0) {
        particles.removeAt(i);
      }
    }
  }

  void _updateWaves(double dt) {
    for (int i = waves.length - 1; i >= 0; i--) {
      final wave = waves[i];
      
      // 更新波浪参数
      wave.phase += wave.frequency * dt * 2 * pi;
      wave.radius += wave.speed * dt;
      wave.amplitude *= pow(1.0 - wave.decay, dt * 60);
      wave.life -= dt;
      
      // 移除死亡波浪
      if (wave.life <= 0 || wave.amplitude < 0.01) {
        waves.removeAt(i);
      }
    }
  }

  void _cleanup() {
    // 限制粒子数量以保持性能
    if (particles.length > 100) {
      particles.removeRange(0, particles.length - 100);
    }
    
    // 限制波浪数量
    if (waves.length > 20) {
      waves.removeRange(0, waves.length - 20);
    }
  }

  double _calculateTouchFrequency(double intensity) {
    return 3.0 + intensity * 2.0;
  }

  double _calculateTouchAmplitude(double intensity) {
    return intensity * 0.5 * (2.0 - viscosity);
  }

  void _generateTouchParticles(Offset position, double intensity) {
    final particleCount = (intensity * 10).round();
    
    for (int i = 0; i < particleCount; i++) {
      final angle = (i / particleCount) * 2 * pi;
      final speed = 50 + intensity * 100;
      
      particles.add(LiquidParticle(
        position: Vector2(position.dx, position.dy),
        velocity: Vector2(cos(angle), sin(angle)) * speed,
        size: 2.0 + intensity * 3.0,
        life: 1.0 + intensity,
        opacity: 0.3 + intensity * 0.4,
      ));
    }
  }

  void _generateTiltParticles() {
    if (particles.length < 50 && Random().nextDouble() < 0.3) {
      final randomX = Random().nextDouble() * 400;
      final randomY = Random().nextDouble() * 800;
      
      particles.add(LiquidParticle(
        position: Vector2(randomX, randomY),
        velocity: deviceTilt * 20,
        size: 1.0 + Random().nextDouble() * 2.0,
        life: 2.0 + Random().nextDouble() * 3.0,
        opacity: 0.1 + Random().nextDouble() * 0.2,
      ));
    }
  }

  Vector2 _calculateReflection(Vector2 incident, Vector2 normal) {
    return incident - normal * (2.0 * normal.dot(incident));
  }

  double _calculateLocalDensity(Vector2 position) {
    double density = 0.0;
    
    for (final particle in particles) {
      final distance = (position - particle.position).magnitude;
      if (distance < 30.0) {
        density += particle.opacity / (distance + 1.0);
      }
    }
    
    return min(density, 1.0);
  }

  double _calculateDispersion(Vector2 position) {
    // 基于位置和当前状态计算色散
    final waveInfluence = waves.fold(0.0, (sum, wave) {
      final distance = (position - wave.center).magnitude;
      return sum + (wave.amplitude * exp(-distance * 0.01));
    });
    
    return clampDouble(waveInfluence * 0.3, 0.0, 0.5);
  }
}

/// 液态粒子
class LiquidParticle {
  Vector2 position;
  Vector2 velocity;
  double size;
  double life;
  double opacity;

  LiquidParticle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.life,
    required this.opacity,
  });
}

/// 液态波浪
class LiquidWave {
  Vector2 center;
  double intensity;
  double frequency;
  double amplitude;
  double decay;
  double radius;
  double speed;
  double phase;
  double life;

  LiquidWave({
    required this.center,
    required this.intensity,
    required this.frequency,
    required this.amplitude,
    required this.decay,
  }) : radius = 0.0,
       speed = 100.0,
       phase = 0.0,
       life = 3.0;
}

/// 光学属性
class OpticalProperties {
  final double refractionIndex;
  final double transparency;
  final double blur;
  final double dispersion;

  OpticalProperties({
    required this.refractionIndex,
    required this.transparency,
    required this.blur,
    required this.dispersion,
  });
}

/// 2D向量工具类
class Vector2 {
  double x;
  double y;

  Vector2(this.x, this.y);

  Vector2.zero() : x = 0.0, y = 0.0;

  double get magnitude => sqrt(x * x + y * y);

  Vector2 operator +(Vector2 other) => Vector2(x + other.x, y + other.y);
  Vector2 operator -(Vector2 other) => Vector2(x - other.x, y - other.y);
  Vector2 operator *(double scalar) => Vector2(x * scalar, y * scalar);

  double dot(Vector2 other) => x * other.x + y * other.y;

  Vector2 normalized() {
    final mag = magnitude;
    return mag > 0 ? Vector2(x / mag, y / mag) : Vector2.zero();
  }
}