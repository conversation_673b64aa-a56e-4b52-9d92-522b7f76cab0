import 'dart:math';
import 'dart:ui';
import 'package:flutter/material.dart';

/// iOS 26 微光粒子系统
/// 模拟液态玻璃中的光散射和粒子效果
class ParticleSystem {
  List<LightParticle> _particles = [];
  List<CausticLight> _causticLights = [];
  Random _random = Random();
  
  // 系统参数
  int maxParticles;
  int maxCaustics;
  double particleSpawnRate;
  double causticSpawnRate;
  Size screenSize;
  
  // 环境参数
  double _ambientBrightness = 0.5;
  Color _dominantColor = Colors.white;
  double _liquidDensity = 0.8;
  
  ParticleSystem({
    this.maxParticles = 150,
    this.maxCaustics = 20,
    this.particleSpawnRate = 0.3,
    this.causticSpawnRate = 0.1,
    this.screenSize = const Size(400, 800),
  });

  /// 更新粒子系统
  void update(Duration deltaTime, {
    double? ambientBrightness,
    Color? dominantColor,
    double? liquidDensity,
    List<Offset>? lightSources,
    double? motionIntensity,
  }) {
    final dt = deltaTime.inMicroseconds / 1000000.0;
    
    // 更新环境参数
    if (ambientBrightness != null) _ambientBrightness = ambientBrightness;
    if (dominantColor != null) _dominantColor = dominantColor;
    if (liquidDensity != null) _liquidDensity = liquidDensity;
    
    // 更新现有粒子
    _updateParticles(dt, motionIntensity ?? 0.0);
    
    // 更新焦散光
    _updateCausticLights(dt, lightSources ?? []);
    
    // 生成新粒子
    _spawnParticles(dt, motionIntensity ?? 0.0);
    
    // 生成焦散光
    _spawnCausticLights(dt, lightSources ?? []);
    
    // 清理过期效果
    _cleanup();
  }

  /// 添加触摸光源
  void addTouchLight(Offset position, double intensity) {
    // 在触摸点生成爆发性光粒子
    final particleCount = (intensity * 20).round();
    
    for (int i = 0; i < particleCount; i++) {
      final angle = (_random.nextDouble() * 2 * pi);
      final speed = 20 + _random.nextDouble() * 100 * intensity;
      final size = 1.0 + _random.nextDouble() * 3.0 * intensity;
      
      _particles.add(LightParticle(
        position: Vector2(position.dx, position.dy),
        velocity: Vector2(cos(angle), sin(angle)) * speed,
        size: size,
        brightness: 0.6 + intensity * 0.4,
        color: _calculateLightColor(position),
        life: 1.0 + _random.nextDouble() * 2.0,
        particleType: LightParticleType.touch,
      ));
    }
    
    // 生成焦散光效果
    _causticLights.add(CausticLight(
      center: Vector2(position.dx, position.dy),
      intensity: intensity,
      radius: 30 + intensity * 50,
      patterns: _generateCausticPatterns(3),
      life: 2.0 + intensity,
      color: _calculateCausticColor(position),
    ));
  }

  /// 添加重力影响的光粒子
  void addGravityParticles(Vector2 gravityDirection, double intensity) {
    if (_particles.length < maxParticles * 0.7) {
      // 根据重力方向生成流动光粒子
      final spawnCount = (intensity * 5).round();
      
      for (int i = 0; i < spawnCount; i++) {
        final spawnX = _random.nextDouble() * screenSize.width;
        final spawnY = _random.nextDouble() * screenSize.height;
        
        _particles.add(LightParticle(
          position: Vector2(spawnX, spawnY),
          velocity: gravityDirection * (10 + _random.nextDouble() * 30),
          size: 0.5 + _random.nextDouble() * 1.5,
          brightness: 0.2 + _random.nextDouble() * 0.3,
          color: _dominantColor.withOpacity(0.4),
          life: 3.0 + _random.nextDouble() * 5.0,
          particleType: LightParticleType.gravity,
        ));
      }
    }
  }

  /// 渲染粒子系统
  void paint(Canvas canvas, Size size) {
    // 渲染焦散光 (底层)
    for (final caustic in _causticLights) {
      _paintCausticLight(canvas, caustic);
    }
    
    // 渲染光粒子 (上层)
    for (final particle in _particles) {
      _paintLightParticle(canvas, particle);
    }
  }

  /// 获取粒子数量统计
  ParticleStats getStats() {
    final touchParticles = _particles.where((p) => p.particleType == LightParticleType.touch).length;
    final ambientParticles = _particles.where((p) => p.particleType == LightParticleType.ambient).length;
    final gravityParticles = _particles.where((p) => p.particleType == LightParticleType.gravity).length;
    
    return ParticleStats(
      totalParticles: _particles.length,
      touchParticles: touchParticles,
      ambientParticles: ambientParticles,
      gravityParticles: gravityParticles,
      causticLights: _causticLights.length,
    );
  }

  // 私有方法

  void _updateParticles(double dt, double motionIntensity) {
    for (int i = _particles.length - 1; i >= 0; i--) {
      final particle = _particles[i];
      
      // 基本物理更新
      particle.position += particle.velocity * dt;
      particle.life -= dt;
      
      // 基于液体密度的阻力
      final dragFactor = 1.0 - (_liquidDensity * 0.1 * dt);
      particle.velocity *= pow(dragFactor, dt * 60).toDouble();
      
      // 基于运动的亮度调制
      particle.brightness *= (1.0 + motionIntensity * 0.2);
      
      // 边界处理 - 粒子在边缘反弹或消失
      if (particle.position.x < 0 || particle.position.x > screenSize.width) {
        particle.velocity.x *= -0.5;
      }
      if (particle.position.y < 0 || particle.position.y > screenSize.height) {
        particle.velocity.y *= -0.5;
      }
      
      // 移除死亡粒子
      if (particle.life <= 0 || particle.brightness < 0.01) {
        _particles.removeAt(i);
      }
    }
  }

  void _updateCausticLights(double dt, List<Offset> lightSources) {
    for (int i = _causticLights.length - 1; i >= 0; i--) {
      final caustic = _causticLights[i];
      
      // 更新焦散光动画
      caustic.animationPhase += dt * 2.0;
      caustic.radius += dt * 20.0;
      caustic.intensity *= pow(0.95, dt * 60);
      caustic.life -= dt;
      
      // 根据光源位置调整强度
      double lightInfluence = 0.0;
      for (final lightSource in lightSources) {
        final distance = (caustic.center - Vector2(lightSource.dx, lightSource.dy)).magnitude;
        lightInfluence += 1.0 / (distance * 0.01 + 1.0);
      }
      caustic.intensity *= (0.8 + lightInfluence * 0.4);
      
      // 移除死亡焦散光
      if (caustic.life <= 0 || caustic.intensity < 0.05) {
        _causticLights.removeAt(i);
      }
    }
  }

  void _spawnParticles(double dt, double motionIntensity) {
    // 环境粒子生成
    if (_random.nextDouble() < particleSpawnRate * dt * _ambientBrightness) {
      if (_particles.length < maxParticles) {
        _particles.add(_createAmbientParticle());
      }
    }
    
    // 运动相关粒子生成
    if (motionIntensity > 0.1 && _random.nextDouble() < motionIntensity * dt) {
      if (_particles.length < maxParticles) {
        _particles.add(_createMotionParticle(motionIntensity));
      }
    }
  }

  void _spawnCausticLights(double dt, List<Offset> lightSources) {
    if (_causticLights.length < maxCaustics && 
        _random.nextDouble() < causticSpawnRate * dt * _ambientBrightness) {
      
      final position = lightSources.isNotEmpty 
          ? lightSources[_random.nextInt(lightSources.length)]
          : Offset(_random.nextDouble() * screenSize.width, 
                   _random.nextDouble() * screenSize.height);
      
      _causticLights.add(CausticLight(
        center: Vector2(position.dx, position.dy),
        intensity: 0.3 + _random.nextDouble() * 0.4,
        radius: 20 + _random.nextDouble() * 40,
        patterns: _generateCausticPatterns(2 + _random.nextInt(4)),
        life: 2.0 + _random.nextDouble() * 4.0,
        color: _calculateCausticColor(position),
      ));
    }
  }

  LightParticle _createAmbientParticle() {
    return LightParticle(
      position: Vector2(
        _random.nextDouble() * screenSize.width,
        _random.nextDouble() * screenSize.height,
      ),
      velocity: Vector2(
        (_random.nextDouble() - 0.5) * 20,
        (_random.nextDouble() - 0.5) * 20,
      ),
      size: 0.5 + _random.nextDouble() * 1.0,
      brightness: 0.1 + _random.nextDouble() * 0.3,
      color: _dominantColor.withOpacity(0.3),
      life: 5.0 + _random.nextDouble() * 10.0,
      particleType: LightParticleType.ambient,
    );
  }

  LightParticle _createMotionParticle(double intensity) {
    return LightParticle(
      position: Vector2(
        _random.nextDouble() * screenSize.width,
        _random.nextDouble() * screenSize.height,
      ),
      velocity: Vector2(
        (_random.nextDouble() - 0.5) * 50 * intensity,
        (_random.nextDouble() - 0.5) * 50 * intensity,
      ),
      size: 1.0 + _random.nextDouble() * 2.0 * intensity,
      brightness: 0.3 + intensity * 0.4,
      color: _calculateMotionColor(intensity),
      life: 2.0 + _random.nextDouble() * 3.0,
      particleType: LightParticleType.gravity,
    );
  }

  void _cleanup() {
    // 限制粒子数量以保持性能
    if (_particles.length > maxParticles) {
      _particles.removeRange(0, _particles.length - maxParticles);
    }
    
    if (_causticLights.length > maxCaustics) {
      _causticLights.removeRange(0, _causticLights.length - maxCaustics);
    }
  }

  Color _calculateLightColor(Offset position) {
    // 基于位置和环境生成颜色
    final hue = (position.dx / screenSize.width) * 60 + 200; // 蓝-紫色范围
    return HSLColor.fromAHSL(0.6, hue, 0.7, 0.6 + _ambientBrightness * 0.3).toColor();
  }

  Color _calculateCausticColor(Offset position) {
    // 焦散光颜色，偏向暖色
    final hue = (position.dy / screenSize.height) * 60 + 45; // 橙-黄色范围
    return HSLColor.fromAHSL(0.4, hue, 0.8, 0.7).toColor();
  }

  Color _calculateMotionColor(double intensity) {
    // 运动粒子颜色，随强度变化
    final hue = 180 + intensity * 60; // 青-蓝色范围
    return HSLColor.fromAHSL(0.5, hue, 0.9, 0.5 + intensity * 0.3).toColor();
  }

  List<CausticPattern> _generateCausticPatterns(int count) {
    List<CausticPattern> patterns = [];
    
    for (int i = 0; i < count; i++) {
      patterns.add(CausticPattern(
        angle: _random.nextDouble() * 2 * pi,
        length: 10 + _random.nextDouble() * 20,
        width: 1 + _random.nextDouble() * 3,
        intensity: 0.3 + _random.nextDouble() * 0.7,
        speed: 0.5 + _random.nextDouble() * 1.5,
      ));
    }
    
    return patterns;
  }

  void _paintLightParticle(Canvas canvas, LightParticle particle) {
    final paint = Paint()
      ..color = particle.color.withOpacity(particle.brightness * (particle.life / particle.maxLife))
      ..style = PaintingStyle.fill;
    
    // 根据粒子类型选择不同的渲染方式
    switch (particle.particleType) {
      case LightParticleType.touch:
        // 触摸粒子 - 发光效果
        final gradient = RadialGradient(
          colors: [
            particle.color.withOpacity(particle.brightness),
            particle.color.withOpacity(0.0),
          ],
        );
        paint.shader = gradient.createShader(
          Rect.fromCircle(
            center: Offset(particle.position.x, particle.position.y),
            radius: particle.size * 2,
          ),
        );
        canvas.drawCircle(
          Offset(particle.position.x, particle.position.y),
          particle.size * 2,
          paint,
        );
        break;
      
      case LightParticleType.ambient:
        // 环境粒子 - 简单圆点
        canvas.drawCircle(
          Offset(particle.position.x, particle.position.y),
          particle.size,
          paint,
        );
        break;
      
      case LightParticleType.gravity:
        // 重力粒子 - 拖尾效果
        final tail = Offset(
          particle.position.x - particle.velocity.x * 0.1,
          particle.position.y - particle.velocity.y * 0.1,
        );
        paint.strokeCap = StrokeCap.round;
        paint.strokeWidth = particle.size;
        paint.style = PaintingStyle.stroke;
        canvas.drawLine(
          tail,
          Offset(particle.position.x, particle.position.y),
          paint,
        );
        break;
    }
  }

  void _paintCausticLight(Canvas canvas, CausticLight caustic) {
    final center = Offset(caustic.center.x, caustic.center.y);
    
    for (final pattern in caustic.patterns) {
      final paint = Paint()
        ..color = caustic.color.withOpacity(caustic.intensity * pattern.intensity)
        ..strokeWidth = pattern.width
        ..strokeCap = StrokeCap.round
        ..style = PaintingStyle.stroke;
      
      final angle = pattern.angle + caustic.animationPhase * pattern.speed;
      final endX = center.dx + cos(angle) * pattern.length;
      final endY = center.dy + sin(angle) * pattern.length;
      
      canvas.drawLine(center, Offset(endX, endY), paint);
    }
  }
}

/// 光粒子类
class LightParticle {
  Vector2 position;
  Vector2 velocity;
  double size;
  double brightness;
  Color color;
  double life;
  double maxLife;
  LightParticleType particleType;

  LightParticle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.brightness,
    required this.color,
    required this.life,
    required this.particleType,
  }) : maxLife = life;
}

/// 焦散光类
class CausticLight {
  Vector2 center;
  double intensity;
  double radius;
  List<CausticPattern> patterns;
  double life;
  Color color;
  double animationPhase;

  CausticLight({
    required this.center,
    required this.intensity,
    required this.radius,
    required this.patterns,
    required this.life,
    required this.color,
  }) : animationPhase = 0.0;
}

/// 焦散光图案
class CausticPattern {
  double angle;
  double length;
  double width;
  double intensity;
  double speed;

  CausticPattern({
    required this.angle,
    required this.length,
    required this.width,
    required this.intensity,
    required this.speed,
  });
}

/// 粒子类型枚举
enum LightParticleType {
  touch,    // 触摸产生的粒子
  ambient,  // 环境粒子
  gravity,  // 重力影响的粒子
}

/// 粒子统计信息
class ParticleStats {
  final int totalParticles;
  final int touchParticles;
  final int ambientParticles;
  final int gravityParticles;
  final int causticLights;

  ParticleStats({
    required this.totalParticles,
    required this.touchParticles,
    required this.ambientParticles,
    required this.gravityParticles,
    required this.causticLights,
  });
}

/// 2D向量类 (复用)
class Vector2 {
  double x;
  double y;

  Vector2(this.x, this.y);

  Vector2.zero() : x = 0.0, y = 0.0;

  double get magnitude => sqrt(x * x + y * y);

  Vector2 operator +(Vector2 other) => Vector2(x + other.x, y + other.y);
  Vector2 operator -(Vector2 other) => Vector2(x - other.x, y - other.y);
  Vector2 operator *(double scalar) => Vector2(x * scalar, y * scalar);
}