import 'package:flutter/material.dart';
import '../pages/auth/login_page.dart';

/// 导航助手类，提供全局导航功能
class NavigationHelper {
  /// 导航到登录页面，清除导航栈
  static void navigateToLogin(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  /// 导航到主页，清除导航栈
  static void navigateToMain(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil('/main', (route) => false);
  }

  /// 当用户登出时，返回到登录页面
  static void handleLogout(BuildContext context) {
    navigateToLogin(context);
  }
}
