import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/api_cache_provider.dart';
import '../providers/user_provider.dart';
import '../services/auth_service.dart';

/// 后台异步加载工具类
/// 用于在UI完成初始渲染后，异步加载数据，不阻塞主UI线程
class BackgroundLoader {
  /// 在页面完成渲染后执行异步任务
  static void runAfterBuild(
    BuildContext context,
    Future<void> Function() task,
  ) {
    // 确保下一帧渲染后执行，不阻塞初始UI构建
    WidgetsBinding.instance.addPostFrameCallback((_) {
      task();
    });
  }

  /// 异步加载API数据，不阻塞UI
  static Future<Map<String, dynamic>?> loadApiData({
    required BuildContext context,
    required String endpoint,
    bool useMemoryCache = true,
    bool usePersistentCache = false,
    bool forceRefresh = false,
    Map<String, String>? extraHeaders,
    void Function(Map<String, dynamic>)? onSuccess,
    void Function(String)? onError,
  }) async {
    try {
      // 获取API缓存服务实例
      final apiCacheProvider = Provider.of<ApiCacheProvider>(
        context,
        listen: false,
      );
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // 准备授权头
      Map<String, String> headers = extraHeaders ?? {};
      if (userProvider.isLoggedIn) {
        headers['Authorization'] = 'Bearer ${userProvider.token}';
      }

      // 获取数据
      final response = await apiCacheProvider.getWithCache(
        endpoint,
        headers: headers,
        useMemoryCache: useMemoryCache,
        usePersistentCache: usePersistentCache,
        forceRefresh: forceRefresh,
      );

      // 处理成功回调
      if (onSuccess != null) {
        onSuccess(response);
      }

      return response;
    } catch (e) {
      // 处理错误回调
      if (onError != null) {
        onError(e.toString());
      }

      // 记录到控制台但不打断应用流程
      print('API数据加载失败: $endpoint - $e');
      return null;
    }
  }

  /// 后台批量并发加载多个API数据
  static Future<List<Map<String, dynamic>?>> loadMultipleApis({
    required BuildContext context,
    required List<String> endpoints,
    bool useMemoryCache = true,
    bool usePersistentCache = false,
    void Function(List<Map<String, dynamic>?>)? onAllComplete,
  }) async {
    try {
      // 获取API缓存服务实例
      final apiCacheProvider = Provider.of<ApiCacheProvider>(
        context,
        listen: false,
      );
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // 准备授权头
      Map<String, String> headers = {};
      if (userProvider.isLoggedIn) {
        headers['Authorization'] = 'Bearer ${userProvider.token}';
      }

      // 创建单独的函数，确保类型安全
      Future<Map<String, dynamic>?> fetchEndpoint(String endpoint) async {
        try {
          return await apiCacheProvider.getWithCache(
            endpoint,
            headers: headers,
            useMemoryCache: useMemoryCache,
            usePersistentCache: usePersistentCache,
          );
        } catch (e) {
          print('API加载失败: $endpoint - $e');
          return null;
        }
      }

      // 并发请求所有API
      final futures =
          endpoints.map((endpoint) => fetchEndpoint(endpoint)).toList();

      // 等待所有请求完成
      final results = await Future.wait(futures);

      // 处理成功回调
      if (onAllComplete != null) {
        onAllComplete(results);
      }

      return results;
    } catch (e) {
      print('批量API加载失败: $e');
      return List<Map<String, dynamic>?>.filled(endpoints.length, null);
    }
  }

  /// 预热缓存，在应用启动时调用
  static Future<void> preloadCommonData(BuildContext context) async {
    // 获取常用数据API路径
    final commonApis = [
      '${AuthService.baseUrl}/api/config',
      '${AuthService.baseUrl}/api/courses',
      '${AuthService.baseUrl}/api/categories/level1',
    ];

    // 后台异步加载
    runAfterBuild(context, () async {
      await loadMultipleApis(
        context: context,
        endpoints: commonApis,
        useMemoryCache: true,
        usePersistentCache: true,
      );

      print('应用常用数据预热完成');
    });
  }
}
