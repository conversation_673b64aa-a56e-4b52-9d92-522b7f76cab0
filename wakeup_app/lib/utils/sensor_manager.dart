import 'dart:async';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:sensors_plus/sensors_plus.dart';

/// 传感器管理器 - 处理设备运动和环境感知
/// 为液态玻璃效果提供真实的物理响应
class SensorManager {
  // 单例模式
  static final SensorManager _instance = SensorManager._internal();
  factory SensorManager() => _instance;
  SensorManager._internal();

  // 传感器数据流
  StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  StreamSubscription<GyroscopeEvent>? _gyroscopeSubscription;
  StreamSubscription<MagnetometerEvent>? _magnetometerSubscription;

  // 当前传感器数据
  AccelerometerEvent? _lastAccelerometerEvent;
  GyroscopeEvent? _lastGyroscopeEvent;
  MagnetometerEvent? _lastMagnetometerEvent;

  // 过滤和平滑参数
  final double _filterAlpha = 0.8; // 低通滤波器系数
  final double _motionThreshold = 0.1; // 运动检测阈值
  final double _rotationThreshold = 0.05; // 旋转检测阈值

  // 平滑后的数据
  Vector3 _smoothedAcceleration = Vector3.zero();
  Vector3 _smoothedGyroscope = Vector3.zero();
  Vector3 _smoothedMagnetic = Vector3.zero();

  // 设备方向和运动状态
  DeviceOrientation _deviceOrientation = DeviceOrientation.portrait;
  double _deviceTilt = 0.0; // 设备倾斜角度
  Vector2 _gravityDirection = Vector2(0, 1); // 重力方向
  bool _isInMotion = false;
  double _motionIntensity = 0.0;

  // 环境光线感应 (模拟)
  double _ambientBrightness = 0.5;
  Timer? _brightnessTimer;

  // 传感器状态
  bool _sensorsAvailable = false;
  bool _isInitialized = false;

  // 回调函数
  Function(Vector2)? _onGravityChanged;
  Function(double)? _onMotionIntensityChanged;
  Function(DeviceOrientation)? _onOrientationChanged;
  Function(double)? _onBrightnessChanged;

  /// 启动传感器监听
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _requestPermissions();
      await _checkSensorAvailability();
      
      if (_sensorsAvailable) {
        _startSensorListening();
      } else {
        print('传感器不可用，启用模拟模式');
        _enableSimulationMode();
      }
      
      _startBrightnessSimulation();
      _isInitialized = true;
    } catch (e) {
      print('传感器初始化失败: $e，启用模拟模式');
      _sensorsAvailable = false;
      _enableSimulationMode();
      _startBrightnessSimulation();
      _isInitialized = true;
    }
  }

  /// 停止传感器监听
  void dispose() {
    _accelerometerSubscription?.cancel();
    _gyroscopeSubscription?.cancel();
    _magnetometerSubscription?.cancel();
    _brightnessTimer?.cancel();
  }

  /// 设置重力变化回调
  void setOnGravityChanged(Function(Vector2) callback) {
    _onGravityChanged = callback;
  }

  /// 设置运动强度回调
  void setOnMotionIntensityChanged(Function(double) callback) {
    _onMotionIntensityChanged = callback;
  }

  /// 设置设备方向回调
  void setOnOrientationChanged(Function(DeviceOrientation) callback) {
    _onOrientationChanged = callback;
  }

  /// 设置环境亮度回调
  void setOnBrightnessChanged(Function(double) callback) {
    _onBrightnessChanged = callback;
  }

  /// 获取当前重力方向 (归一化向量)
  Vector2 get gravityDirection => _gravityDirection;

  /// 获取当前运动强度 (0.0 - 1.0)
  double get motionIntensity => _motionIntensity;

  /// 获取设备倾斜角度 (弧度)
  double get deviceTilt => _deviceTilt;

  /// 获取当前设备方向
  DeviceOrientation get deviceOrientation => _deviceOrientation;

  /// 是否在运动中
  bool get isInMotion => _isInMotion;

  /// 获取环境亮度 (0.0 - 1.0)
  double get ambientBrightness => _ambientBrightness;

  /// 传感器是否可用
  bool get sensorsAvailable => _sensorsAvailable;

  /// 计算基于运动的粘滞度调整
  double calculateMotionBasedViscosity(double baseViscosity) {
    // 运动时降低粘滞度，模拟液体流动性增加
    final motionFactor = 1.0 - (_motionIntensity * 0.3);
    return baseViscosity * motionFactor;
  }

  /// 计算基于倾斜的流动方向
  Vector2 calculateFlowDirection() {
    // 基于重力方向计算液体流动方向
    return _gravityDirection * _motionIntensity;
  }

  /// 计算环境光对透明度的影响
  double calculateLightBasedOpacity(double baseOpacity) {
    // 明亮环境下降低透明度，昏暗环境下增加透明度
    final lightFactor = 0.8 + (_ambientBrightness * 0.4);
    return (baseOpacity * lightFactor).clamp(0.0, 1.0);
  }

  // 私有方法

  /// 检查传感器可用性
  Future<void> _checkSensorAvailability() async {
    try {
      print('🔍 开始传感器可用性检查...');
      
      // 检查是否在开发环境中运行
      bool isDebugMode = false;
      assert(isDebugMode = true); // 在debug模式下这行代码会执行
      
      if (isDebugMode) {
        print('🐛 检测到开发模式，可能在桌面模拟器中运行');
      }
      
      // 添加多重检查机制
      bool testPassed = false;
      
      // 第一次尝试：快速检查
      try {
        final testStream = accelerometerEvents.timeout(const Duration(seconds: 2));
        await testStream.first;
        testPassed = true;
        print('✅ 第一次传感器检查通过');
      } catch (e) {
        print('⚠️ 第一次传感器检查失败: $e');
        
        // 第二次尝试：延长超时时间
        try {
          print('🔄 尝试第二次传感器检查...');
          final testStream2 = accelerometerEvents.timeout(const Duration(seconds: 5));
          await testStream2.first;
          testPassed = true;
          print('✅ 第二次传感器检查通过');
        } catch (e2) {
          print('❌ 第二次传感器检查也失败: $e2');
        }
      }
      
      _sensorsAvailable = testPassed;
      
      if (_sensorsAvailable) {
        print('传感器可用性检查：✅ 最终结果 - 可用');
      } else {
        print('传感器可用性检查：❌ 最终结果 - 不可用，将启用模拟模式');
        print('🔄 自动切换到模拟模式以确保应用正常运行');
      }
      
    } catch (e) {
      _sensorsAvailable = false;
      print('传感器可用性检查：💥 严重错误 - $e');
      
      // 详细的错误分析
      if (e.toString().contains('MissingPluginException')) {
        print('📱 检测到MissingPluginException，插件未正确安装或平台不支持');
        print('💡 建议：执行完全重建 - flutter clean && flutter pub get && cd ios && pod install');
        print('🔄 自动启用模拟模式以保证应用正常运行');
      } else if (e.toString().contains('TimeoutException')) {
        print('⏱️ 传感器响应超时，设备可能不支持或权限不足');
        print('🔄 启用模拟模式替代真实传感器');
      } else if (e.toString().contains('PlatformException')) {
        print('🔧 平台特定错误，可能需要权限或硬件不支持');
        print('🔄 使用模拟模式确保功能可用');
      } else {
        print('❓ 未知传感器错误类型：${e.runtimeType}');
        print('🔄 启用模拟模式以确保应用稳定运行');
      }
      
      print('✅ 模拟模式已准备就绪，液态玻璃效果将正常工作');
    }
  }

  /// 启用模拟模式
  void _enableSimulationMode() {
    print('🔧 启用传感器模拟模式');
    
    // 模拟轻微的设备倾斜
    Timer.periodic(const Duration(seconds: 3), (timer) {
      if (!_isInitialized) {
        timer.cancel();
        return;
      }
      
      final random = Random();
      final x = (random.nextDouble() - 0.5) * 0.2;
      final y = (random.nextDouble() - 0.5) * 0.2;
      
      _gravityDirection = Vector2(x, y);
      _onGravityChanged?.call(_gravityDirection);
      
      // 模拟轻微运动
      _motionIntensity = 0.1 + random.nextDouble() * 0.1;
      _onMotionIntensityChanged?.call(_motionIntensity);
    });
  }

  Future<void> _requestPermissions() async {
    // Flutter sensors_plus 通常不需要显式权限请求
    // 但在某些平台上可能需要
    try {
      print('🔐 检查传感器权限...');
      
      // 检查平台支持情况
      if (!_isPlatformSupported()) {
        print('🚫 当前平台不支持传感器功能');
        return;
      }
      
      // 未来可以在此处添加平台特定的权限请求逻辑
      // 例如：Android上的BODY_SENSORS权限等
      
      print('✅ 权限检查完成');
    } catch (e) {
      print('❌ 传感器权限请求失败: $e');
    }
  }

  void _startSensorListening() {
    if (!_sensorsAvailable) {
      print('🚫 传感器不可用，跳过传感器监听');
      return;
    }

    try {
      // 加速度计 - 用于检测重力方向和运动
      _accelerometerSubscription = accelerometerEvents.listen(
        _onAccelerometerEvent,
        onError: (error) {
          print('❌ 加速度计错误: $error');
          _handleSensorError('accelerometer', error);
        },
        cancelOnError: false,
      );

      // 陀螺仪 - 用于检测旋转运动  
      _gyroscopeSubscription = gyroscopeEvents.listen(
        _onGyroscopeEvent,
        onError: (error) {
          print('❌ 陀螺仪错误: $error');
          _handleSensorError('gyroscope', error);
        },
        cancelOnError: false,
      );

      // 磁力计 - 用于辅助方向计算
      _magnetometerSubscription = magnetometerEvents.listen(
        _onMagnetometerEvent,
        onError: (error) {
          print('❌ 磁力计错误: $error');
          _handleSensorError('magnetometer', error);
        },
        cancelOnError: false,
      );
      
      print('✅ 传感器监听已启动');
    } catch (e) {
      print('❌ 启动传感器监听失败: $e');
      _sensorsAvailable = false;
      _enableSimulationMode();
    }
  }

  void _onAccelerometerEvent(AccelerometerEvent event) {
    _lastAccelerometerEvent = event;
    
    // 应用低通滤波器平滑数据
    _smoothedAcceleration = _smoothedAcceleration * _filterAlpha + 
        Vector3(event.x, event.y, event.z) * (1.0 - _filterAlpha);

    // 更新重力方向
    _updateGravityDirection();
    
    // 检测运动状态
    _updateMotionState();
    
    // 更新设备方向
    _updateDeviceOrientation();
  }

  void _onGyroscopeEvent(GyroscopeEvent event) {
    _lastGyroscopeEvent = event;
    
    // 应用低通滤波器平滑数据
    _smoothedGyroscope = _smoothedGyroscope * _filterAlpha + 
        Vector3(event.x, event.y, event.z) * (1.0 - _filterAlpha);

    // 基于陀螺仪数据更新运动强度
    _updateRotationMotion();
  }

  void _onMagnetometerEvent(MagnetometerEvent event) {
    _lastMagnetometerEvent = event;
    
    // 应用低通滤波器平滑数据
    _smoothedMagnetic = _smoothedMagnetic * _filterAlpha + 
        Vector3(event.x, event.y, event.z) * (1.0 - _filterAlpha);
  }

  void _updateGravityDirection() {
    // 从加速度计数据提取重力方向
    final gravity = Vector3(
      -_smoothedAcceleration.x,
      -_smoothedAcceleration.y,
      -_smoothedAcceleration.z,
    ).normalized();
    
    // 投影到2D屏幕坐标
    _gravityDirection = Vector2(gravity.x, gravity.y);
    
    // 计算倾斜角度
    _deviceTilt = atan2(_gravityDirection.x, _gravityDirection.y);
    
    // 通知回调
    _onGravityChanged?.call(_gravityDirection);
  }

  void _updateMotionState() {
    // 计算加速度变化幅度
    final accelerationMagnitude = _smoothedAcceleration.magnitude;
    
    // 检测是否在运动 (考虑重力加速度 ~9.8)
    _isInMotion = (accelerationMagnitude - 9.8).abs() > _motionThreshold;
    
    // 计算运动强度
    final rawIntensity = ((accelerationMagnitude - 9.8).abs() / 9.8).clamp(0.0, 1.0);
    _motionIntensity = _motionIntensity * 0.9 + rawIntensity * 0.1; // 平滑处理
    
    // 通知回调
    _onMotionIntensityChanged?.call(_motionIntensity);
  }

  void _updateRotationMotion() {
    // 基于陀螺仪增加旋转运动的贡献
    final rotationMagnitude = _smoothedGyroscope.magnitude;
    
    if (rotationMagnitude > _rotationThreshold) {
      final rotationIntensity = (rotationMagnitude * 0.1).clamp(0.0, 0.3);
      _motionIntensity = max(_motionIntensity, rotationIntensity);
      _onMotionIntensityChanged?.call(_motionIntensity);
    }
  }

  void _updateDeviceOrientation() {
    // 基于重力方向确定设备方向
    DeviceOrientation newOrientation;
    
    if (_gravityDirection.y.abs() > _gravityDirection.x.abs()) {
      newOrientation = _gravityDirection.y > 0 
          ? DeviceOrientation.portrait
          : DeviceOrientation.portraitDown;
    } else {
      newOrientation = _gravityDirection.x > 0 
          ? DeviceOrientation.landscapeRight
          : DeviceOrientation.landscapeLeft;
    }
    
    if (newOrientation != _deviceOrientation) {
      _deviceOrientation = newOrientation;
      _onOrientationChanged?.call(_deviceOrientation);
    }
  }

  void _startBrightnessSimulation() {
    // 模拟环境光线变化
    // 在真实应用中，可以使用light sensor插件
    _brightnessTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!_isInitialized) {
        timer.cancel();
        return;
      }
      
      // 模拟缓慢的亮度变化
      _ambientBrightness += (Random().nextDouble() - 0.5) * 0.1;
      _ambientBrightness = _ambientBrightness.clamp(0.0, 1.0);
      
      _onBrightnessChanged?.call(_ambientBrightness);
    });
  }
  
  /// 处理传感器错误
  void _handleSensorError(String sensorType, dynamic error) {
    print('🔧 处理传感器错误: $sensorType - $error');
    
    // 如果是MissingPluginException，标记传感器不可用
    if (error.toString().contains('MissingPluginException') || 
        error.toString().contains('PlatformException')) {
      print('🚫 检测到平台插件问题，切换到模拟模式');
      _sensorsAvailable = false;
      
      // 取消现有订阅
      _accelerometerSubscription?.cancel();
      _gyroscopeSubscription?.cancel();
      _magnetometerSubscription?.cancel();
      
      // 启用模拟模式
      _enableSimulationMode();
    }
  }
  
  /// 检查平台支持
  bool _isPlatformSupported() {
    try {
      // 检查当前平台是否支持传感器
      // 在某些环境下（如桌面模拟器），传感器可能不可用
      
      // 可以通过dart:io来检查平台
      // 但为了避免在web平台出错，这里使用更通用的方法
      
      // 检查是否在Flutter Web环境
      bool isWeb = identical(0, 0.0); // 这是检测web平台的通用方法
      if (isWeb) {
        print('🌐 检测到Web平台，传感器功能受限');
        return false;
      }
      
      // 默认假设移动平台支持，实际错误会在运行时捕获
      return true;
    } catch (e) {
      print('❌ 平台支持检查失败: $e');
      return false;
    }
  }
  
  /// 获取传感器状态信息
  Map<String, dynamic> getSensorStatus() {
    return {
      'available': _sensorsAvailable,
      'initialized': _isInitialized,
      'activeTimers': _brightnessTimer?.isActive ?? false,
      'lastAccelerometerData': _lastAccelerometerEvent != null,
      'lastGyroscopeData': _lastGyroscopeEvent != null,
      'lastMagnetometerData': _lastMagnetometerEvent != null,
    };
  }
}

/// 设备方向枚举
enum DeviceOrientation {
  portrait,
  portraitDown,
  landscapeLeft,
  landscapeRight,
}

/// 3D向量工具类
class Vector3 {
  double x;
  double y;
  double z;

  Vector3(this.x, this.y, this.z);

  Vector3.zero() : x = 0.0, y = 0.0, z = 0.0;

  double get magnitude => sqrt(x * x + y * y + z * z);

  Vector3 operator +(Vector3 other) => Vector3(x + other.x, y + other.y, z + other.z);
  Vector3 operator -(Vector3 other) => Vector3(x - other.x, y - other.y, z - other.z);
  Vector3 operator *(double scalar) => Vector3(x * scalar, y * scalar, z * scalar);

  Vector3 normalized() {
    final mag = magnitude;
    return mag > 0 ? Vector3(x / mag, y / mag, z / mag) : Vector3.zero();
  }
}

/// 2D向量工具类 (复用自physics_engine.dart)
class Vector2 {
  double x;
  double y;

  Vector2(this.x, this.y);

  Vector2.zero() : x = 0.0, y = 0.0;

  double get magnitude => sqrt(x * x + y * y);

  Vector2 operator +(Vector2 other) => Vector2(x + other.x, y + other.y);
  Vector2 operator -(Vector2 other) => Vector2(x - other.x, y - other.y);
  Vector2 operator *(double scalar) => Vector2(x * scalar, y * scalar);
}