import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';
import 'package:flutter/scheduler.dart';

/// 提供各种自定义页面过渡动画
class AppPageTransitions {
  /// 淡入淡出过渡效果
  static PageRouteBuilder fadeTransition({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 250),
    );
  }

  /// 从右到左滑动进入
  static PageRouteBuilder slideHorizontal({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var curve = Curves.easeOutCubic;
        var curveTween = CurveTween(curve: curve);

        var begin = const Offset(1.0, 0.0);
        var end = Offset.zero;
        var tween = Tween(begin: begin, end: end).chain(curveTween);

        return SlideTransition(position: animation.drive(tween), child: child);
      },
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 250),
    );
  }

  /// 从底部滑入的效果，类似模态底部菜单
  static PageRouteBuilder slideVertical({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var curve = Curves.easeOutCubic;
        var curveTween = CurveTween(curve: curve);

        var begin = const Offset(0.0, 1.0);
        var end = Offset.zero;
        var tween = Tween(begin: begin, end: end).chain(curveTween);

        return SlideTransition(position: animation.drive(tween), child: child);
      },
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 250),
      opaque: false,
      fullscreenDialog: true,
      barrierColor: Colors.transparent,
      maintainState: true,
    );
  }

  /// iOS风格从底部弹出，带有弹性效果的过渡
  static PageRouteBuilder iOSBottomSheet({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 使用弹簧动画曲线 - 模拟iOS的自然弹性效果
        const springDescription = SpringDescription(
          mass: 0.8, // 质量
          stiffness: 100.0, // 刚度
          damping: 14.0, // 阻尼
        );

        // 创建弹簧模拟器
        final springSimulation = SpringSimulation(springDescription, 0, 1, 0);

        // 使用模拟器驱动动画
        final animationWithSpring = AnimationController(
          duration: const Duration(milliseconds: 600),
          vsync: const _DummyTickerProvider(),
        )..animateWith(springSimulation);

        // 构建滑动动画
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 1.0),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
            // 使用弹性曲线效果
            reverseCurve: Curves.easeInCubic,
          ),
        );

        // 构建缩放动画
        final scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
          CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
        );

        // 构建透明度动画
        final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: animation,
            curve: const Interval(0.1, 1.0, curve: Curves.easeOut),
          ),
        );

        // 背景模糊效果的透明度
        final blurAnimation = Tween<double>(
          begin: 0.0,
          end: 0.65,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return Stack(
          children: [
            // 背景暗化效果
            FadeTransition(
              opacity: blurAnimation,
              child: Container(color: Colors.black),
            ),
            // 内容弹出效果
            SlideTransition(
              position: slideAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: FadeTransition(opacity: fadeAnimation, child: child),
              ),
            ),
          ],
        );
      },
      opaque: false,
      barrierColor: Colors.transparent,
      barrierDismissible: true,
      maintainState: true,
      fullscreenDialog: true,
      transitionDuration: const Duration(milliseconds: 600),
      reverseTransitionDuration: const Duration(milliseconds: 500),
    );
  }

  /// 从中心缩放效果
  static PageRouteBuilder scaleTransition({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var curve = Curves.easeOutBack;

        var scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        var fadeAnimation = Tween<double>(
          begin: 0.6,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(scale: scaleAnimation, child: child),
        );
      },
      transitionDuration: const Duration(milliseconds: 450),
      reverseTransitionDuration: const Duration(milliseconds: 350),
    );
  }

  /// 视差滑动效果，同时处理前后页面动画
  static PageRouteBuilder parallaxTransition({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        var curve = Curves.easeOutCubic;

        // 当前页面的动画
        var slideAnimation = Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        // 上一个页面的动画
        var previousPageAnimation = Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(-0.3, 0.0),
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        // 暗化前一个页面的动画
        var fadeOutAnimation = Tween<double>(
          begin: 1.0,
          end: 0.7,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        return Stack(
          children: [
            // 前一个页面的动画
            SlideTransition(
              position: previousPageAnimation,
              child: FadeTransition(
                opacity: fadeOutAnimation,
                child: Container(color: Colors.black), // 这里实际上是前一个页面的内容
              ),
            ),
            // 当前页面的动画
            SlideTransition(position: slideAnimation, child: child),
          ],
        );
      },
      transitionDuration: const Duration(milliseconds: 400),
      reverseTransitionDuration: const Duration(milliseconds: 350),
    );
  }

  /// Apple Music 风格动画，结合滑动和透明度
  static PageRouteBuilder appleMusicStyle({required Widget page}) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 使用适合音乐应用的曲线
        var curve = Curves.easeOutQuint;

        // 滑入动画
        var slideAnimation = Tween<Offset>(
          begin: const Offset(0.0, 0.3),
          end: Offset.zero,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        // 透明度动画
        var fadeAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: curve));

        // 深色背景过渡动画
        var backdropAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(parent: animation, curve: Curves.easeOut));

        return Stack(
          children: [
            // 背景颜色过渡
            FadeTransition(
              opacity: backdropAnimation,
              child: Container(color: Colors.black.withOpacity(0.3)),
            ),
            // 内容过渡
            FadeTransition(
              opacity: fadeAnimation,
              child: SlideTransition(position: slideAnimation, child: child),
            ),
          ],
        );
      },
      opaque: false,
      barrierColor: Colors.black45,
      barrierDismissible: true,
      transitionDuration: const Duration(milliseconds: 600),
      reverseTransitionDuration: const Duration(milliseconds: 500),
    );
  }

  /// 零延迟路由 - 无任何动画，立即显示页面
  static PageRoute zeroTransitionRoute({
    required Widget page,
    bool maintainState = true,
    RouteSettings? settings,
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 不应用任何动画，直接返回子组件
        return child;
      },
      transitionDuration: Duration.zero,
      reverseTransitionDuration: Duration.zero,
      maintainState: maintainState,
      opaque: true,
      settings: settings,
    );
  }

  /// 可交互返回路由 - 用于实现无动画的手势返回
  static PageRoute interactivePopRoute({
    required Widget page,
    bool maintainState = true,
    Duration transitionDuration = Duration.zero,
    Duration reverseTransitionDuration = Duration.zero,
  }) {
    return InteractivePopRoute(
      builder: (_) => page,
      transitionDuration: transitionDuration,
      reverseTransitionDuration: reverseTransitionDuration,
      maintainState: maintainState,
    );
  }
}

/// 用于创建弹性动画的虚拟TickerProvider
class _DummyTickerProvider extends TickerProvider {
  const _DummyTickerProvider();

  @override
  Ticker createTicker(TickerCallback onTick) =>
      Ticker(onTick, debugLabel: 'iOS Spring Animation');
}

/// 自定义可交互返回路由，支持手势直接返回，无动画
class InteractivePopRoute<T> extends PageRoute<T> {
  InteractivePopRoute({
    required this.builder,
    this.transitionDuration = Duration.zero,
    this.reverseTransitionDuration = Duration.zero,
    this.opaque = true,
    this.barrierDismissible = false,
    this.barrierColor,
    this.barrierLabel,
    this.maintainState = true,
    super.fullscreenDialog = false,
    super.allowSnapshotting = true,
  });

  final WidgetBuilder builder;

  @override
  final Duration transitionDuration;

  @override
  final Duration reverseTransitionDuration;

  @override
  final bool opaque;

  @override
  final bool barrierDismissible;

  @override
  final Color? barrierColor;

  @override
  final String? barrierLabel;

  @override
  final bool maintainState;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return builder(context);
  }

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // 不应用任何动画，直接返回子组件
    return child;
  }
}
