import 'package:flutter/cupertino.dart';

/// 路由辅助类，提供统一的Cupertino风格页面转换
class RouteHelper {
  /// 使用Cupertino风格的页面转场效果
  static PageRoute<T> cupertinoRoute<T>({
    required WidgetBuilder builder,
    String? title,
    RouteSettings? settings,
    bool fullscreenDialog = false,
    bool maintainState = true,
  }) {
    return CupertinoPageRoute<T>(
      builder: builder,
      title: title,
      settings: settings,
      fullscreenDialog: fullscreenDialog,
      maintainState: maintainState,
    );
  }
  
  /// 创建全屏模态页面，自下而上滑入
  static PageRoute<T> modalRoute<T>({
    required WidgetBuilder builder,
    RouteSettings? settings,
    bool maintainState = true,
  }) {
    return CupertinoPageRoute<T>(
      builder: builder,
      settings: settings,
      fullscreenDialog: true,
      maintainState: maintainState,
    );
  }
  
  /// 创建淡入淡出效果的页面转场动画
  static PageRoute<T> fadeRoute<T>({
    required WidgetBuilder builder,
    RouteSettings? settings,
    bool maintainState = true,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => builder(context),
      settings: settings,
      maintainState: maintainState,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
    );
  }
  
  /// 替换当前路由并跳转到新页面
  static void pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    WidgetBuilder builder, {
    String? title,
    bool fullscreenDialog = false,
    RouteSettings? settings,
  }) {
    Navigator.pushReplacement(
      context,
      cupertinoRoute(
        builder: builder,
        title: title,
        fullscreenDialog: fullscreenDialog,
        settings: settings,
      ),
    );
  }
  
  /// 跳转到新页面
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    WidgetBuilder builder, {
    String? title,
    bool fullscreenDialog = false,
    RouteSettings? settings,
  }) {
    return Navigator.push<T>(
      context,
      cupertinoRoute(
        builder: builder,
        title: title,
        fullscreenDialog: fullscreenDialog,
        settings: settings,
      ),
    );
  }
  
  /// 跳转到新页面并移除之前的所有页面
  static void pushAndRemoveUntil<T extends Object?>(
    BuildContext context,
    WidgetBuilder builder, {
    String? title,
    bool fullscreenDialog = false,
    RouteSettings? settings,
    RoutePredicate? predicate,
  }) {
    Navigator.pushAndRemoveUntil(
      context,
      cupertinoRoute(
        builder: builder,
        title: title,
        fullscreenDialog: fullscreenDialog,
        settings: settings,
      ),
      predicate ?? (Route<dynamic> route) => false,
    );
  }
} 