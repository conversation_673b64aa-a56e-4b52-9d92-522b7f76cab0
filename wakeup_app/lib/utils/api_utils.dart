import 'dart:convert';
import 'package:http/http.dart' as http;
import '../services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../pages/auth/login_page.dart';

/// API工具类，用于解析API响应
class ApiUtils {
  /// 构建完整的API URL
  static String buildUrl(String endpoint) {
    return '${AuthService.baseUrl}$endpoint';
  }

  /// 构建标准的API请求头
  static Map<String, String> buildHeaders(String token) {
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  /// 处理授权失败
  static void handleUnauthorized(BuildContext? context) async {
    // 清除token
    await AuthService.clearToken();

    // 如果有context，使用Provider更新用户状态
    if (context != null && context.mounted) {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      userProvider.clearUser();

      // 使用Cupertino风格的对话框
      showCupertinoDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: const Text('登录已过期'),
            content: const Text('您的登录已过期，请重新登录'),
            actions: [
              CupertinoDialogAction(
                child: const Text('确定'),
                onPressed: () {
                  Navigator.of(context).pop();
                  // 可选：导航到登录页面
                  Navigator.of(context).push(
                    CupertinoPageRoute(builder: (context) => const LoginPage()),
                  );
                },
              ),
            ],
          );
        },
      );
    }
  }

  /// 解析JSON响应
  /// [response] HTTP响应对象
  /// [expectList] 是否期望返回列表数据
  /// [context] 可选的BuildContext，用于处理401错误
  static dynamic parseJsonResponse(
    http.Response response, {
    bool expectList = true,
    BuildContext? context,
  }) {
    // 特殊处理401错误（未授权）
    if (response.statusCode == 401) {
      print('授权错误: 令牌无效或已过期');

      // 异步处理授权失败
      Future.microtask(() => handleUnauthorized(context));

      return expectList ? [] : {'error': '无效的授权令牌，请重新登录'};
    }

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        // 检查响应体是否为空
        if (response.body.isEmpty) {
          print('响应体为空');
          return expectList ? [] : {'error': '响应体为空'};
        }

        final jsonData = json.decode(response.body);

        // 处理直接返回列表的情况
        if (jsonData is List) {
          return expectList ? jsonData : {'data': jsonData};
        }

        // 处理返回标准格式JSON对象的情况
        if (jsonData is Map<String, dynamic>) {
          // 如果有code字段且为0，表示成功
          if (jsonData.containsKey('code') && jsonData['code'] == 0) {
            final data = jsonData['data'];
            // 根据期望类型返回
            if (data is List && expectList) {
              return data;
            } else if (data is Map && !expectList) {
              return data;
            } else {
              // 返回原始data字段，不管类型
              return data ?? (expectList ? [] : {});
            }
          } else {
            // API返回了错误
            final message = jsonData['message'] ?? '未知错误';
            print('API错误: $message');
            return expectList ? [] : {'error': message};
          }
        } else {
          // 不是Map也不是List的情况
          print('API返回格式异常: 非标准JSON格式');
          return expectList ? [] : {'error': 'API返回格式异常'};
        }
      } catch (e) {
        print('解析响应失败: $e');
        return expectList ? [] : {'error': '解析响应失败'};
      }
    } else {
      print('HTTP错误: ${response.statusCode}');
      return expectList ? [] : {'error': '请求失败，状态码: ${response.statusCode}'};
    }
  }

  /// 安全获取Map中的值，避免空值和类型错误
  static T? getValueSafely<T>(Map<String, dynamic> map, String key) {
    if (!map.containsKey(key) || map[key] == null) {
      return null;
    }

    final value = map[key];
    if (value is T) {
      return value;
    }

    // 尝试类型转换
    try {
      if (T == int && value is String) {
        return int.tryParse(value) as T?;
      } else if (T == double && value is String) {
        return double.tryParse(value) as T?;
      } else if (T == String) {
        return value.toString() as T;
      } else if (T == bool && value is String) {
        return (value.toLowerCase() == 'true') as T?;
      } else if (T == int && value is num) {
        return value.toInt() as T;
      } else if (T == double && value is num) {
        return value.toDouble() as T;
      }
    } catch (e) {
      print('类型转换失败: $e');
      // 转换失败，返回null
    }

    return null;
  }
}
