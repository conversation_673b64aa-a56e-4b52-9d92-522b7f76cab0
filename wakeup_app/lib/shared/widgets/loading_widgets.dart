import 'package:flutter/cupertino.dart';
import '../../constants/fonts.dart';

/// 统一的加载组件库
/// 解决项目中重复的加载UI代码

/// 基础加载指示器
class AppLoadingIndicator extends StatelessWidget {
  final Color? color;
  final double radius;
  final String? message;

  const AppLoadingIndicator({
    super.key,
    this.color,
    this.radius = 15,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CupertinoActivityIndicator(
          radius: radius,
          color: color ?? CupertinoColors.white,
        ),
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: AppFonts.createMixedStyle(
              fontSize: 14,
              color: color ?? CupertinoColors.white,
            ),
          ),
        ],
      ],
    );
  }
}

/// 全屏加载遮罩
class AppLoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;

  const AppLoadingOverlay({
    super.key,
    this.message,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      color: CupertinoColors.black.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: CupertinoColors.systemBackground,
            borderRadius: BorderRadius.circular(12),
          ),
          child: AppLoadingIndicator(
            color: CupertinoColors.label,
            message: message ?? '加载中...',
          ),
        ),
      ),
    );
  }
}

/// 页面加载状态
class AppPageLoading extends StatelessWidget {
  final String? message;
  final Color backgroundColor;

  const AppPageLoading({
    super.key,
    this.message,
    this.backgroundColor = CupertinoColors.black,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor,
      child: Center(
        child: AppLoadingIndicator(
          message: message ?? '正在加载...',
          color: backgroundColor == CupertinoColors.black 
              ? CupertinoColors.white 
              : CupertinoColors.label,
        ),
      ),
    );
  }
}

/// 列表加载更多指示器
class AppLoadMoreIndicator extends StatelessWidget {
  final bool isLoading;
  final VoidCallback? onLoadMore;
  final String? message;

  const AppLoadMoreIndicator({
    super.key,
    this.isLoading = false,
    this.onLoadMore,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: isLoading
            ? AppLoadingIndicator(
                radius: 12,
                message: message ?? '加载更多...',
                color: CupertinoColors.systemGrey,
              )
            : CupertinoButton(
                onPressed: onLoadMore,
                child: Text(
                  '加载更多',
                  style: AppFonts.createMixedStyle(
                    fontSize: 14,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
              ),
      ),
    );
  }
}

/// 刷新加载指示器
class AppRefreshIndicator extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;

  const AppRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      slivers: [
        CupertinoSliverRefreshControl(
          onRefresh: onRefresh,
        ),
        SliverToBoxAdapter(child: child),
      ],
    );
  }
}

/// 骨架屏加载
class AppSkeletonLoader extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const AppSkeletonLoader({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  State<AppSkeletonLoader> createState() => _AppSkeletonLoaderState();
}

class _AppSkeletonLoaderState extends State<AppSkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: CupertinoColors.systemGrey5.withValues(alpha: _animation.value),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4),
          ),
        );
      },
    );
  }
}