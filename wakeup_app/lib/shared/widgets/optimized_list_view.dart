import 'package:flutter/material.dart';

/// 优化的列表视图，支持虚拟化滚动和性能监控
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget? separator;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool addSemanticIndexes;
  final double? itemExtent;
  final int? semanticChildCount;
  final VoidCallback? onEndReached;
  final double endReachedThreshold;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.separator,
    this.padding,
    this.controller,
    this.shrinkWrap = false,
    this.physics,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
    this.itemExtent,
    this.semanticChildCount,
    this.onEndReached,
    this.endReachedThreshold = 200.0,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  late ScrollController _scrollController;
  // final Set<int> _visibleIndexes = {}; // 暂时注释，未来可用于可见性优化

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_handleScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_handleScroll);
    }
    super.dispose();
  }

  void _handleScroll() {
    // 检测是否接近底部
    if (widget.onEndReached != null) {
      final position = _scrollController.position;
      if (position.pixels >=
          position.maxScrollExtent - widget.endReachedThreshold) {
        widget.onEndReached!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView.separated(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
      addRepaintBoundaries: widget.addRepaintBoundaries,
      addSemanticIndexes: widget.addSemanticIndexes,
      // itemExtent: widget.itemExtent, // ListView.separated 不支持此参数
      // semanticChildCount: widget.semanticChildCount ?? widget.items.length,
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        final item = widget.items[index];

        // 使用 RepaintBoundary 包装每个项目，减少重绘
        return RepaintBoundary(
          child: _OptimizedListItem<T>(
            key: ValueKey('${item.hashCode}_$index'),
            item: item,
            index: index,
            builder: widget.itemBuilder,
          ),
        );
      },
      separatorBuilder: (context, index) {
        return widget.separator ?? const SizedBox.shrink();
      },
    );
  }
}

/// 优化的列表项，支持内容检测和自动优化
class _OptimizedListItem<T> extends StatefulWidget {
  final T item;
  final int index;
  final Widget Function(BuildContext context, T item, int index) builder;

  const _OptimizedListItem({
    super.key,
    required this.item,
    required this.index,
    required this.builder,
  });

  @override
  State<_OptimizedListItem<T>> createState() => _OptimizedListItemState<T>();
}

class _OptimizedListItemState<T> extends State<_OptimizedListItem<T>>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => false; // 大部分情况下不需要保持状态

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.builder(context, widget.item, widget.index);
  }
}

/// 大数据量列表的虚拟化实现
class VirtualizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final double itemHeight;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;
  final int bufferSize;

  const VirtualizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.itemHeight,
    this.padding,
    this.controller,
    this.bufferSize = 5,
  });

  @override
  State<VirtualizedListView<T>> createState() => _VirtualizedListViewState<T>();
}

class _VirtualizedListViewState<T> extends State<VirtualizedListView<T>> {
  late ScrollController _scrollController;
  int _startIndex = 0;
  int _endIndex = 0;
  late double _viewportHeight;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_updateVisibleRange);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_updateVisibleRange);
    }
    super.dispose();
  }

  void _updateVisibleRange() {
    if (!mounted) return;

    final scrollOffset = _scrollController.offset;
    final newStartIndex = (scrollOffset / widget.itemHeight).floor().clamp(
      0,
      widget.items.length - 1,
    );
    final visibleCount = (_viewportHeight / widget.itemHeight).ceil();
    final newEndIndex = (newStartIndex + visibleCount + widget.bufferSize)
        .clamp(0, widget.items.length - 1);

    if (newStartIndex != _startIndex || newEndIndex != _endIndex) {
      setState(() {
        _startIndex = newStartIndex;
        _endIndex = newEndIndex;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _viewportHeight = constraints.maxHeight;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _updateVisibleRange();
        });

        return ListView.builder(
          controller: _scrollController,
          padding: widget.padding,
          itemExtent: widget.itemHeight,
          itemCount: widget.items.length,
          itemBuilder: (context, index) {
            // 只渲染可见范围内的项目
            if (index < _startIndex || index > _endIndex) {
              return SizedBox(height: widget.itemHeight);
            }

            return widget.itemBuilder(context, widget.items[index], index);
          },
        );
      },
    );
  }
}
