class KnowledgeCard {
  final String id;
  final String title;
  final String content;
  final String courseId;
  final String courseName;
  final List<String> keyPoints;
  final List<KnowledgeQuestion> relatedQuestions;
  final DateTime createdAt;
  final int difficulty;
  final List<String> tags;

  KnowledgeCard({
    required this.id,
    required this.title,
    required this.content,
    required this.courseId,
    required this.courseName,
    required this.keyPoints,
    required this.relatedQuestions,
    required this.createdAt,
    this.difficulty = 1,
    this.tags = const [],
  });

  factory KnowledgeCard.fromJson(Map<String, dynamic> json) {
    return KnowledgeCard(
      id: json['id']?.toString() ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      courseId: json['course_id']?.toString() ?? '',
      courseName: json['course_name'] ?? '',
      keyPoints: List<String>.from(json['key_points'] ?? []),
      relatedQuestions: (json['related_questions'] as List<dynamic>?)
          ?.map((e) => KnowledgeQuestion.fromJson(e))
          .toList() ?? [],
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      difficulty: json['difficulty'] ?? 1,
      tags: List<String>.from(json['tags'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'course_id': courseId,
      'course_name': courseName,
      'key_points': keyPoints,
      'related_questions': relatedQuestions.map((e) => e.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'difficulty': difficulty,
      'tags': tags,
    };
  }
}

class KnowledgeQuestion {
  final String id;
  final String question;
  final List<String> options;
  final String correctAnswer;
  final String explanation;
  final String difficulty;

  KnowledgeQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
    this.difficulty = 'medium',
  });

  factory KnowledgeQuestion.fromJson(Map<String, dynamic> json) {
    return KnowledgeQuestion(
      id: json['id']?.toString() ?? '',
      question: json['question'] ?? '',
      options: List<String>.from(json['options'] ?? []),
      correctAnswer: json['correct_answer'] ?? '',
      explanation: json['explanation'] ?? '',
      difficulty: json['difficulty'] ?? 'medium',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correct_answer': correctAnswer,
      'explanation': explanation,
      'difficulty': difficulty,
    };
  }
}