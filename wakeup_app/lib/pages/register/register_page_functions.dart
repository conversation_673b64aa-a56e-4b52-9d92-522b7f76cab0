import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math';

// 用户名提示项
Widget buildUsernameTip(String tip) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 6),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          CupertinoIcons.checkmark_circle_fill,
          color: Colors.white.withOpacity(0.5),
          size: 12,
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            tip,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
              height: 1.3,
            ),
          ),
        ),
      ],
    ),
  );
}

// 密码提示项
Widget buildPasswordTip(String tip) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 6),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          CupertinoIcons.shield_fill,
          color: Colors.white.withOpacity(0.5),
          size: 12,
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            tip,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
              height: 1.3,
            ),
          ),
        ),
      ],
    ),
  );
}

// 性别选择
Widget buildGenderSelection(
  String selectedGender,
  Function(String) onSelect,
  Function showError,
  Function clearError,
) {
  final genders = ['男', '女', '其他'];

  return Column(
    children: [
      ...genders.map((gender) {
        final isSelected = selectedGender == gender;
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              HapticFeedback.selectionClick();
              clearError();
              onSelect(gender);
            },
            child: Container(
              width: double.infinity,
              height: 56,
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Colors.white.withOpacity(0.2)
                        : Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color:
                      isSelected
                          ? Colors.white.withOpacity(0.5)
                          : Colors.white.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  gender,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    ],
  );
}

// 地区选择
Widget buildRegionSelection(
  String selectedRegion,
  Function(String, bool) onSelect,
  Function showError,
  Function clearError,
) {
  final regions = [
    {'name': '中国大陆', 'flag': '🇨🇳', 'desc': '简体中文内容，本土化题库', 'available': true},
    {
      'name': '中国香港',
      'flag': '🇭🇰',
      'desc': '繁体中文内容，国际化视野',
      'available': false,
    },
    {'name': '中国台湾', 'flag': '🇹🇼', 'desc': '繁体中文内容，本土特色', 'available': false},
    {'name': '美国', 'flag': '🇺🇸', 'desc': '英文内容，北美教育体系', 'available': false},
    {'name': '英国', 'flag': '🇬🇧', 'desc': '英文内容，英式教育传统', 'available': false},
    {'name': '加拿大', 'flag': '🇨🇦', 'desc': '双语内容，多元文化背景', 'available': false},
    {'name': '澳大利亚', 'flag': '🇦🇺', 'desc': '英文内容，澳洲教育特色', 'available': false},
    {'name': '新加坡', 'flag': '🇸🇬', 'desc': '双语内容，亚洲教育枢纽', 'available': false},
    {'name': '日本', 'flag': '🇯🇵', 'desc': '日文内容，日式学习方法', 'available': false},
    {'name': '韩国', 'flag': '🇰🇷', 'desc': '韩文内容，韩式教育风格', 'available': false},
    {'name': '德国', 'flag': '🇩🇪', 'desc': '德文内容，严谨学术传统', 'available': false},
    {'name': '法国', 'flag': '🇫🇷', 'desc': '法文内容，浪漫文化气息', 'available': false},
    {'name': '其他国家/地区', 'flag': '🌍', 'desc': '国际化内容，全球视野', 'available': false},
  ];

  // 创建一个Map来存储所有按钮的抖动状态控制器
  final Map<String, GlobalKey<ShakeWidgetState>> shakeKeys = {};

  // 为每个地区创建一个唯一的抖动控制器
  for (var region in regions) {
    String name = region['name'] as String;
    shakeKeys[name] = GlobalKey<ShakeWidgetState>();
  }

  return Column(
    children: [
      // 提示信息
      Container(
        width: double.infinity,
        margin: const EdgeInsets.only(bottom: 20),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '选择将影响为您推荐的内容',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 6),
            _buildInfoItem('📚', '本地化题库和学习资料'),
            _buildInfoItem('📰', '当地教育资讯和考试信息'),
            _buildInfoItem('🎯', '符合当地教育体系的课程内容'),
            _buildInfoItem('🌐', '对应语言和文化背景的学习体验'),
          ],
        ),
      ),

      // 国家/地区选择列表
      Column(
        children:
            regions.map((region) {
              final regionName = region['name'] as String;
              final isSelected = selectedRegion == regionName;
              final isAvailable = region['available'] as bool;

              // 获取该地区对应的抖动控制器键
              final shakeKey = shakeKeys[regionName];

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                child: ShakeWidget(
                  key: shakeKey,
                  shakeCount: 5, // 增加抖动次数
                  shakeOffset: 15.0, // 增加抖动幅度
                  shakeDuration: const Duration(milliseconds: 800), // 增加抖动时长
                  onShakeComplete: () {
                    // 抖动结束后，如果这是当前选中的不可用区域，清除选择
                    if (selectedRegion == regionName && !isAvailable) {
                      onSelect('', false);
                    }
                  },
                  child: CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed:
                        isAvailable
                            ? () {
                              HapticFeedback.selectionClick();
                              clearError();
                              onSelect(regionName, true);
                            }
                            : () {
                              HapticFeedback.heavyImpact();
                              showError('$regionName 区域暂未开放');

                              // 设置临时选择状态
                              onSelect(regionName, false);

                              // 手动触发抖动动画，确保调用
                              print('触发抖动: $regionName'); // 添加调试输出
                              Future.microtask(() {
                                if (shakeKey?.currentState != null) {
                                  shakeKey!.currentState!.shake();
                                } else {
                                  print('抖动控制器为null: $regionName');
                                }
                              });
                            },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? isAvailable
                                    ? Colors.white.withOpacity(0.2)
                                    : Colors.red.withOpacity(0.2)
                                : Colors.white.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color:
                              isSelected
                                  ? isAvailable
                                      ? Colors.white.withOpacity(0.5)
                                      : Colors.red.withOpacity(0.5)
                                  : Colors.white.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          // 国旗emoji
                          Text(
                            region['flag'] as String,
                            style: const TextStyle(fontSize: 24),
                          ),
                          const SizedBox(width: 12),
                          // 国家/地区信息
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  regionName,
                                  style: TextStyle(
                                    color:
                                        isSelected
                                            ? isAvailable
                                                ? Colors.white
                                                : Colors.red.withOpacity(0.9)
                                            : isAvailable
                                            ? Colors.white.withOpacity(0.9)
                                            : Colors.white.withOpacity(0.5),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  isAvailable
                                      ? region['desc'] as String
                                      : '即将开放，敬请期待',
                                  style: TextStyle(
                                    color:
                                        isSelected
                                            ? isAvailable
                                                ? Colors.white.withOpacity(0.8)
                                                : Colors.red.withOpacity(0.7)
                                            : Colors.white.withOpacity(0.6),
                                    fontSize: 12,
                                    height: 1.2,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // 选中状态指示器
                          if (isSelected && isAvailable)
                            Icon(
                              CupertinoIcons.checkmark_circle_fill,
                              color: Colors.white,
                              size: 20,
                            ),
                          if (!isAvailable)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '待开放',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.6),
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    ],
  );
}

// 抖动效果组件
class ShakeWidget extends StatefulWidget {
  final Widget child;
  final int shakeCount;
  final double shakeOffset;
  final Duration shakeDuration;
  final VoidCallback? onShakeComplete;

  const ShakeWidget({
    Key? key,
    required this.child,
    this.shakeCount = 3,
    this.shakeOffset = 5.0,
    this.shakeDuration = const Duration(milliseconds: 300),
    this.onShakeComplete,
  }) : super(key: key);

  @override
  ShakeWidgetState createState() => ShakeWidgetState();
}

class ShakeWidgetState extends State<ShakeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isShaking = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.shakeDuration,
    );
    _controller.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 动画完成时触发回调
        _isShaking = false;
        widget.onShakeComplete?.call();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void shake() {
    if (_isShaking) return; // 避免重复触发

    _isShaking = true;
    print("开始抖动动画");
    _controller.reset();
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    // 使用sin函数创建震动效果，让抖动更加明显
    double offset =
        _isShaking
            ? sin(_controller.value * widget.shakeCount * 2 * 3.14159) *
                widget.shakeOffset
            : 0.0;

    return Transform.translate(offset: Offset(offset, 0), child: widget.child);
  }
}

// 构建信息提示项
Widget _buildInfoItem(String icon, String text) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 4),
    child: Row(
      children: [
        Text(icon, style: const TextStyle(fontSize: 12)),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white.withOpacity(0.6),
              fontSize: 11,
              height: 1.3,
            ),
          ),
        ),
      ],
    ),
  );
}

// 课程选择
Widget buildCourseSelection(
  List<String> selectedCourses,
  Function(String) onToggle,
  Function showError,
  Function clearError,
) {
  final courses = [
    '英语口语提升',
    '商务英语',
    '雅思托福',
    '日语基础',
    '韩语入门',
    '法语学习',
    '德语基础',
    '西班牙语',
    '意大利语',
    '俄语学习',
    '阿拉伯语',
    '编程基础',
  ];

  return Column(
    children: [
      ...courses.map((course) {
        final isSelected = selectedCourses.contains(course);
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              HapticFeedback.selectionClick();
              clearError();
              onToggle(course);
            },
            child: Container(
              width: double.infinity,
              height: 48,
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? const Color(0xFFFF0000).withOpacity(0.2)
                        : Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color:
                      isSelected
                          ? const Color(0xFFFF0000).withOpacity(0.5)
                          : Colors.white.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  course,
                  style: TextStyle(
                    color:
                        isSelected
                            ? Colors.white
                            : Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    ],
  );
}
