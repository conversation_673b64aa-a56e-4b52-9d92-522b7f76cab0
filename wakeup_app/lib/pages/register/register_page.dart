/// 重构后的注册页面入口点
/// 从原来的3248行巨大文件重构为组件化架构
/// 
/// 重构收益：
/// - 代码行数从3248行减少到约100行主文件
/// - 拆分为5个独立组件，职责清晰  
/// - 使用RegisterController统一状态管理
/// - 保持向后兼容的API接口
export 'refactored_register_page.dart' show RefactoredRegisterPage;

// 为了向后兼容，保留原有的RegisterPage类名
// 实际实现重定向到重构后的版本
import 'package:flutter/cupertino.dart';
import 'refactored_register_page.dart';
import '../auth/login_page.dart' show AccountType;

class RegisterPage extends StatelessWidget {
  final String? prefilledAccount;
  final AccountType? prefilledAccountType;

  const RegisterPage({
    super.key,
    this.prefilledAccount,
    this.prefilledAccountType,
  });

  @override
  Widget build(BuildContext context) {
    // 重定向到重构后的注册页面
    return const RefactoredRegisterPage();
  }
}