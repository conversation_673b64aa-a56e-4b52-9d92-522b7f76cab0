import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // 用于触觉反馈
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../../widgets/animated_avatar_widget.dart';
import '../../constants/fonts.dart';
import '../../models/article_model.dart';
import '../../services/article_service.dart';
import '../../services/course_service.dart';
import '../../widgets/article_widgets.dart';
import '../common/article_detail_page.dart';
import '../../providers/user_provider.dart';
import 'dart:developer' as developer;
import '../../utils/page_transitions.dart'; // 引入页面转场动画
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../services/auth_service.dart'; // 添加AuthService导入

class ResourceLibraryPage extends StatefulWidget {
  final int? courseId; // 接收所选课程ID参数
  final String? courseName; // 接收所选课程名称参数

  const ResourceLibraryPage({super.key, this.courseId, this.courseName});

  @override
  State<ResourceLibraryPage> createState() => _ResourceLibraryPageState();
}

// 自定义自定义文章模型，支持本地资产图片
class LocalArticle extends Article {
  final String? localAssetImage;

  LocalArticle({
    required String id,
    required String title,
    String subtitle = '',
    required String content,
    required DateTime publishDate,
    this.localAssetImage,
    String thumbnailUrl = '',
    String authorName = '',
    String source = '',
    List<String> tags = const [],
    List<String> categoryIds = const [],
    bool isFeatured = false,
    int readTimeMinutes = 0,
  }) : super(
         id: id,
         title: title,
         subtitle: subtitle,
         content: content,
         publishDate: publishDate,
         thumbnailUrl: thumbnailUrl,
         authorName: authorName,
         source: source,
         tags: tags,
         categoryIds: categoryIds,
         isFeatured: isFeatured,
         readTimeMinutes: readTimeMinutes,
       );
}

// 自定义课程分类模型，用于显示用户已加入课程
class CourseCategoryItem {
  final int courseId;
  final String id; // 用于文章分类的ID
  final String name; // 课程名称
  final String? imageUrl; // 课程图片

  CourseCategoryItem({
    required this.courseId,
    required this.id,
    required this.name,
    this.imageUrl,
  });
}

class _ResourceLibraryPageState extends State<ResourceLibraryPage> {
  final ArticleService _articleService = ArticleService();
  final CourseService _courseService = CourseService();
  List<dynamic> _latestArticles = []; // 使用dynamic类型以支持混合文章类型
  List<CourseCategoryItem> _courseCategories = []; // 用户已加入的课程作为分类
  bool _isLoading = true;
  String? _selectedCategoryId;
  int? _selectedCourseId; // 当前选择的课程ID
  String _selectedCourseName = ''; // 当前选择的课程名称

  // 课程ID与文章分类ID的映射关系
  // 使用每个课程ID的字符串形式作为分类ID
  // 这样可以确保每个课程都有唯一的分类ID
  Map<int, String> _getCategoryIdForCourse(int courseId) {
    // 根据课程ID生成唯一的分类ID
    return {courseId: 'course_$courseId'};
  }

  // 获取本地图片资源
  String _getLocalImageForIndex(int index) {
    final images = [
      'assets/images/learning_bg1.png',
      'assets/images/learning_bg2.png',
    ];
    return images[index % images.length];
  }

  @override
  void initState() {
    super.initState();

    // 如果有传入的课程ID和课程名称，则设置为当前选中的课程
    if (widget.courseId != null && widget.courseName != null) {
      _selectedCourseId = widget.courseId;
      _selectedCourseName = widget.courseName!;
      developer.log(
        '资料库页面接收到课程: id=${widget.courseId}, name=${widget.courseName}',
      );
    }

    _loadData();
  }

  Future<void> _loadData() async {
    if (!mounted) return; // 添加mounted检查

    setState(() {
      _isLoading = true;
    });

    try {
      // 先获取用户已加入的课程
      final userCourses = await _getUserEnrolledCourses();
      developer.log('获取到用户课程数量: ${userCourses.length}');

      // 将用户的课程转换为分类项
      final courseCategoryItems = _createCourseCategoryItems(userCourses);

      // 如果有指定课程ID，则自动选择该课程对应的分类
      if (_selectedCourseId != null) {
        for (var category in courseCategoryItems) {
          if (category.courseId == _selectedCourseId) {
            _selectedCategoryId = category.id;
            break;
          }
        }
      }

      // 为每个课程生成文章
      List<dynamic> allCourseArticles = [];

      // 如果有选中的分类ID
      if (_selectedCategoryId != null) {
        final selectedCategory = courseCategoryItems.firstWhere(
          (category) => category.id == _selectedCategoryId,
          orElse:
              () =>
                  courseCategoryItems.isNotEmpty
                      ? courseCategoryItems.first
                      : CourseCategoryItem(courseId: 0, id: '', name: '未知课程'),
        );

        // 为选中的课程生成文章
        final courseArticles = _generateArticlesForCourse(selectedCategory);
        allCourseArticles = courseArticles;
      } else {
        // 否则生成所有课程的文章
        for (var course in courseCategoryItems) {
          final courseArticles = _generateArticlesForCourse(course);
          allCourseArticles.addAll(courseArticles);
        }
      }

      // 只有当没有课程相关文章时，才尝试获取通用文章
      List<dynamic> articles;
      if (allCourseArticles.isEmpty) {
        // 获取默认文章并替换为本地图片
        List<dynamic> defaultArticles = _getDefaultArticles();
        articles = defaultArticles;
      } else {
        articles = allCourseArticles;
      }

      // 添加mounted检查
      if (!mounted) return;

      setState(() {
        _courseCategories = courseCategoryItems;
        _latestArticles =
            articles..sort((a, b) => b.publishDate.compareTo(a.publishDate));
        _isLoading = false;
      });

      developer.log(
        '资料库页面加载完成，共有${_courseCategories.length}个课程分类，${_latestArticles.length}篇文章',
      );
    } catch (e) {
      developer.log('加载资料库数据出错: $e');

      // 添加mounted检查
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });
    }
  }

  // 创建默认文章，使用本地图片
  List<LocalArticle> _getDefaultArticles() {
    final now = DateTime.now();

    return [
      LocalArticle(
        id: 'default_resource_1',
        title: '学习资料合集',
        subtitle: '精选学习资源',
        content:
            '这里收录了各种精选的学习资料，包括文档、视频、练习题等，帮助你更全面地掌握知识点。这些资料经过精心筛选，确保质量和实用性。',
        publishDate: now.subtract(const Duration(days: 1)),
        localAssetImage: _getLocalImageForIndex(0),
        authorName: 'WakeUp团队',
        tags: ['学习资料', '文档集合'],
        readTimeMinutes: 10,
      ),
      LocalArticle(
        id: 'default_resource_2',
        title: '考试备考指南',
        subtitle: '备考资料大全',
        content: '全面的考试备考资料，包括历年真题、模拟试卷、知识点总结等。这些资料能够帮助你系统地复习和准备考试，提高应试能力。',
        publishDate: now.subtract(const Duration(days: 3)),
        localAssetImage: _getLocalImageForIndex(1),
        authorName: 'WakeUp团队',
        tags: ['考试备考', '真题资料'],
        readTimeMinutes: 15,
      ),
    ];
  }

  // 为课程生成示例文章
  List<LocalArticle> _generateArticlesForCourse(CourseCategoryItem course) {
    final now = DateTime.now();
    final courseIndex = course.courseId.hashCode;

    return [
      LocalArticle(
        id: 'resource_${course.courseId}_1',
        title: '${course.name}学习资料包',
        subtitle: '完整学习资源',
        content:
            '这是一个完整的${course.name}学习资料包，包含了课程相关的所有必要资料：教材PDF、练习题、参考答案、学习笔记等。',
        publishDate: now.subtract(const Duration(days: 1)),
        localAssetImage: _getLocalImageForIndex(courseIndex),
        authorName: 'WakeUp团队',
        tags: [course.name, '学习资料', '资料包'],
        categoryIds: [course.id],
        readTimeMinutes: 8,
      ),
      LocalArticle(
        id: 'resource_${course.courseId}_2',
        title: '${course.name}练习题库',
        subtitle: '专项训练题集',
        content:
            '专门为${course.name}准备的练习题库，包含各种难度的题目和详细解答。通过这些题目的练习，可以有效提升你在该领域的应用能力。',
        publishDate: now.subtract(const Duration(days: 2)),
        localAssetImage: _getLocalImageForIndex(courseIndex + 1),
        authorName: 'WakeUp团队',
        tags: [course.name, '练习题库', '专项训练'],
        categoryIds: [course.id],
        readTimeMinutes: 12,
      ),
      LocalArticle(
        id: 'resource_${course.courseId}_3',
        title: '${course.name}参考资料',
        subtitle: '拓展阅读材料',
        content:
            '关于${course.name}的拓展参考资料，包括相关书籍推荐、在线资源链接、专业网站等。这些资料可以帮助你深入了解相关领域的最新发展。',
        publishDate: now.subtract(const Duration(days: 4)),
        localAssetImage: _getLocalImageForIndex(courseIndex + 2),
        authorName: 'WakeUp团队',
        tags: [course.name, '参考资料', '拓展阅读'],
        categoryIds: [course.id],
        readTimeMinutes: 6,
      ),
    ];
  }

  // 获取用户已加入的课程
  Future<List<Map<String, dynamic>>> _getUserEnrolledCourses() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn) {
        developer.log('用户未登录，无法获取课程');
        return [];
      }

      // 注意：这里不使用CourseService中的getUserCourses方法，
      // 因为我们需要获取用户在本应用中选择加入的课程，而不是购买的课程
      final userId = userProvider.userId;
      final token = userProvider.token;

      if (token == null) {
        developer.log('用户token为空，无法获取课程');
        return [];
      }

      // 使用user_course/list API直接获取用户已加入的课程
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/user_course/list/$userId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> courses = jsonDecode(response.body);
        developer.log('成功获取到${courses.length}个用户课程');
        return List<Map<String, dynamic>>.from(courses);
      } else {
        developer.log('获取用户课程失败：${response.statusCode} ${response.body}');
        return [];
      }
    } catch (e) {
      developer.log('获取用户课程出错: $e');
      return [];
    }
  }

  // 将用户课程转换为分类项
  List<CourseCategoryItem> _createCourseCategoryItems(
    List<Map<String, dynamic>> userCourses,
  ) {
    final courseCategoryItems = <CourseCategoryItem>[];

    for (var i = 0; i < userCourses.length; i++) {
      final course = userCourses[i];

      // 兼容不同的API返回格式
      final courseId = course['course_id'] ?? course['id'] ?? 0;
      if (courseId == 0) continue;

      final courseName = course['course_name'] ?? course['name'] ?? '未命名课程';
      final courseImageUrl = course['cover_image'] ?? course['image_url'];

      // 为每个课程生成唯一的分类ID
      final categoryId = _getCategoryIdForCourse(courseId)[courseId]!;

      developer.log('添加课程分类: id=$courseId, 名称=$courseName, 分类ID=$categoryId');

      courseCategoryItems.add(
        CourseCategoryItem(
          courseId: courseId,
          id: categoryId,
          name: courseName,
          imageUrl: courseImageUrl,
        ),
      );
    }

    developer.log('用户共有${courseCategoryItems.length}个课程分类');
    return courseCategoryItems;
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  void _navigateToArticleDetail(dynamic article) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => ArticleDetailPage(article: article),
      ),
    );
  }

  void _selectCategory(String categoryId) {
    if (!mounted) return; // 添加mounted检查

    setState(() {
      if (_selectedCategoryId == categoryId) {
        _selectedCategoryId = null;
      } else {
        _selectedCategoryId = categoryId;
      }
    });

    _loadArticlesByCategory();
  }

  Future<void> _loadArticlesByCategory() async {
    if (_selectedCategoryId == null) {
      await _loadData();
      return;
    }

    if (!mounted) return; // 添加mounted检查

    setState(() {
      _isLoading = true;
    });

    try {
      // 找到选中的课程
      final selectedCourse = _courseCategories.firstWhere(
        (category) => category.id == _selectedCategoryId,
        orElse: () => CourseCategoryItem(courseId: 0, id: '', name: '未知课程'),
      );

      // 如果是有效的课程，生成文章
      if (selectedCourse.courseId > 0) {
        final courseArticles = _generateArticlesForCourse(selectedCourse);

        if (!mounted) return; // 添加mounted检查

        setState(() {
          _latestArticles = courseArticles;
          _isLoading = false;
        });

        developer.log('已加载${selectedCourse.name}的${courseArticles.length}篇资料');
        return;
      }

      // 没有有效课程时，返回默认文章
      final defaultArticles = _getDefaultArticles();

      if (!mounted) return; // 添加mounted检查

      setState(() {
        _latestArticles = defaultArticles;
        _isLoading = false;
      });
    } catch (e) {
      developer.log('加载分类资料出错: $e');

      if (!mounted) return; // 添加mounted检查

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final titleText =
        AppLocalizations.of(context)!.resourceLibraryPageTitle ?? '资料库';
    final isChinese = Localizations.localeOf(context).languageCode == 'zh';

    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        // 使用Stack来叠加背景图片
        fit: StackFit.expand,
        children: [
          // 背景图片 - 与选题页面保持一致
          Positioned.fill(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: Image.asset(
                'assets/images/traditional_quiz_bg.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  developer.log('❌ 背景图片加载失败: $error');
                  // 出错时使用备选背景
                  return Image.asset(
                    'assets/images/learning_bg3.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  );
                },
              ),
            ),
          ),
          // 内容区域
          SafeArea(
            child: RefreshIndicator(
              backgroundColor: Colors.grey[900],
              color: Colors.white,
              onRefresh: _refreshData,
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                slivers: [
                  // 标题栏
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child:
                                (Platform.isIOS || Platform.isMacOS) &&
                                        !isChinese
                                    ? AppFonts.createBoldTextStack(
                                      titleText,
                                      fontSize: 36,
                                      letterSpacing: -0.5,
                                    )
                                    : Text(
                                      titleText,
                                      style: AppFonts.createTitleStyle(
                                        fontSize: 36,
                                        isLatinText: !isChinese,
                                      ),
                                    ),
                          ),
                          const AnimatedAvatarWidget(size: 36),
                        ],
                      ),
                    ),
                  ),

                  // 分类选择器
                  SliverToBoxAdapter(child: _buildCategorySelector()),

                  if (_isLoading)
                    const SliverFillRemaining(
                      child: Center(
                        child: CupertinoActivityIndicator(color: Colors.white),
                      ),
                    )
                  else ...[
                    // 最新资料
                    if (_latestArticles.isNotEmpty)
                      SliverToBoxAdapter(
                        child: CollectionHeader(
                          title:
                              _selectedCategoryId != null
                                  ? _courseCategories
                                      .firstWhere(
                                        (category) =>
                                            category.id == _selectedCategoryId,
                                        orElse:
                                            () => CourseCategoryItem(
                                              courseId: 0,
                                              id: '',
                                              name: '分类资料',
                                            ),
                                      )
                                      .name
                                  : '最新资料',
                        ),
                      ),

                    if (_latestArticles.isEmpty)
                      const SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: Center(
                            child: Text(
                              '暂无资料',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      )
                    else
                      SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          final article = _latestArticles[index];
                          return Column(
                            children: [
                              _buildArticleCard(
                                article,
                                () => _navigateToArticleDetail(article),
                              ),
                              if (index < _latestArticles.length - 1)
                                const ArticleDivider(),
                            ],
                          );
                        }, childCount: _latestArticles.length),
                      ),
                  ],

                  // 底部间距
                  const SliverToBoxAdapter(child: SizedBox(height: 50)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建文章卡片，支持本地资源图片
  Widget _buildArticleCard(dynamic article, VoidCallback onTap) {
    // 直接使用HorizontalArticleCard，它现在是纯文本样式
    if (article is Article) {
      return HorizontalArticleCard(article: article, onTap: onTap);
    } else if (article is LocalArticle) {
      // 如果需要，可以为LocalArticle创建一个转换到Article的方法，或者确保其兼容Article
      // 这里假设LocalArticle的属性与Article兼容，或者已经处理了转换
      return HorizontalArticleCard(
        article: Article(
          id: article.id,
          title: article.title,
          subtitle: article.subtitle,
          content: article.content,
          publishDate: article.publishDate,
          thumbnailUrl: article.thumbnailUrl, // ThumbnailUrl仍保留，以防未来需要
          authorName: article.authorName,
          source: article.source,
          tags: article.tags,
          categoryIds: article.categoryIds,
          isFeatured: article.isFeatured,
          readTimeMinutes: article.readTimeMinutes,
        ),
        onTap: onTap,
      );
    } else {
      // 对于未知类型，可以返回一个占位符或者错误提示
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text('未知资料类型', style: TextStyle(color: Colors.red)),
      );
    }
  }

  Widget _buildCategorySelector() {
    return Container(
      height: 56, // 稍微增加高度
      margin: const EdgeInsets.only(top: 20, bottom: 8), // 调整上下间距
      child:
          _courseCategories.isEmpty
              ? Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                  ), // 增加水平padding
                  child: Text(
                    '您尚未加入任何学习，请先在"探索"中加入学习',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.5), // 更微妙的颜色
                      fontSize: 15, // 稍大的字号
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.1,
                    ),
                    textAlign: TextAlign.center, // 居中对齐
                  ),
                ),
              )
              : ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _courseCategories.length,
                padding: const EdgeInsets.symmetric(
                  horizontal: 14,
                ), // 调整水平padding
                itemBuilder: (context, index) {
                  final courseCategory = _courseCategories[index];
                  final isSelected = courseCategory.id == _selectedCategoryId;

                  return _buildCourseCategoryCard(
                    courseCategory: courseCategory,
                    isSelected: isSelected,
                    onTap: () => _selectCategory(courseCategory.id),
                  );
                },
              ),
    );
  }

  // 构建课程分类卡片 - Apple风格的选择按钮
  Widget _buildCourseCategoryCard({
    required CourseCategoryItem courseCategory,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6), // 稍微调整间距
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          HapticFeedback.selectionClick();
          onTap();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 12,
          ), // 增大内边距
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Colors.white.withOpacity(0.15) // 选中状态的微妙背景
                    : Colors.white.withOpacity(0.05), // 未选中状态的极淡背景
            borderRadius: BorderRadius.circular(22), // 更大的圆角，Apple风格
            border: Border.all(
              color:
                  isSelected
                      ? Colors.white.withOpacity(0.3)
                      : Colors.white.withOpacity(0.1), // 微妙的边框
              width: 0.5,
            ),
          ),
          child: Text(
            courseCategory.name,
            style: TextStyle(
              color:
                  isSelected
                      ? Colors.white
                      : Colors.white.withOpacity(0.7), // 更微妙的颜色变化
              fontWeight:
                  isSelected ? FontWeight.w600 : FontWeight.w500, // Apple风格的字重
              fontSize: 14,
              letterSpacing: 0.1, // 微调字符间距
            ),
          ),
        ),
      ),
    );
  }

  // 静态方法，从课程信息导航到资料库页面
  static void navigateFromCourse(
    BuildContext context,
    int courseId,
    String courseName,
  ) {
    // 添加轻微触觉反馈增强交互体验
    HapticFeedback.selectionClick();

    developer.log('从课程导航到资料库: courseId=$courseId, courseName=$courseName');

    Navigator.of(context).push(
      AppPageTransitions.zeroTransitionRoute(
        page: ResourceLibraryPage(courseId: courseId, courseName: courseName),
        maintainState: true,
        settings: const RouteSettings(name: 'ResourceLibraryPage'),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    // 在这里添加清理代码
  }
}
