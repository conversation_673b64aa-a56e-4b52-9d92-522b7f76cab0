import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart'
    hide
        Scaffold,
        TextField,
        TextButton,
        ElevatedButton,
        CircularProgressIndicator;
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../main/main_page.dart';
import '../register/register_page.dart';
import '../../services/social_auth_service.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

// 登录模式枚举
enum LoginMode { sms, password }

// 账号类型枚举
enum AccountType { phone, email, username }

// 账号输入模式枚举
enum AccountInputMode { phone, emailOrUsername }

// 账号验证状态
enum AccountStatus { unknown, checking, registered, notRegistered }

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  final accountController = TextEditingController();
  final codeController = TextEditingController();
  final passwordController = TextEditingController();
  final FocusNode accountFocusNode = FocusNode();
  final FocusNode codeFocusNode = FocusNode();
  final FocusNode passwordFocusNode = FocusNode();

  bool isSending = false;
  bool isLoggingIn = false;
  int countdown = 0;
  Timer? timer;
  Timer? autoVerifyTimer; // 自动验证防抖定时器
  bool showDevPanel = false;

  // 登录模式状态
  LoginMode loginMode = LoginMode.sms;

  // 账号验证状态
  AccountStatus accountStatus = AccountStatus.unknown;
  AccountType accountType = AccountType.phone;
  bool showSecondInput = false;
  bool accountVerified = false;
  bool codeInputHasContent = false;
  String? lastVerifiedAccount;

  // 账号输入模式状态
  AccountInputMode accountInputMode = AccountInputMode.phone;
  String selectedCountryCode = '+86';
  String selectedCountryName = '中国大陆';

  // 验证状态
  bool isVerifyingCode = false;
  bool hasCodeError = false; // 验证码错误状态
  bool isAutoClearing = false; // 标记是否正在自动清空

  // 动画控制器（移除了不需要的渐变动画）
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _inputController;
  late AnimationController _pulseController;

  // 动画
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _inputAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    accountController.text = '***********';

    // 添加账号输入框监听器
    accountController.addListener(_onAccountChanged);

    // 添加验证码输入框监听器
    codeController.addListener(_onCodeChanged);

    // 初始化动画控制器（移除了渐变动画控制器）
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _inputController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 初始化动画（移除了渐变动画）
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _inputAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _inputController, curve: Curves.easeInOut),
    );

    _pulseAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween(begin: 1.0, end: 1.08), weight: 1),
      TweenSequenceItem(tween: Tween(begin: 1.08, end: 1.0), weight: 1),
    ]).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // 启动动画
    _startAnimations();
  }

  void _startAnimations() {
    // 移除了动态渐变动画，只保留入场动画
    Future.delayed(const Duration(milliseconds: 100), () {
      _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  // 监听账号输入变化
  void _onAccountChanged() {
    final account = accountController.text.trim();
    print('🔄 账号输入变化: "$account"');

    // 如果账号已验证过，检查当前账号是否与上次验证的账号不同
    if (accountVerified && lastVerifiedAccount != null) {
      // 只要当前账号与上次验证的账号不完全一致，就立即重置
      if (account != lastVerifiedAccount) {
        print('🔄 账号已改变，重置状态');
        setState(() {
          accountVerified = false;
          showSecondInput = false;
          accountStatus = AccountStatus.unknown;
          lastVerifiedAccount = null; // 清空上次验证的账号记录
          // 清空第二输入框的内容
          codeController.clear();
          passwordController.clear();
          codeInputHasContent = false;
          hasCodeError = false;
          // 重置倒计时
          timer?.cancel();
          countdown = 0;
        });
        _inputController.reverse();

        // 如果账号完全清空了，直接返回
        if (account.isEmpty) {
          return;
        }
      }
    }

    // 如果账号为空，重置所有状态
    if (account.isEmpty) {
      print('🔄 账号为空，重置所有状态');
      setState(() {
        accountStatus = AccountStatus.unknown;
        showSecondInput = false;
        accountVerified = false;
        lastVerifiedAccount = null;
        codeController.clear();
        passwordController.clear();
        codeInputHasContent = false;
        hasCodeError = false;
        timer?.cancel();
        countdown = 0;
      });
      _inputController.reverse();
      return;
    }

    // 检测账号类型
    _detectAccountType(account);
    print('🔍 检测到账号类型: $accountType');

    // 如果输入完整，但还未验证过，则自动触发验证
    final isComplete = _isAccountComplete(account);
    print('🔍 账号是否完整: $isComplete');
    print('🔍 账号是否已验证: $accountVerified');

    if (isComplete && !accountVerified) {
      print('✅ 满足自动验证条件，启动防抖定时器');
      // 取消之前的自动验证定时器
      autoVerifyTimer?.cancel();

      // 设置防抖延迟，500ms后自动验证
      autoVerifyTimer = Timer(const Duration(milliseconds: 500), () {
        print('⏰ 防抖定时器触发，检查验证条件');
        if (mounted &&
            accountController.text.trim() == account &&
            !accountVerified &&
            accountStatus != AccountStatus.checking) {
          print('🚀 开始执行自动验证');
          _verifyAccount();
        } else {
          print('❌ 验证条件不满足:');
          print('  - mounted: $mounted');
          print('  - 当前账号: "${accountController.text.trim()}"');
          print('  - 预期账号: "$account"');
          print('  - accountVerified: $accountVerified');
          print('  - accountStatus: $accountStatus');
        }
      });
    }
  }

  // 监听验证码输入变化
  void _onCodeChanged() {
    final code = codeController.text.trim();
    setState(() {
      codeInputHasContent = code.isNotEmpty;
      // 只有在用户主动输入时才清除错误信息和错误状态
      if (!isAutoClearing) {
        loginErrorMessage = '';
        hasCodeError = false;
      }
    });

    // 如果验证码长度达到6位，自动执行登录验证
    if (code.length == 6 && int.tryParse(code) != null) {
      // 启动验证中动效
      setState(() {
        isVerifyingCode = true;
      });

      // 开始脉动动画
      _pulseController.repeat();

      // 添加触觉反馈
      HapticFeedback.lightImpact();

      // 添加短暂延迟，让用户看到输入完成和验证中状态
      Future.delayed(const Duration(milliseconds: 600), () {
        if (mounted && codeController.text.trim().length == 6) {
          // 停止脉动动画
          _pulseController.stop();

          setState(() {
            isVerifyingCode = false;
          });

          login();
        } else {
          // 如果不满足条件，恢复状态
          setState(() {
            isVerifyingCode = false;
          });
          _pulseController.stop();
        }
      });
    }
  }

  // 检测账号类型
  void _detectAccountType(String account) {
    if (accountInputMode == AccountInputMode.phone) {
      accountType = AccountType.phone;
    } else {
      if (_isEmail(account)) {
        accountType = AccountType.email;
      } else {
        accountType = AccountType.username;
      }
    }
  }

  // 检查账号是否完整
  bool _isAccountComplete(String account) {
    if (accountInputMode == AccountInputMode.phone) {
      return _isPhoneNumber(account);
    } else {
      if (_isEmail(account)) {
        return account.contains('@') && account.contains('.');
      } else {
        return account.length >= 3; // 用户名至少3位
      }
    }
  }

  // 检查是否为手机号
  bool _isPhoneNumber(String account) {
    // 根据选择的国家代码匹配相应的手机号格式
    switch (selectedCountryCode) {
      case '+86': // 中国大陆
        return RegExp(r'^1[3-9]\d{9}$').hasMatch(account);
      case '+852': // 中国香港
        return RegExp(r'^[569]\d{7}$').hasMatch(account);
      case '+886': // 中国台湾
        return RegExp(r'^09\d{8}$').hasMatch(account);
      case '+1': // 美国/加拿大
        return RegExp(r'^\d{10}$').hasMatch(account);
      case '+44': // 英国
        return RegExp(r'^7\d{9}$').hasMatch(account);
      case '+81': // 日本
        return RegExp(r'^[789]0\d{8}$').hasMatch(account);
      case '+82': // 韩国
        return RegExp(r'^010\d{8}$').hasMatch(account);
      case '+65': // 新加坡
        return RegExp(r'^[89]\d{7}$').hasMatch(account);
      case '+60': // 马来西亚
        return RegExp(r'^1[0-9]\d{7,8}$').hasMatch(account);
      case '+66': // 泰国
        return RegExp(r'^[89]\d{8}$').hasMatch(account);
      case '+91': // 印度
        return RegExp(r'^[6-9]\d{9}$').hasMatch(account);
      case '+61': // 澳大利亚
        return RegExp(r'^4\d{8}$').hasMatch(account);
      case '+49': // 德国
        return RegExp(r'^1[5-7]\d{8,9}$').hasMatch(account);
      case '+33': // 法国
        return RegExp(r'^[67]\d{8}$').hasMatch(account);
      default:
        // 默认使用中国大陆格式
        return RegExp(r'^1[3-9]\d{9}$').hasMatch(account);
    }
  }

  // 检查是否为邮箱
  bool _isEmail(String account) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(account);
  }

  // 获取键盘类型
  TextInputType _getKeyboardType() {
    switch (accountInputMode) {
      case AccountInputMode.phone:
        return TextInputType.phone;
      case AccountInputMode.emailOrUsername:
        return TextInputType.text;
    }
  }

  // 获取占位符文本
  String _getPlaceholder() {
    switch (accountInputMode) {
      case AccountInputMode.phone:
        return '手机号码';
      case AccountInputMode.emailOrUsername:
        return '邮箱 / 用户名';
    }
  }

  // 切换账号输入模式
  void _switchAccountInputMode() {
    HapticFeedback.selectionClick();
    setState(() {
      accountInputMode =
          accountInputMode == AccountInputMode.phone
              ? AccountInputMode.emailOrUsername
              : AccountInputMode.phone;

      // 清空账号输入框和相关状态
      accountController.clear();
      accountStatus = AccountStatus.unknown;
      showSecondInput = false;
      accountVerified = false;
      lastVerifiedAccount = null;
      codeController.clear();
      passwordController.clear();
      codeInputHasContent = false;
      hasCodeError = false;
      timer?.cancel();
      countdown = 0;
      loginErrorMessage = '';
    });
    _inputController.reverse();
  }

  // 构建国家代码选择器
  Widget _buildCountryCodeSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: _showCountryCodePicker,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              selectedCountryCode,
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              CupertinoIcons.chevron_down,
              color: Colors.white.withOpacity(0.7),
              size: 14,
            ),
          ],
        ),
      ),
    );
  }

  // 显示国家代码选择器
  void _showCountryCodePicker() {
    HapticFeedback.lightImpact();

    final countries = [
      {'name': '中国大陆', 'code': '+86'},
      {'name': '中国香港', 'code': '+852'},
      {'name': '中国台湾', 'code': '+886'},
      {'name': '美国', 'code': '+1'},
      {'name': '英国', 'code': '+44'},
      {'name': '日本', 'code': '+81'},
      {'name': '韩国', 'code': '+82'},
      {'name': '新加坡', 'code': '+65'},
      {'name': '马来西亚', 'code': '+60'},
      {'name': '泰国', 'code': '+66'},
      {'name': '印度', 'code': '+91'},
      {'name': '澳大利亚', 'code': '+61'},
      {'name': '加拿大', 'code': '+1'},
      {'name': '德国', 'code': '+49'},
      {'name': '法国', 'code': '+33'},
    ];

    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 300,
          decoration: const BoxDecoration(
            color: Color(0xFF1a1a1a),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Column(
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.white.withOpacity(0.1),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 16,
                        ),
                      ),
                    ),
                    Text(
                      '选择国家/地区',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 48), // 占位符保持居中
                  ],
                ),
              ),
              // 国家列表
              Expanded(
                child: ListView.builder(
                  itemCount: countries.length,
                  itemBuilder: (context, index) {
                    final country = countries[index];
                    final isSelected = country['code'] == selectedCountryCode;

                    return CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        HapticFeedback.selectionClick();
                        setState(() {
                          selectedCountryCode = country['code']!;
                          selectedCountryName = country['name']!;
                        });
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? Colors.white.withOpacity(0.1)
                                  : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                country['name']!,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Text(
                              country['code']!,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 8),
                              Icon(
                                CupertinoIcons.checkmark,
                                color: Colors.white.withOpacity(0.9),
                                size: 18,
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建账号输入模式切换
  Widget _buildAccountInputModeSwitch() {
    return Container(
      height: 32,
      margin: const EdgeInsets.only(top: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: _switchAccountInputMode,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  accountInputMode == AccountInputMode.phone
                      ? CupertinoIcons.at
                      : CupertinoIcons.phone,
                  size: 14,
                  color: Colors.white.withOpacity(0.6),
                ),
                const SizedBox(width: 6),
                Text(
                  accountInputMode == AccountInputMode.phone
                      ? '使用邮箱/用户名登录'
                      : '使用手机号登录',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 检查账号注册状态
  Future<void> _checkAccountStatus(String account) async {
    setState(() {
      accountStatus = AccountStatus.checking;
    });

    try {
      // 调用后端API检查账号是否注册
      String accountToCheck = account;
      // 如果是手机号模式，传递完整的手机号（包含国家代码）
      if (accountInputMode == AccountInputMode.phone &&
          accountType == AccountType.phone) {
        accountToCheck = selectedCountryCode + account;
      }

      final response = await http.post(
        Uri.parse('${AuthService.baseUrl}/api/auth/check-account'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'account': accountToCheck, 'type': accountType.name}),
      );

      bool isRegistered = false;
      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        isRegistered = result['exists'] ?? false;
      } else {
        // API调用失败，显示错误并不跳转
        _showNotification('检查账号状态失败，请重试');
        setState(() {
          accountStatus = AccountStatus.unknown;
        });
        return;
      }

      setState(() {
        accountStatus =
            isRegistered
                ? AccountStatus.registered
                : AccountStatus.notRegistered;
        showSecondInput = isRegistered;
        accountVerified = isRegistered;

        // 保存验证通过的账号
        if (isRegistered) {
          lastVerifiedAccount = account;
        }

        // 如果是用户名且已注册，强制切换到密码登录模式
        if (isRegistered &&
            accountType == AccountType.username &&
            loginMode == LoginMode.sms) {
          loginMode = LoginMode.password;
        }
      });

      if (isRegistered) {
        _inputController.forward();
        // 自动聚焦到第二个输入框
        Future.delayed(const Duration(milliseconds: 300), () {
          if (loginMode == LoginMode.sms) {
            codeFocusNode.requestFocus();
            // 如果是验证码登录模式，自动发送验证码
            Future.delayed(const Duration(milliseconds: 500), () {
              sendCode();
            });
          } else {
            passwordFocusNode.requestFocus();
          }
        });
      } else {
        // 未注册，跳转到注册页面，并传递账号信息
        String accountToPass = account;
        // 如果是手机号模式，传递完整的手机号（包含国家代码）
        if (accountInputMode == AccountInputMode.phone &&
            accountType == AccountType.phone) {
          accountToPass = selectedCountryCode + account;
        }
        _navigateToRegister(
          prefilledAccount: accountToPass,
          accountType: accountType,
        );
      }
    } catch (e) {
      setState(() {
        accountStatus = AccountStatus.unknown;
      });
    }
  }

  // 切换登录模式
  void _switchLoginMode() {
    // 如果是用户名，不允许切换到验证码登录
    if (accountType == AccountType.username &&
        loginMode == LoginMode.password) {
      return;
    }

    HapticFeedback.selectionClick();
    setState(() {
      loginMode =
          loginMode == LoginMode.sms ? LoginMode.password : LoginMode.sms;
    });

    // 清空相关输入框
    if (loginMode == LoginMode.password) {
      _clearCodeByUser();
      timer?.cancel();
      countdown = 0;
    } else {
      passwordController.clear();
      // 切换到验证码模式时，如果还没有发送过验证码，自动发送
      if (countdown == 0 && !isSending) {
        Future.delayed(const Duration(milliseconds: 300), () {
          sendCode();
        });
      }
    }
  }

  // 获取账号类型描述
  String _getAccountTypeDescription() {
    switch (accountType) {
      case AccountType.phone:
        return '手机号';
      case AccountType.email:
        return '电子邮件';
      case AccountType.username:
        return '用户名';
    }
  }

  // 获取登录模式描述
  String _getLoginModeDescription() {
    if (accountType == AccountType.username && loginMode == LoginMode.sms) {
      return '用户名不支持验证码登录';
    }
    return loginMode == LoginMode.sms ? '验证码登录' : '密码登录';
  }

  // 检查当前登录模式是否可用
  bool _isLoginModeAvailable() {
    // 用户名不支持验证码登录
    return !(accountType == AccountType.username && loginMode == LoginMode.sms);
  }

  // 检查是否可以切换登录模式
  bool _canSwitchLoginMode() {
    // 只有手机号和邮箱可以切换，用户名不可以
    return accountType != AccountType.username &&
        accountStatus == AccountStatus.registered;
  }

  // 检查是否应该显示验证按钮
  bool _shouldShowVerifyButton() {
    final account = accountController.text.trim();
    // 只有在账号输入完整且未验证时显示箭头按钮
    return account.isNotEmpty &&
        _isAccountComplete(account) &&
        !accountVerified &&
        accountStatus != AccountStatus.registered;
  }

  Future<void> sendCode() async {
    final account = accountController.text.trim();
    if (account.isEmpty) {
      _showNotification('请输入账号');
      return;
    }

    String fullAccount = account;
    if (accountInputMode == AccountInputMode.phone) {
      if (!_isPhoneNumber(account)) {
        _showNotification('请输入正确的手机号格式');
        return;
      }
      fullAccount = selectedCountryCode + account;
    } else {
      if (!_isPhoneNumber(account) && !_isEmail(account)) {
        _showNotification('验证码登录仅支持手机号或邮箱');
        return;
      }
    }

    if (isSending || countdown > 0) return;

    HapticFeedback.lightImpact();

    setState(() {
      isSending = true;
    });

    final result = await AuthService.sendCodeAndGetValue(fullAccount);

    setState(() {
      if (result['success']) {
        countdown = 60;
        _showNotification(
          '验证码已发送${showDevPanel ? " (${result['code']})" : ""}',
        );

        if (showDevPanel && result['code'] != null) {
          Future.delayed(const Duration(seconds: 2), () {
            codeController.text = result['code'].toString();
          });
        }

        timer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
          setState(() {
            countdown = countdown - 1;
            if (countdown == 0) {
              t.cancel();
            }
          });
        });
      } else {
        _showNotification(result['message'] ?? '验证码发送失败，请稍后重试');
      }
      isSending = false;
    });
  }

  // 登录页面的错误提示信息
  String loginErrorMessage = '';

  // 社交登录状态
  bool isSocialLoginLoading = false;

  void _showNotification(String message) {
    setState(() {
      loginErrorMessage = message;
    });

    // 3秒后自动清除错误信息
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          loginErrorMessage = '';
        });
      }
    });
  }

  void _clearLoginError() {
    setState(() {
      loginErrorMessage = '';
      hasCodeError = false;
    });
  }

  // 用户操作引起的验证码清空（会清除错误状态）
  void _clearCodeByUser() {
    setState(() {
      hasCodeError = false;
      loginErrorMessage = '';
    });
    codeController.clear();
  }

  // 系统自动清空验证码（保持错误状态）
  void _clearCodeBySystem() {
    setState(() {
      isAutoClearing = true;
    });
    codeController.clear();
    // 清空完成后立即重置标志
    Future.microtask(() {
      if (mounted) {
        setState(() {
          isAutoClearing = false;
        });
      }
    });
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Icon(
            CupertinoIcons.exclamationmark_circle_fill,
            color: Colors.red.withOpacity(0.8),
            size: 14,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                color: Colors.red.withOpacity(0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> login() async {
    final account = accountController.text.trim();

    if (account.isEmpty) {
      _showNotification('请输入账号');
      return;
    }

    if (accountStatus != AccountStatus.registered) {
      _showNotification('请先验证账号状态');
      return;
    }

    // 根据登录模式验证不同字段
    if (loginMode == LoginMode.sms) {
      final code = codeController.text.trim();
      if (code.length != 6 || int.tryParse(code) == null) {
        _showNotification('请输入6位验证码');
        return;
      }
    } else {
      final password = passwordController.text.trim();
      if (password.isEmpty) {
        _showNotification('请输入密码');
        return;
      }
    }

    // 构建完整账号（手机号模式下包含国家代码）
    String fullAccount = account;
    if (accountInputMode == AccountInputMode.phone) {
      fullAccount = selectedCountryCode + account;
    }

    HapticFeedback.mediumImpact();

    setState(() {
      isLoggingIn = true;
    });

    try {
      final authService = AuthService();
      Map<String, dynamic> result;

      if (loginMode == LoginMode.sms) {
        // 验证码登录
        result = await authService.login(
          fullAccount,
          codeController.text.trim(),
        );
      } else {
        // 密码登录 - 使用静态方法调用
        result = await AuthService.loginWithPassword(
          fullAccount,
          passwordController.text.trim(),
        );
      }

      setState(() {
        isLoggingIn = false;
      });

      if (result['success'] == true) {
        final userId = result['user_id'] as int? ?? 0;
        final token = result['token'] as String? ?? '';

        if (userId > 0 && token.isNotEmpty) {
          Provider.of<UserProvider>(
            context,
            listen: false,
          ).setUser(userId, token);

          // 直接跳转，不显示登录成功提示
          Navigator.pushReplacement(
            context,
            CupertinoPageRoute(builder: (context) => const MainPage()),
          );
        } else {
          _showNotification('登录响应格式错误');
        }
      } else {
        final message = result['message'] as String? ?? '登录失败，请稍后重试';
        _showNotification(message);

        // 如果是验证码模式登录失败，设置错误状态并清空验证码输入框
        if (loginMode == LoginMode.sms) {
          // 添加震动反馈
          HapticFeedback.heavyImpact();
          setState(() {
            hasCodeError = true; // 设置验证码错误状态
          });

          // 3秒后自动清除错误状态
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                hasCodeError = false;
              });
            }
          });

          // 使用系统自动清空方法
          _clearCodeBySystem();
          // 短暂延迟后重新获取焦点，确保键盘弹出
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              codeFocusNode.requestFocus();
            }
          });
        }
      }
    } catch (e) {
      setState(() {
        isLoggingIn = false;
      });
      _showNotification('登录发生错误: $e');
    }
  }

  // 注册功能
  void _navigateToRegister({
    String? prefilledAccount,
    AccountType? accountType,
  }) {
    HapticFeedback.lightImpact();

    Navigator.push(
      context,
      CupertinoPageRoute(
        builder:
            (context) => RegisterPage(
              prefilledAccount: prefilledAccount,
              prefilledAccountType: accountType,
            ),
      ),
    );
  }

  // 执行社交登录
  Future<void> _performSocialLogin(SocialProvider provider) async {
    if (isSocialLoginLoading) return;

    setState(() {
      isSocialLoginLoading = true;
      loginErrorMessage = '';
    });

    try {
      HapticFeedback.lightImpact();

      // 显示加载提示
      _showNotification('正在连接${provider.displayName}...');

      // 添加Apple登录特别调试
      if (provider == SocialProvider.apple) {
        print('🍎 开始Apple登录调试...');
        final isAvailable = await SignInWithApple.isAvailable();
        print('🍎 Apple登录可用性: $isAvailable');

        if (!isAvailable) {
          _showNotification('当前设备不支持Apple登录，请检查iOS版本（需要13.0+）');
          return;
        }
      }

      // 初始化社交登录服务
      await SocialAuthService.initialize();

      // 执行社交登录
      print('🔍 开始执行${provider.displayName}登录...');
      final userInfo = await SocialAuthService.signInWith(provider);
      print('🔍 ${provider.displayName}登录返回结果: $userInfo');

      if (userInfo == null) {
        _showNotification('${provider.displayName}登录已取消');
        return;
      }

      // 向后端验证
      _showNotification('正在验证登录信息...');
      final result = await SocialAuthService.loginWithSocial(userInfo);

      if (result['success'] == true) {
        final userId = result['user_id'] as int? ?? 0;
        final token = result['token'] as String? ?? '';
        final isNewUser = result['is_new_user'] ?? false;

        if (userId > 0 && token.isNotEmpty) {
          Provider.of<UserProvider>(
            context,
            listen: false,
          ).setUser(userId, token);

          // 显示登录成功消息
          final message = isNewUser ? '欢迎！首次登录已自动完成注册' : '欢迎回来！';
          _showNotification(message);

          // 延迟一下再跳转，让用户看到成功消息
          Future.delayed(const Duration(milliseconds: 1500), () {
            if (mounted) {
              Navigator.pushReplacement(
                context,
                CupertinoPageRoute(builder: (context) => const MainPage()),
              );
            }
          });
        } else {
          _showNotification('登录响应格式错误');
        }
      } else {
        _showNotification(result['message'] ?? '${provider.displayName}登录失败');
      }
    } catch (e) {
      String errorMessage = e.toString();
      if (errorMessage.contains('用户取消')) {
        _showNotification('${provider.displayName}登录已取消');
      } else if (errorMessage.contains('未安装')) {
        _showNotification('未安装${provider.displayName}应用');
      } else if (errorMessage.contains('不支持')) {
        _showNotification('当前设备不支持${provider.displayName}登录');
      } else {
        _showNotification('${provider.displayName}登录失败: $errorMessage');
      }
    } finally {
      if (mounted) {
        setState(() {
          isSocialLoginLoading = false;
        });
      }
    }
  }

  Future<void> _devLogin({bool success = true}) async {
    setState(() => isLoggingIn = true);

    try {
      if (success) {
        await Future.delayed(const Duration(milliseconds: 500));

        const userId = 999;
        const token = 'dev_temp_token_12345';

        Provider.of<UserProvider>(
          context,
          listen: false,
        ).setUser(userId, token);

        _showNotification('开发模式登录成功');

        Future.delayed(const Duration(milliseconds: 500), () {
          if (!mounted) return;
          Navigator.pushReplacement(
            context,
            CupertinoPageRoute(builder: (context) => const MainPage()),
          );
        });
      } else {
        _showNotification('模拟登录失败');
      }
    } finally {
      if (mounted) setState(() => isLoggingIn = false);
    }
  }

  // 临时跳过登录功能 - 仅在DEBUG模式下使用
  Future<void> _skipLoginForTesting() async {
    if (!kDebugMode) return; // 确保只在调试模式下工作

    setState(() => isLoggingIn = true);
    
    try {
      // 添加短暂延迟以显示加载状态
      await Future.delayed(const Duration(milliseconds: 300));

      // 设置临时测试用户信息
      const userId = 888;
      const token = 'temp_skip_token_for_testing';

      Provider.of<UserProvider>(
        context,
        listen: false,
      ).setUser(userId, token, nickname: '测试用户');

      _showNotification('已跳过登录 (测试模式)');

      // 跳转到主页面
      Future.delayed(const Duration(milliseconds: 800), () {
        if (!mounted) return;
        Navigator.pushReplacement(
          context,
          CupertinoPageRoute(builder: (context) => const MainPage()),
        );
      });
    } finally {
      if (mounted) setState(() => isLoggingIn = false);
    }
  }

  // 手动验证账号状态
  Future<void> _verifyAccount() async {
    print('📞 _verifyAccount() 方法被调用');
    // 取消自动验证定时器
    autoVerifyTimer?.cancel();

    final account = accountController.text.trim();
    print('📞 验证账号: "$account"');

    if (account.isEmpty) {
      print('❌ 账号为空');
      _showNotification('请输入账号');
      return;
    }

    // 检测账号类型
    _detectAccountType(account);
    print('📞 账号类型: $accountType');

    // 检查账号格式是否正确
    if (!_isAccountComplete(account)) {
      String message = '';
      if (accountType == AccountType.phone) {
        message = '请输入正确的手机号格式';
      } else if (accountType == AccountType.email) {
        message = '请输入正确的邮箱格式';
      } else {
        message = '用户名至少需要3个字符';
      }
      print('❌ 账号格式验证失败: $message');
      _showNotification(message);
      return;
    }

    print('✅ 账号格式验证通过，开始API调用');
    HapticFeedback.lightImpact();

    setState(() {
      accountStatus = AccountStatus.checking;
      showSecondInput = false;
    });

    _inputController.reverse();

    try {
      // 调用后端API检查账号是否注册
      String accountToCheck = account;
      // 如果是手机号模式，传递完整的手机号（包含国家代码）
      if (accountInputMode == AccountInputMode.phone &&
          accountType == AccountType.phone) {
        accountToCheck = selectedCountryCode + account;
      }

      // 尝试多个URL进行网络连接回退
      http.Response? response;
      String? successUrl;

      final urls = [AuthService.baseUrl, ...AuthService.fallbackUrls];

      for (String baseUrl in urls) {
        final apiUrl = '$baseUrl/api/auth/check-account';
        print('🌐 尝试API请求:');
        print('  - URL: $apiUrl');
        print('  - 账号: $accountToCheck');
        print('  - 类型: ${accountType.name}');

        try {
          response = await http
              .post(
                Uri.parse(apiUrl),
                headers: {'Content-Type': 'application/json'},
                body: jsonEncode({
                  'account': accountToCheck,
                  'type': accountType.name,
                }),
              )
              .timeout(Duration(seconds: 5));

          if (response.statusCode == 200) {
            successUrl = baseUrl;
            print('✅ 成功连接到: $apiUrl');
            break;
          }
        } catch (e) {
          print('❌ 连接失败 $apiUrl: $e');
          continue;
        }
      }

      if (response == null) {
        print('❌ 所有URL都连接失败');
        _showNotification('无法连接到服务器，请检查网络设置');
        setState(() {
          accountStatus = AccountStatus.unknown;
          showSecondInput = false;
        });
        return;
      }

      print('🌐 API响应:');
      print('  - 状态码: ${response.statusCode}');
      print('  - 响应体: ${response.body}');

      bool isRegistered = false;
      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        isRegistered = result['exists'] ?? false;
        print('✅ API调用成功，账号已注册: $isRegistered');
      } else {
        // API调用失败，显示错误并不跳转
        print('❌ API调用失败，状态码: ${response.statusCode}');
        _showNotification('检查账号状态失败，请重试');
        setState(() {
          accountStatus = AccountStatus.unknown;
          showSecondInput = false;
        });
        return;
      }

      setState(() {
        accountStatus =
            isRegistered
                ? AccountStatus.registered
                : AccountStatus.notRegistered;
        showSecondInput = isRegistered;
        accountVerified = isRegistered;

        // 保存验证通过的账号
        if (isRegistered) {
          lastVerifiedAccount = account;
        }
      });

      if (isRegistered) {
        _inputController.forward();
        // 自动聚焦到第二个输入框
        Future.delayed(const Duration(milliseconds: 300), () {
          if (loginMode == LoginMode.sms) {
            codeFocusNode.requestFocus();
            // 如果是验证码登录模式，自动发送验证码
            Future.delayed(const Duration(milliseconds: 500), () {
              sendCode();
            });
          } else {
            passwordFocusNode.requestFocus();
          }
        });
      } else {
        // 直接跳转到注册页面，并传递账号信息
        String accountToPass = account;
        // 如果是手机号模式，传递完整的手机号（包含国家代码）
        if (accountInputMode == AccountInputMode.phone &&
            accountType == AccountType.phone) {
          accountToPass = selectedCountryCode + account;
        }
        _navigateToRegister(
          prefilledAccount: accountToPass,
          accountType: accountType,
        );
      }
    } catch (e) {
      print('❌ API调用异常: $e');
      setState(() {
        accountStatus = AccountStatus.unknown;
        showSecondInput = false;
      });
      _showNotification('网络连接失败，请检查网络设置');
    }
  }

  @override
  void dispose() {
    timer?.cancel();
    autoVerifyTimer?.cancel();
    accountController.dispose();
    codeController.dispose();
    passwordController.dispose();
    accountFocusNode.dispose();
    codeFocusNode.dispose();
    passwordFocusNode.dispose();
    // 移除了渐变动画控制器的释放
    _slideController.dispose();
    _fadeController.dispose();
    _inputController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
          HapticFeedback.selectionClick();
        },
        child: Stack(
          children: [
            // 背景容器
            _buildStaticBackground(),

            // 主要内容
            SafeArea(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight:
                        MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        // 顶部空间
                        const SizedBox(height: 40),

                        // 主要内容区域
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 40),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // 标题区域
                                _buildHeader(),

                                const SizedBox(height: 50),

                                // 纯OAuth登录说明
                                _buildOAuthOnlyMessage(),

                                const SizedBox(height: 40),

                                // 社交登录按钮（主要登录方式）
                                _buildSocialLogins(),

                                const SizedBox(height: 30),

                                // 临时跳过登录按钮（仅在DEBUG模式下显示）
                                if (kDebugMode) _buildSkipLoginButton(),
                              ],
                            ),
                          ),
                        ),

                        // 开发面板
                        if (showDevPanel) _buildDevPanel(),

                        // 底部空间
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStaticBackground() {
    return Container(
      // 基础深色渐变层 - 与应用主题一致的黑色基底
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF000000), // 纯黑 - 与应用主题保持一致
            Color(0xFF0E0E0E), // 深黑
            Color(0xFF1a1a1a), // 深灰
            Color(0xFF2a2a2a), // 中灰 - 增加层次
            Color(0xFF0E0E0E), // 深黑
            Color(0xFF000000), // 纯黑
          ],
          stops: [0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
        ),
      ),
      child: Container(
        // 微妙的深度渐变层 - 营造空间感
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0x15222222), // 极淡的炭黑色调
              Color(0x08333333), // 透明深灰过渡
              Color(0x10111111), // 微妙的深色调节
              Color(0x05222222), // 极淡炭黑
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Container(
          // 右上角极微妙的白光效果 - 模拟光源
          decoration: const BoxDecoration(
            gradient: RadialGradient(
              center: Alignment(0.8, -0.8),
              radius: 1.8,
              colors: [
                Color(0x08FFFFFF), // 极淡白光核心
                Color(0x04E0E0E0), // 浅灰光晕
                Color(0x02CCCCCC), // 微妙光影
                Colors.transparent,
              ],
              stops: [0.0, 0.3, 0.6, 1.0],
            ),
          ),
          child: Container(
            // 左下角深色区域增强
            decoration: const BoxDecoration(
              gradient: RadialGradient(
                center: Alignment(-0.7, 0.8),
                radius: 1.2,
                colors: [
                  Color(0x15000000), // 更深的黑色区域
                  Color(0x08111111), // 深灰过渡
                  Colors.transparent,
                ],
                stops: [0.0, 0.5, 1.0],
              ),
            ),
            child: Container(
              // 左上角微妙的紫色氛围 - 呼应应用的紫色主题元素
              decoration: const BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment(-0.6, -0.5),
                  radius: 1.0,
                  colors: [
                    Color(0x054c2956), // 极淡的紫色 - 与应用紫色主题呼应
                    Color(0x03443044), // 深紫灰
                    Color(0x02221122), // 微妙过渡
                    Colors.transparent,
                  ],
                  stops: [0.0, 0.4, 0.7, 1.0],
                ),
              ),
              child: Container(
                // 顶部中央极微妙的光效
                decoration: const BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(0.0, -0.9),
                    radius: 0.8,
                    colors: [
                      Color(0x06FFFFFF), // 极淡白光
                      Color(0x03F5F5F5), // 浅色光晕
                      Colors.transparent,
                    ],
                    stops: [0.0, 0.5, 1.0],
                  ),
                ),
                child: Container(
                  // 右下角深化区域
                  decoration: const BoxDecoration(
                    gradient: RadialGradient(
                      center: Alignment(0.7, 0.7),
                      radius: 1.0,
                      colors: [
                        Color(0x10000000), // 深黑色增强
                        Color(0x05111111), // 深灰过渡
                        Colors.transparent,
                      ],
                      stops: [0.0, 0.4, 1.0],
                    ),
                  ),
                  child: Container(
                    // 中央区域微妙的径向光效 - 增加画面中心的视觉焦点
                    decoration: const BoxDecoration(
                      gradient: RadialGradient(
                        center: Alignment(0.1, -0.1),
                        radius: 0.8,
                        colors: [
                          Color(0x04FFFFFF), // 极微妙的白光中心
                          Color(0x02E0E0E0), // 浅灰光晕
                          Color(0x01CCCCCC), // 极淡扩散
                          Colors.transparent,
                        ],
                        stops: [0.0, 0.3, 0.6, 1.0],
                      ),
                    ),
                    child: Container(
                      // 整体深度调节层 - 统一色调
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Color(0x08111111), // 顶部深灰微调
                            Color(0x02000000), // 中部透明
                            Color(0x05111111), // 底部深灰微调
                          ],
                          stops: [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            // 应用图标
            GestureDetector(
              onLongPress: () {
                HapticFeedback.heavyImpact();
                setState(() => showDevPanel = !showDevPanel);
              },
              child: Column(
                children: [
                  // 应用图标 - 与深色主题协调的设计
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        // 主要深度阴影
                        BoxShadow(
                          color: Colors.black.withOpacity(0.4),
                          offset: const Offset(0, 4),
                          blurRadius: 16,
                          spreadRadius: 0,
                        ),
                        // 微妙的顶部高光
                        BoxShadow(
                          color: Colors.white.withOpacity(0.08),
                          offset: const Offset(0, -1),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                        // 极微妙的整体光晕
                        BoxShadow(
                          color: Colors.grey.shade300.withOpacity(0.12),
                          offset: const Offset(0, 0),
                          blurRadius: 24,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.asset(
                        'assets/icons/cube_icon.png',
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // 如果图片加载失败，显示与深色主题协调的占位符
                          return Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.grey.shade800,
                                  Colors.grey.shade900,
                                  Colors.black87,
                                  Colors.grey.shade800,
                                ],
                              ),
                            ),
                            child: Icon(
                              Icons.apps,
                              color: Colors.white.withOpacity(0.8),
                              size: 60,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginForm() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // 账号输入框
            _buildAccountInput(),

            const SizedBox(height: 16),

            // 账号状态指示器
            _buildAccountStatusIndicator(),

            // 账号输入模式切换
            if (accountStatus == AccountStatus.unknown)
              _buildAccountInputModeSwitch(),

            // 动态显示的第二输入框
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 500),
              crossFadeState:
                  showSecondInput
                      ? CrossFadeState.showSecond
                      : CrossFadeState.showFirst,
              firstChild: const SizedBox.shrink(),
              secondChild: Column(
                children: [
                  const SizedBox(height: 20),
                  _buildSecondInput(),
                  // 错误信息显示
                  if (loginErrorMessage.isNotEmpty)
                    _buildErrorMessage(loginErrorMessage),
                  // 登录模式切换（小图标形式）
                  if (showSecondInput &&
                      accountStatus == AccountStatus.registered)
                    _buildModeSwitch(),
                  // "无法登录？"文字
                  if (showSecondInput &&
                      accountStatus == AccountStatus.registered)
                    _buildLoginTrouble(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInput() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          // 国家代码选择器（仅在手机号模式下显示）
          if (accountInputMode == AccountInputMode.phone)
            _buildCountryCodeSelector(),

          Expanded(
            child: CupertinoTextField(
              controller: accountController,
              focusNode: accountFocusNode,
              keyboardType: _getKeyboardType(),
              textInputAction: TextInputAction.go,
              onSubmitted: (_) => _verifyAccount(),
              placeholder: _getPlaceholder(),
              placeholderStyle: TextStyle(
                color: Colors.white.withOpacity(0.5),
                fontSize: 16,
                fontWeight: FontWeight.w400,
                letterSpacing: 0.5,
              ),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
              decoration: const BoxDecoration(),
              padding: EdgeInsets.symmetric(
                horizontal:
                    accountInputMode == AccountInputMode.phone ? 12 : 20,
                vertical: 16,
              ),
              onTap: () => HapticFeedback.selectionClick(),
            ),
          ),
          // 验证按钮 - 只在需要时显示
          if (_shouldShowVerifyButton())
            Container(
              width: 56,
              height: 56,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (accountStatus == AccountStatus.checking) {
                    return;
                  }
                  _verifyAccount();
                },
                child:
                    accountStatus == AccountStatus.checking
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CupertinoActivityIndicator(
                            color: Colors.white,
                          ),
                        )
                        : Stack(
                          children: [
                            Icon(
                              CupertinoIcons.arrow_right_circle,
                              color:
                                  accountVerified
                                      ? Colors.green.withOpacity(0.7)
                                      : Colors.white.withOpacity(0.7),
                              size: 24,
                            ),
                            if (accountVerified)
                              Positioned(
                                right: 0,
                                top: 0,
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: Colors.green,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ),
                          ],
                        ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAccountStatusIndicator() {
    if (accountStatus == AccountStatus.unknown) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 24,
      child: Row(
        children: [
          if (accountStatus == AccountStatus.checking) ...[
            const SizedBox(
              width: 12,
              height: 12,
              child: CupertinoActivityIndicator(color: Colors.white),
            ),
            const SizedBox(width: 8),
            Text(
              '正在验证账号...',
              style: TextStyle(
                color: Colors.white.withOpacity(0.6),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ] else if (accountStatus == AccountStatus.registered) ...[
            Icon(
              CupertinoIcons.checkmark_circle_fill,
              color: Colors.green.withOpacity(0.8),
              size: 14,
            ),
            const SizedBox(width: 6),
            Text(
              '${_getAccountTypeDescription()}已注册',
              style: TextStyle(
                color: Colors.green.withOpacity(0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ] else if (accountStatus == AccountStatus.notRegistered) ...[
            Icon(
              CupertinoIcons.exclamationmark_circle_fill,
              color: Colors.orange.withOpacity(0.8),
              size: 14,
            ),
            const SizedBox(width: 6),
            Text(
              '${_getAccountTypeDescription()}未注册',
              style: TextStyle(
                color: Colors.orange.withOpacity(0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSecondInput() {
    if (loginMode == LoginMode.sms) {
      return _buildCodeInputWithIntegratedButton();
    } else {
      return _buildModernTextField(
        controller: passwordController,
        focusNode: passwordFocusNode,
        placeholder: '密码',
        isPassword: true,
        onSubmitted: () => login(),
        showArrow: true,
        onArrowPressed: login,
      );
    }
  }

  // 验证码输入框与按钮一体化
  Widget _buildCodeInputWithIntegratedButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        // 确定容器应该用哪种颜色 - 根据错误状态改变颜色
        Color containerColor =
            hasCodeError
                ? Colors.red.withOpacity(0.08)
                : Colors.white.withOpacity(0.05);
        Color borderColor =
            hasCodeError
                ? Colors.red.withOpacity(0.4)
                : Colors.white.withOpacity(0.1);

        Widget inputContainer = Container(
          height: 56,
          decoration: BoxDecoration(
            color: containerColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: borderColor,
              width: hasCodeError ? 1.5 : 1, // 错误状态时增加边框宽度
            ),
            // 移除阴影效果
          ),
          child: Row(
            children: [
              Expanded(
                child: CupertinoTextField(
                  controller: codeController,
                  focusNode: codeFocusNode,
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  placeholder: '验证码',
                  placeholderStyle: TextStyle(
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    letterSpacing: 0.5,
                  ),
                  style: TextStyle(
                    color:
                        hasCodeError
                            ? Colors.red.withOpacity(0.9)
                            : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                  decoration: const BoxDecoration(),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  onTap: () => HapticFeedback.selectionClick(),
                  onSubmitted:
                      codeInputHasContent && !isVerifyingCode
                          ? (_) => login()
                          : null,
                  enabled: !isVerifyingCode,
                ),
              ),
              // 动态按钮：发送验证码 或 箭头登录
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: codeInputHasContent ? 56 : 120, // 有内容时缩小为箭头大小
                height: 56,
                child: CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed:
                      isVerifyingCode
                          ? null
                          : codeInputHasContent
                          ? login // 有内容时点击登录
                          : (isSending || countdown > 0)
                          ? null
                          : sendCode, // 无内容时发送验证码
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          (isSending || countdown > 0 || isVerifyingCode) &&
                                  !codeInputHasContent
                              ? Colors.white.withOpacity(0.05)
                              : Colors.white.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color:
                            (isSending || countdown > 0 || isVerifyingCode) &&
                                    !codeInputHasContent
                                ? Colors.white.withOpacity(0.1)
                                : Colors.white.withOpacity(0.15),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child:
                          isVerifyingCode
                              ? SizedBox(
                                // 显示验证中加载
                                width: 20,
                                height: 20,
                                child: CupertinoActivityIndicator(
                                  color: Colors.white.withOpacity(0.7),
                                ),
                              )
                              : codeInputHasContent
                              ? Icon(
                                // 显示箭头
                                CupertinoIcons.arrow_right_circle,
                                color: Colors.white.withOpacity(0.7),
                                size: 24,
                              )
                              : isSending
                              ? const SizedBox(
                                // 显示发送中加载
                                width: 20,
                                height: 20,
                                child: CupertinoActivityIndicator(
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                // 显示发送按钮或倒计时
                                countdown > 0 ? '${countdown}s' : '发送',
                                style: TextStyle(
                                  color:
                                      (isSending || countdown > 0)
                                          ? Colors.white.withOpacity(0.5)
                                          : Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 0.5,
                                ),
                              ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );

        // 应用脉冲动画
        if (isVerifyingCode) {
          inputContainer = Transform.scale(
            scale: _pulseAnimation.value,
            child: inputContainer,
          );
        }

        return inputContainer;
      },
    );
  }

  Widget _buildModeSwitch() {
    // 只有在可以切换模式时才显示
    if (!_canSwitchLoginMode()) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: _isLoginModeAvailable() ? _switchLoginMode : null,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  loginMode == LoginMode.sms
                      ? CupertinoIcons.lock_shield
                      : CupertinoIcons.chat_bubble,
                  size: 16,
                  color:
                      _isLoginModeAvailable()
                          ? Colors.white.withOpacity(0.7)
                          : Colors.white.withOpacity(0.3),
                ),
                const SizedBox(width: 6),
                Text(
                  loginMode == LoginMode.sms ? '切换到密码登录' : '切换到验证码登录',
                  style: TextStyle(
                    color:
                        _isLoginModeAvailable()
                            ? Colors.white.withOpacity(0.7)
                            : Colors.white.withOpacity(0.3),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String placeholder,
    TextInputType? keyboardType,
    int? maxLength,
    bool isPassword = false,
    VoidCallback? onSubmitted,
    bool showArrow = false,
    VoidCallback? onArrowPressed,
  }) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Row(
        children: [
          Expanded(
            child: CupertinoTextField(
              controller: controller,
              focusNode: focusNode,
              keyboardType: keyboardType,
              maxLength: maxLength,
              obscureText: isPassword,
              placeholder: placeholder,
              placeholderStyle: TextStyle(
                color: Colors.white.withOpacity(0.5),
                fontSize: 16,
                fontWeight: FontWeight.w400,
                letterSpacing: 0.5,
              ),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
              decoration: const BoxDecoration(),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              onTap: () => HapticFeedback.selectionClick(),
              onSubmitted: onSubmitted != null ? (_) => onSubmitted() : null,
            ),
          ),
          if (showArrow)
            Container(
              width: 56,
              height: 56,
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: onArrowPressed,
                child: Icon(
                  CupertinoIcons.arrow_right_circle,
                  color: Colors.white.withOpacity(0.7),
                  size: 24,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoginTrouble() {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          HapticFeedback.selectionClick();
          // TODO: 处理无法登录的情况
        },
        child: Text(
          '无法登录？',
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildOAuthOnlyMessage() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            Text(
              '选择你喜欢的方式登录',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 18,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '首次登录将自动为你创建账户',
              style: TextStyle(
                color: Colors.white.withOpacity(0.6),
                fontSize: 14,
                fontWeight: FontWeight.w400,
                letterSpacing: 0.3,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '安全 · 便捷 · 无需密码',
              style: TextStyle(
                color: Colors.white.withOpacity(0.4),
                fontSize: 12,
                fontWeight: FontWeight.w400,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialLogins() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // 第一行：Apple、Google
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 苹果登录
                _buildEnhancedSocialLoginButton(
                  provider: SocialProvider.apple,
                  icon: FontAwesomeIcons.apple,
                  label: 'Apple',
                ),

                const SizedBox(width: 20),

                // 谷歌登录
                _buildEnhancedSocialLoginButton(
                  provider: SocialProvider.google,
                  icon: FontAwesomeIcons.google,
                  label: 'Google',
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 第二行：微信、QQ
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 微信登录
                _buildEnhancedSocialLoginButton(
                  provider: SocialProvider.wechat,
                  icon: FontAwesomeIcons.weixin,
                  label: '微信',
                ),

                const SizedBox(width: 20),

                // QQ登录
                _buildEnhancedSocialLoginButton(
                  provider: SocialProvider.qq,
                  icon: FontAwesomeIcons.qq,
                  label: 'QQ',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedSocialLoginButton({
    required SocialProvider provider,
    required IconData icon,
    required String label,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: isLoggingIn ? null : () => _performSocialLogin(provider),
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withOpacity(0.15), width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(icon, color: Colors.white.withOpacity(0.9), size: 28),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 13,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialLoginButtonWithFaIcon({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onPressed,
      child: Container(
        width: 120,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.08),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.15), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(icon, color: Colors.white.withOpacity(0.9), size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 11,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDevPanel() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.08),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.withOpacity(0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'DEVELOPMENT PANEL',
            style: TextStyle(
              color: Colors.amber.withOpacity(0.9),
              fontWeight: FontWeight.w700,
              fontSize: 12,
              letterSpacing: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDevButton(
                  'SUCCESS',
                  Colors.green,
                  () => _devLogin(success: true),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDevButton(
                  'FAILURE',
                  Colors.red,
                  () => _devLogin(success: false),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDevButton(String text, Color color, VoidCallback onPressed) {
    return Container(
      height: 36,
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: isLoggingIn ? null : onPressed,
        child: Container(
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: color.withOpacity(0.3), width: 0.5),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                color: color.withOpacity(0.9),
                fontSize: 11,
                fontWeight: FontWeight.w700,
                letterSpacing: 1,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSkipLoginButton() {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            const SizedBox(height: 20),
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: isLoggingIn ? null : _skipLoginForTesting,
              child: Container(
                width: double.infinity,
                height: 50,
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.withOpacity(0.4),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      CupertinoIcons.forward,
                      color: Colors.orange.withOpacity(0.9),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '跳过登录 (仅测试)',
                      style: TextStyle(
                        color: Colors.orange.withOpacity(0.9),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '⚠️ 此按钮仅在开发测试时显示',
              style: TextStyle(
                color: Colors.orange.withOpacity(0.6),
                fontSize: 12,
                fontWeight: FontWeight.w400,
                letterSpacing: 0.3,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegisterButton() {
    // 在纯OAuth模式下，注册按钮重定向到说明消息
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        HapticFeedback.lightImpact();
        _showNotification('请选择上方任一方式登录，首次使用将自动注册');
      },
      child: Text(
        '没有账户？',
        style: TextStyle(
          color: Colors.white.withOpacity(0.7),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
