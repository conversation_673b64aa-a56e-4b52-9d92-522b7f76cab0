import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui'; // For ImageFilter
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/user_provider.dart';
import 'course_detail_page.dart';
import '../auth/login_page.dart';
import '../../widgets/user_avatar_button.dart';
import '../../widgets/animated_avatar_widget.dart';
import '../../widgets/page_title_bar.dart';
import '../../widgets/gradient_course_card.dart';
import '../../services/auth_service.dart';
import '../../services/api_service.dart';
import '../../constants/fonts.dart';

class CoursePage extends StatefulWidget {
  const CoursePage({super.key});

  @override
  State<CoursePage> createState() => _CoursePageState();
}

class _CoursePageState extends State<CoursePage> {
  Future<List<Map<String, dynamic>>>? courseFuture;
  Map<String, dynamic>? selectedCourse;
  bool _isLoading = false;
  String? _errorMessage;

  String getBaseUrl() {
    return AuthService.baseUrl; // 使用AuthService中的baseUrl常量
  }

  @override
  void initState() {
    super.initState();
    _loadCourses();
  }

  void _loadCourses() {
    courseFuture = fetchCourses();
  }

  Future<List<Map<String, dynamic>>> fetchCourses() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    final Map<String, String> headers = {};
    if (userProvider.isLoggedIn) {
      headers['Authorization'] = 'Bearer ${userProvider.token}';
    }

    try {
      // 使用新的分层分类接口，只获取两级数据
      final response = await http.get(
        Uri.parse(
          '${getBaseUrl()}/api/categories/hierarchical?max_level=2&_t=${DateTime.now().millisecondsSinceEpoch}',
        ),
        headers: headers,
      );

      if (response.statusCode == 200) {
        // 打印响应内容以便调试
        print('分层分类API响应: ${response.body}');

        // 解析JSON响应
        final dynamic responseData = jsonDecode(response.body);

        if (responseData is Map && responseData.containsKey('data')) {
          // 新API格式：包含success和data字段
          final List<dynamic> categoriesList =
              responseData['data'] as List<dynamic>;

          // 转换数据格式，保持与原有UI兼容
          return categoriesList
              .map((item) => item as Map<String, dynamic>)
              .toList();
        } else if (responseData is List) {
          // 如果响应直接是一个列表（旧格式兼容）
          return responseData
              .map((item) => item as Map<String, dynamic>)
              .toList();
        } else {
          throw Exception(
            AppLocalizations.of(context)!.unexpectedDataStructure,
          );
        }
      } else {
        print('分层分类API请求失败，使用模拟数据: ${response.statusCode}');
        return ApiService.getMockCourseData();
      }
    } catch (e) {
      print('加载分层分类出错，使用模拟数据: $e');
      return ApiService.getMockCourseData();
    }
  }

  void selectCourse(Map<String, dynamic> course) {
    setState(() {
      if (selectedCourse?['name'] == course['name']) {
        selectedCourse = null;
      } else {
        selectedCourse = course;
      }
    });
  }

  Future<void> joinCourse(int courseId) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (!userProvider.isLoggedIn) {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const LoginPage()),
      );

      // 用户取消登录或登录失败
      if (!userProvider.isLoggedIn) {
        return;
      }
    }

    try {
      // 使用新的课程注册接口
      final response = await http.post(
        Uri.parse('${getBaseUrl()}/api/courses/$courseId/enroll'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${userProvider.token}',
        },
        body: jsonEncode({'user_id': userProvider.userId}),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.of(context)!.joinCourseSuccess),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                responseData['message'] ??
                    AppLocalizations.of(context)!.joinCourseFailed,
              ),
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.joinCourseFailed),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.error(e.toString())),
        ),
      );
    }
  }

  Widget buildCategory(String title, List<Map<String, dynamic>> items) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 10),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                items.map((item) {
                  bool isSelected = selectedCourse?['name'] == item['name'];

                  Widget chipContent = InterestChip(
                    label: item['name'],
                    selected: isSelected,
                    onTap: () => selectCourse(item),
                    textStyle: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                  );

                  if (isSelected) {
                    // Selected state: Solid blue-purple background
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(16.0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF5A6BFA), // Solid color
                          borderRadius: BorderRadius.circular(16.0),
                          // Add a border with the same width as the unselected state to maintain size consistency
                          border: Border.all(
                            color: const Color(
                              0xFF5A6BFA,
                            ), // Same as background, or Colors.transparent
                            width: 1.0,
                          ),
                        ),
                        child: chipContent,
                      ),
                    );
                  } else {
                    // Unselected state: Glassmorphic effect
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(16.0),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.12),
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                              width: 1.0,
                            ),
                          ),
                          child: chipContent,
                        ),
                      ),
                    );
                  }
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget buildDetailCard(BuildContext context) {
    if (selectedCourse == null) return const SizedBox.shrink();

    final userProvider = Provider.of<UserProvider>(context);

    return GradientCourseCard(
      key: ValueKey(selectedCourse!['name']),
      courseName: selectedCourse!['name'],
      description: selectedCourse!['description'],
      highlight: selectedCourse!['highlight'],
      categoryId: int.tryParse(selectedCourse!['id'].toString()),
      onTap: () {
        if (userProvider.isLoggedIn) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (_) => CourseDetailPage(
                    courseName: selectedCourse!['name'],
                    description: selectedCourse!['description'],
                    highlight: selectedCourse!['highlight'],
                    categoryId: int.parse(selectedCourse!['id'].toString()),
                  ),
            ),
          );
        } else {
          showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: Text(AppLocalizations.of(context)!.loginRequired),
                  content: Text(AppLocalizations.of(context)!.pleaseLoginFirst),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(AppLocalizations.of(context)!.cancel),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LoginPage(),
                          ),
                        ).then((_) {
                          // 重新加载状态
                          setState(() {});
                        });
                      },
                      child: Text(AppLocalizations.of(context)!.login),
                    ),
                  ],
                ),
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        fit: StackFit.expand,
        children: [
          Positioned.fill(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: Image.asset(
                'assets/images/traditional_quiz_bg.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  print('❌ 背景图片加载失败: $error');
                  // 出错时使用备选背景
                  return Image.asset(
                    'assets/images/learning_bg3.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  );
                },
              ),
            ),
          ),
          SafeArea(
            child: Stack(
              children: [
                // 整体内容列
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 添加SizedBox以匹配学习页面上方的间距
                    const SizedBox(height: 16),

                    // 标题行
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 左侧标题部分
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.only(left: 20),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child:
                                  Platform.isIOS &&
                                          Localizations.localeOf(
                                                context,
                                              ).languageCode !=
                                              'zh'
                                      // 英文标题使用加强版超粗字体效果
                                      ? AppFonts.createBoldTextStack(
                                        AppLocalizations.of(
                                              context,
                                            )?.coursePageTitle ??
                                            "课程",
                                        fontSize: 36,
                                        letterSpacing: -0.5, // 使用负值使字符更加紧凑
                                      )
                                      // 中文标题使用原有方法
                                      : Text(
                                        AppLocalizations.of(
                                              context,
                                            )?.coursePageTitle ??
                                            "课程",
                                        style: AppFonts.createTitleStyle(
                                          fontSize: 36,
                                          isLatinText:
                                              Localizations.localeOf(
                                                context,
                                              ).languageCode !=
                                              'zh',
                                        ),
                                      ),
                            ),
                          ),
                        ),

                        // 右侧头像按钮
                        Expanded(
                          flex: 1,
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 16),
                              child: AnimatedAvatarWidget(size: 36),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // 与学习页面保持一致的间距
                    const SizedBox(height: 20),

                    // 主要内容区域
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () => setState(() => selectedCourse = null),
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.only(
                              left: 20,
                              right: 20,
                              top: 0,
                              bottom: 15,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 分类选择区域
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      AppLocalizations.of(
                                        context,
                                      )!.whichCourseInterest,
                                      style: const TextStyle(
                                        fontSize: 22,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    Text(
                                      AppLocalizations.of(
                                        context,
                                      )!.courseSelectionHint,
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.white70,
                                        height: 1.4,
                                      ),
                                    ),
                                    const SizedBox(height: 15),
                                    FutureBuilder<List<Map<String, dynamic>>>(
                                      future: courseFuture,
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState ==
                                            ConnectionState.waiting) {
                                          return const Center(
                                            child: CircularProgressIndicator(),
                                          );
                                        }
                                        if (snapshot.hasError) {
                                          return Center(
                                            child: Text(
                                              AppLocalizations.of(
                                                context,
                                              )!.loadingCourseError(
                                                snapshot.error.toString(),
                                              ),
                                            ),
                                          );
                                        }
                                        final data = snapshot.data!;
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children:
                                              data.map((item) {
                                                // 获取分类名称
                                                final String categoryName =
                                                    item['category']
                                                        ?.toString() ??
                                                    AppLocalizations.of(
                                                      context,
                                                    )!.uncategorizedCourses;

                                                // 获取课程列表，添加空值检查
                                                final coursesRaw =
                                                    item['courses'];
                                                final List<Map<String, dynamic>>
                                                courses;

                                                if (coursesRaw != null &&
                                                    coursesRaw is List) {
                                                  courses =
                                                      coursesRaw
                                                          .map(
                                                            (e) => Map<
                                                              String,
                                                              dynamic
                                                            >.from(e),
                                                          )
                                                          .toList();
                                                } else {
                                                  courses =
                                                      <Map<String, dynamic>>[];
                                                }

                                                return buildCategory(
                                                  categoryName,
                                                  courses,
                                                );
                                              }).toList(),
                                        );
                                      },
                                    ),
                                    const SizedBox(height: 100),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // 底部卡片
                Positioned(
                  bottom: MediaQuery.of(context).padding.bottom + 16,
                  left: 0,
                  right: 0,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 500),
                    transitionBuilder: (child, animation) {
                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(0, 1),
                          end: Offset.zero,
                        ).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOut,
                          ),
                        ),
                        child: FadeTransition(opacity: animation, child: child),
                      );
                    },
                    child: buildDetailCard(context),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class InterestChip extends StatelessWidget {
  final String label;
  final bool selected;
  final VoidCallback onTap;
  final TextStyle textStyle;
  final EdgeInsets padding;

  const InterestChip({
    super.key,
    required this.label,
    required this.selected,
    required this.onTap,
    required this.textStyle,
    required this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: padding,
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(label, style: textStyle),
      ),
    );
  }
}
