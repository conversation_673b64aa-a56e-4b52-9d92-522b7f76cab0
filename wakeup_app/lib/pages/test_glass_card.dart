import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class TestGlassCardPage extends StatelessWidget {
  const TestGlassCardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 深邃夜空背景
          _buildNightSkyBackground(),
          
          // 极光效果
          _buildAuroraEffects(),
          
          // 主要内容
          SafeArea(
            child: _buildGlowingGlassCard(),
          ),
        ],
      ),
    );
  }

  // 构建简洁紫色背景
  Widget _buildNightSkyBackground() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF9C27B0), // 紫色
            Color(0xFF673AB7), // 深紫色
          ],
        ),
      ),
    );
  }

  // 构建极光效果 - 简化版
  Widget _buildAuroraEffects() {
    return Container(); // 移除复杂的极光效果
  }

  // 构建纯透明玻璃卡片
  Widget _buildGlowingGlassCard() {
    return Center(
      child: Container(
        width: 320,
        height: 420,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          // 纯透明玻璃效果
          color: Colors.white.withValues(alpha: 0.1),
          // 简洁的边框
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
          // 简洁的阴影
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 8),
              blurRadius: 32,
              spreadRadius: -8,
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Finder 图标 - 简化版
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white.withValues(alpha: 0.2),
                ),
                child: const Icon(
                  CupertinoIcons.folder_fill,
                  size: 40,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 标题
              const Text(
                'Apple MacBook',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // 副标题
              Text(
                '16 inch, November 2025',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.7),
                  letterSpacing: 0.3,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // 规格信息
              Column(
                children: [
                  _buildSpecRow('Chip', 'Apple M10 Max'),
                  _buildSpecRow('Memory', '256GB'),
                  _buildSpecRow('Startup disk', 'Macintosh HD'),
                  _buildSpecRow('Serial number', 'X0010XUJYSZX'),
                  _buildSpecRow('macOS', 'Sequoia 20.0'),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // More Info 按钮 - 简化版
              Container(
                width: double.infinity,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  color: Colors.white.withValues(alpha: 0.2),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: const Center(
                  child: Text(
                    'More Info',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建规格行
  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.6),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
