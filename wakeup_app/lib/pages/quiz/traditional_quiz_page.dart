import 'dart:async';
import 'dart:ui';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/physics.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../models/quiz_model.dart';
import '../../providers/user_provider.dart';
import '../../services/quiz_service.dart';
import '../../widgets/gradient_border_glass_card.dart';
import '../../widgets/collapsible_section.dart';
import '../../widgets/memory_tips_card.dart';
import '../../widgets/comments_card.dart';
import '../../widgets/back_button.dart';
import '../../widgets/custom_menu_button.dart';
import '../../widgets/filter_button.dart';
import '../../widgets/apple_style_menu.dart';
import '../../widgets/energy_button.dart';
import '../../widgets/energy_modal.dart';
import '../../widgets/energy_dialog.dart';
import '../../widgets/energy_recharge_sheet.dart';
import '../../constants/fonts.dart';
import '../../constants/quiz_constants.dart';
import '../../widgets/filter_popup.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../utils/page_transitions.dart';
import '../favorites_page.dart';
import '../wrong_questions_page.dart';
import '../study_history_page.dart';
import '../leaderboard_page.dart';
import '../../widgets/answer_area_factory.dart';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../services/auth_service.dart';

/// 传统答题页面
class TraditionalQuizPage extends StatefulWidget {
  final int courseId;
  final String courseName;
  final List<int>? questionIds; // 添加可选参数，用于接收指定的题目ID列表
  final Map<int, Map<String, dynamic>>?
  courseInfoMap; // 添加课程信息映射，用于根据题目ID获取对应课程信息

  const TraditionalQuizPage({
    super.key,
    required this.courseId,
    required this.courseName,
    this.questionIds, // 添加可选参数
    this.courseInfoMap, // 添加课程信息映射参数
  });

  @override
  State<TraditionalQuizPage> createState() => _TraditionalQuizPageState();
}

class _TraditionalQuizPageState extends State<TraditionalQuizPage>
    with TickerProviderStateMixin {
  List<Question> _questionList = [];
  List<Question> _originalQuestionList = []; // 添加原始题目列表备份
  int _currentQuestionIndex = 0;
  Question? get _currentQuestion =>
      _questionList.isNotEmpty && _currentQuestionIndex < _questionList.length
          ? _questionList[_currentQuestionIndex]
          : null;

  // 添加用于保存当前显示的课程名称的变量
  String _currentDisplayedCourseName = '';

  int? _selectedOptionId;
  QuestionInteractionState _interactionState =
      QuestionInteractionState.waitingForSelection;
  Timer? _autoNextTimer;

  // 添加计时器相关变量
  Timer? _questionTimer;
  int _questionTimeInSeconds = 0;

  bool _isLoading = true;
  String? _errorMessage;

  // 收藏状态
  bool _isCurrentQuestionFavorited = false;

  // 区域引用的GlobalKey
  final GlobalKey _questionAreaKey = GlobalKey();
  final GlobalKey _optionsAreaKey = GlobalKey();
  final GlobalKey _feedbackAreaKey = GlobalKey();
  final GlobalKey _titleKey = GlobalKey(); // 标题区域的key
  final GlobalKey _navTitleKey = GlobalKey(); // 导航栏标题的key

  // 用于控制选项和解析区域的动画
  late AnimationController _feedbackAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // 用于下拉返回的动画控制器
  late AnimationController _dragController;
  double _dragDistance = 0.0;

  // 用于水平滑动的控制器
  late AnimationController _swipeController;
  double _swipeDistance = 0.0;

  // 用于滚动控制
  ScrollController _scrollController = ScrollController();
  final ScrollController _optionsScrollController = ScrollController();

  // 各区域的展开/收起状态
  bool _showExplanation = true;
  bool _showMemoryTips = true;
  bool _showComments = true;

  // 模拟数据 - 实际项目中应从API获取
  String _memoryTips = "通过关联记忆法记住这个概念：将抽象内容与具体场景联系，如...";
  List<Map<String, dynamic>> _comments = [
    {"user": "学习者A", "content": "这道题的解释很清晰，帮助我理解了核心概念。", "time": "2小时前"},
    {"user": "学习者B", "content": "我觉得这个知识点可以结合实例来记忆更容易。", "time": "昨天"},
  ];

  // 页面颜色
  final Color _gradientStart = const Color(0xFFa9a5b1);
  final Color _gradientMiddle = const Color(0xFF4c2956);
  final Color _gradientEnd = const Color(0xFF06060e);

  final TextEditingController _commentController = TextEditingController();

  // AI问答相关状态
  final TextEditingController _questionController = TextEditingController();
  String? _aiAnswer;
  bool _isLoadingAnswer = false;
  final GlobalKey _aiAnswerKey = GlobalKey();

  // 猜你想问的问题列表
  final List<String> _suggestedQuestions = [
    '这个知识点的应用场景有哪些？',
    '为什么这个答案是正确的？',
    '如何记忆这个知识点？',
  ];

  // 标签页数据
  final List<Map<String, dynamic>> _tabItems = [
    {'icon': CupertinoIcons.bookmark, 'title': '添加到收藏库', 'isSpecial': true},
    {'icon': CupertinoIcons.collections, 'title': '前往收藏库', 'isSpecial': false},
    {'icon': CupertinoIcons.xmark_circle, 'title': '前往错题库'},
    {'icon': CupertinoIcons.clock, 'title': '学习历史'},
    {'icon': CupertinoIcons.chart_bar, 'title': '日排行榜'},
    {'icon': CupertinoIcons.doc_text, 'title': '进行模拟考', 'isSpecial': true},
    {'icon': CupertinoIcons.arrow_right_square, 'title': '退出学习'},
  ];

  // 导航栏标题的透明度
  double _navTitleOpacity = 0.0;

  bool _isUpdatingTitle = false; // 添加到类变量中

  PageController _pageController = PageController();

  // 添加一个GlobalKey用于定位侧边栏按钮
  final GlobalKey _menuButtonKey = GlobalKey();

  // 添加页面过渡动画控制器
  late AnimationController _pageEnterAnimationController;
  late Animation<double> _pageEnterOpacityAnimation;
  late Animation<double> _pageEnterScaleAnimation;

  // 标记是否是初始加载过程中
  bool _isInitialLoading = true;

  // 筛选相关变量
  FilterResult? _currentFilter;
  bool get _hasActiveFilters =>
      _currentFilter != null && !_currentFilter!.isEmpty;

  // 添加做题次数变量，默认为1
  int _attemptCount = 1;

  // 能量按钮引用
  final GlobalKey _energyButtonKey = GlobalKey();

  // 在State类中添加持久化存储相关的常量和方法
  static const String _quizStateStorageKey = 'quiz_state_';

  // 保存当前答题状态
  Future<void> _saveQuizState() async {
    try {
      if (_currentQuestion == null) return;

      final prefs = await SharedPreferences.getInstance();

      // 构建一个包含当前答题状态的Map
      final quizState = {
        'courseId': widget.courseId,
        'courseName': widget.courseName,
        'currentQuestionIndex': _currentQuestionIndex,
        'selectedOptionId': _selectedOptionId,
        'interactionState': _interactionState.index,
        'lastUpdateTime': DateTime.now().millisecondsSinceEpoch,
        'questionTimeInSeconds': _questionTimeInSeconds,
        'showExplanation': _showExplanation,
        'showMemoryTips': _showMemoryTips,
        'showComments': _showComments,
        'attemptCount': _attemptCount,
        // 添加保存问题列表的长度，用于验证
        'questionListLength': _questionList.length,
      };

      // 保存到SharedPreferences
      await prefs.setString(
        _quizStateStorageKey + widget.courseId.toString(),
        jsonEncode(quizState),
      );
      print('答题状态已保存: ${jsonEncode(quizState)}');
    } catch (e) {
      print('保存答题状态失败: $e');
    }
  }

  // 加载保存的答题状态
  Future<void> _loadQuizState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJson = prefs.getString(
        _quizStateStorageKey + widget.courseId.toString(),
      );

      if (stateJson != null) {
        final state = jsonDecode(stateJson) as Map<String, dynamic>;

        // 验证课程ID是否匹配
        if (state['courseId'] == widget.courseId) {
          // 验证问题列表长度是否一致
          final savedQuestionListLength =
              state['questionListLength'] as int? ?? 0;

          if (_questionList.isNotEmpty &&
              savedQuestionListLength == _questionList.length) {
            // 获取保存的索引
            final savedIndex = state['currentQuestionIndex'] as int;

            // 确保索引有效
            if (savedIndex >= 0 && savedIndex < _questionList.length) {
              setState(() {
                _currentQuestionIndex = savedIndex;
                _selectedOptionId = state['selectedOptionId'] as int?;
                _tempSelectedAnswer = null; // 确保临时答案被重置
                _interactionState =
                    QuestionInteractionState.values[state['interactionState']
                        as int];

                // 恢复其他状态
                _questionTimeInSeconds =
                    state['questionTimeInSeconds'] as int? ?? 0;
                _showExplanation = state['showExplanation'] as bool? ?? true;
                _showMemoryTips = state['showMemoryTips'] as bool? ?? true;
                _showComments = state['showComments'] as bool? ?? true;
                _attemptCount = state['attemptCount'] as int? ?? 1;
              });

              // 如果不是等待选择状态，恢复后应停止计时器
              if (_interactionState !=
                  QuestionInteractionState.waitingForSelection) {
                _stopQuestionTimer();
              } else {
                // 否则启动计时器
                _startQuestionTimer();
              }

              print(
                '已恢复答题状态: 当前题目索引=$_currentQuestionIndex, 互动状态=${_interactionState.toString()}',
              );

              // 在恢复状态后，如果已经有选择的答案，滚动到正确的位置
              if (_interactionState !=
                  QuestionInteractionState.waitingForSelection) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _scrollToFeedback();
                });
              }

              return;
            }
          } else {
            print(
              '问题列表长度不匹配，无法恢复状态。保存的长度: $savedQuestionListLength, 当前长度: ${_questionList.length}',
            );
          }
        }
      }

      // 如果没有有效的保存状态，或者解析失败，从第一题开始
      setState(() {
        _currentQuestionIndex = 0;
        _selectedOptionId = null;
        _tempSelectedAnswer = null; // 确保临时答案被重置
        _interactionState = QuestionInteractionState.waitingForSelection;
        _startQuestionTimer(); // 开始计时
      });
    } catch (e) {
      print('加载答题状态失败: $e');
      // 从第一题开始
      setState(() {
        _currentQuestionIndex = 0;
        _selectedOptionId = null;
        _tempSelectedAnswer = null; // 确保临时答案被重置
        _interactionState = QuestionInteractionState.waitingForSelection;
        _startQuestionTimer(); // 开始计时
      });
    }
  }

  // 清除保存的状态
  Future<void> _clearQuizState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_quizStateStorageKey + widget.courseId.toString());
      print('答题状态已清除');
    } catch (e) {
      print('清除答题状态失败: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    print('初始化TraditionalQuizPage: courseId=${widget.courseId}');

    // 初始化动画控制器
    _feedbackAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _fadeAnimation = CurvedAnimation(
      parent: _feedbackAnimationController,
      curve: Curves.easeIn,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _feedbackAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    // 初始化拖动控制器
    _dragController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // 初始化水平滑动控制器
    _swipeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..addListener(() {
      if (_swipeController.status == AnimationStatus.completed) {
        // 当动画完成时，切换到下一题
        _goToNextQuestion();
        // 重置滑动距离和动画控制器
        setState(() {
          _swipeDistance = 0;
        });
        _swipeController.reset();
      } else if (_swipeController.status == AnimationStatus.dismissed) {
        // 当动画被取消时，只重置滑动距离
        setState(() {
          _swipeDistance = 0;
        });
      } else {
        // 动画进行中，更新UI以反映当前状态
        setState(() {});
      }
    });

    // 初始化页面进入动画控制器
    _pageEnterAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _pageEnterOpacityAnimation = CurvedAnimation(
      parent: _pageEnterAnimationController,
      curve: Curves.easeOut,
    );

    _pageEnterScaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _pageEnterAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    // 添加滚动监听器
    _scrollController =
        ScrollController()
          ..addListener(_onScrollChanged)
          ..addListener(_scrollListener);

    // 启动页面进入动画
    _pageEnterAnimationController.forward();

    _initializePageData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 检查用户课程数据是否发生变化
    final userProvider = Provider.of<UserProvider>(context);
    if (userProvider.courseChanged) {
      print("答题页面: 检测到课程数据变化，重新检查课程状态");
      // 重置变化标志
      userProvider.resetCourseChanged();
      // 检查当前课程是否仍然在用户的课程列表中
      _checkUserHasCourse();
    }
  }

  // 检查用户是否仍然拥有该课程
  Future<void> _checkUserHasCourse() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) return;

    try {
      final response = await http.get(
        Uri.parse(
          "${AuthService.baseUrl}/api/user_course/list/${userProvider.userId}",
        ),
        headers: {
          'Authorization': 'Bearer ${userProvider.token}',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> courses = jsonDecode(response.body);
        final bool hasCourse = courses.any(
          (course) =>
              course['course_id'] == widget.courseId ||
              (course['course_id'] is String &&
                  int.parse(course['course_id']) == widget.courseId),
        );

        if (!hasCourse) {
          print("用户已退出当前课程，返回上一页");
          // 如果用户不再拥有此课程，返回上一页
          if (mounted) {
            Navigator.of(context).pop();
            // 显示提示信息
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text("该课程已被移出题库")));
          }
        }
      }
    } catch (e) {
      print("检查用户课程时出错: $e");
    }
  }

  void _initializePageData() {
    // 设置当前显示的课程名称
    _currentDisplayedCourseName = widget.courseName;

    // 加载问题数据
    _loadQuestionsSilently();
  }

  @override
  void dispose() {
    // 在页面关闭前保存当前答题状态
    _saveQuizState();

    // 安全地移除监听器
    try {
      _scrollController.removeListener(_onScrollChanged);
      _scrollController.removeListener(_scrollListener);
    } catch (e) {
      // 如果监听器不存在，忽略错误
      debugPrint('移除滚动监听器警告: $e');
    }
    _scrollController.dispose();
    _pageController.dispose();
    _autoNextTimer?.cancel();
    _questionTimer?.cancel();
    _feedbackAnimationController.dispose();
    _dragController.dispose();
    _swipeController.dispose();
    // 其他控制器的dispose
    _optionsScrollController.dispose();
    _questionController.dispose();
    _pageEnterAnimationController.dispose();
    super.dispose();
  }

  // 滚动监听器方法 - 监听任何滚动变化
  void _scrollListener() {
    // 无论任何滚动情况都更新导航栏标题可见性
    if (mounted) {
      _updateNavTitleVisibility();
    }
  }

  // 在任何可能导致布局变化的方法后，都应该调用此方法更新标题状态
  void _ensureNavTitleVisibility() {
    // 短延迟确保滚动/动画已完成
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _updateNavTitleVisibility();
      }
    });
  }

  // 更新导航栏标题的可见性
  void _updateNavTitleVisibility() {
    // 确保组件已挂载且标题的GlobalKey有效，并且widget树已完全构建
    if (!mounted ||
        _titleKey.currentContext == null ||
        ModalRoute.of(context) == null)
      return;

    try {
      // 获取原始标题的位置和大小
      final RenderBox titleBox =
          _titleKey.currentContext!.findRenderObject() as RenderBox;
      final titlePosition = titleBox.localToGlobal(Offset.zero);

      // 安全地获取状态栏高度
      final MediaQueryData? mediaQuery = MediaQuery.maybeOf(context);
      if (mediaQuery == null) return;
      final double statusBarHeight = mediaQuery.padding.top;

      // 计算AppBar底部位置（包含状态栏高度）
      final double appBarBottom = kToolbarHeight + statusBarHeight;

      // 使用 addPostFrameCallback 确保 setState 不会在构建过程中调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;

        // 简化判断逻辑：只考虑两种基本情况
        if (_scrollController.offset <= 10.0) {
          // 当接近顶部时，强制隐藏导航栏标题和背景
          if (_navTitleOpacity != 0.0) {
            setState(() {
              _navTitleOpacity = 0.0;
            });
          }
        } else {
          // 其他所有情况，根据滚动距离或标题位置计算透明度
          double ratio;

          // 如果标题已经接近或进入AppBar区域
          if (titlePosition.dy <= appBarBottom + 10) {
            // 当标题已经接近或进入AppBar区域，透明度应该接近1.0
            ratio = 1.0 - (titlePosition.dy / appBarBottom).clamp(0.0, 1.0);
          } else {
            // 否则，根据滚动距离计算透明度
            ratio = (_scrollController.offset - 10.0) / 100.0;
          }

          // 应用平滑过渡函数并确保比例在合理范围内
          ratio = ratio.clamp(0.0, 1.0);
          ratio = _smoothstep(ratio);

          if (_navTitleOpacity != ratio) {
            setState(() {
              _navTitleOpacity = ratio;
            });
          }
        }
      });
    } catch (e) {
      debugPrint("获取标题位置出错: $e");
    }
  }

  // 平滑步进函数，使过渡更加自然
  double _smoothstep(double x) {
    // 使用平滑的S曲线: 3x²-2x³
    return x * x * (3 - 2 * x);
  }

  // 执行页面弹回的动画
  void _animateBackToOriginalPosition() {
    final SpringDescription spring = SpringDescription.withDampingRatio(
      mass: 1.0,
      stiffness: 600.0, // 增加弹簧刚度，使弹回更迅速
      ratio: 1.0, // 增加阻尼比例，减少弹回的振动
    );

    final SpringSimulation simulation = SpringSimulation(
      spring,
      _dragDistance,
      0.0,
      0.0,
    );

    _dragController.animateWith(simulation);
  }

  // 执行页面返回的动画
  void _animateToExit() {
    final SpringDescription spring = SpringDescription.withDampingRatio(
      mass: 1.0,
      stiffness: 350.0, // 增加弹簧刚度，使返回更果断
      ratio: 0.8, // 调整阻尼比例，使返回动画更平滑
    );

    final SpringSimulation simulation = SpringSimulation(
      spring,
      _dragDistance,
      MediaQuery.of(context).size.height * 0.3,
      0.0,
    );

    _dragController.animateWith(simulation);
  }

  // 直接返回到学习页面
  void _navigateBackToLearningPage() {
    // 添加触觉反馈增强用户体验
    HapticFeedback.mediumImpact();

    // 使用 popUntil 确保返回到正确的页面
    // 移除所有传统答题页面的实例
    Navigator.of(context).popUntil(
      (route) =>
          route.settings.name != null &&
          !route.settings.name!.contains('TraditionalQuizPage'),
    );
  }

  // 处理垂直拖动事件
  void _handleDragUpdate(DragUpdateDetails details) {
    if (details.primaryDelta != null && details.primaryDelta! > 0) {
      // 使用 addPostFrameCallback 确保 setState 不会在构建过程中调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        setState(() {
          _dragDistance += details.primaryDelta!;
        });
      });
    }
  }

  // 处理垂直拖动结束事件
  void _handleDragEnd(DragEndDetails details) {
    final double dragVelocity = details.velocity.pixelsPerSecond.dy;

    if (_dragDistance > 60 || dragVelocity > 500) {
      // 降低返回触发阈值，使返回更容易触发
      _animateToExit();
    } else {
      // 回弹
      _animateBackToOriginalPosition();

      // 使用 addPostFrameCallback 确保 setState 不会在构建过程中调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        setState(() {
          _dragDistance = 0;
        });
      });
    }
  }

  // 处理水平滑动事件
  void _handleHorizontalDragUpdate(DragUpdateDetails details) {
    // 确保primaryDelta不为空
    if (details.primaryDelta == null) return;

    // 使用 addPostFrameCallback 确保 setState 不会在构建过程中调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // 从左向右滑动(正值)优先处理为返回手势
      if (details.primaryDelta! > 0) {
        setState(() {
          // 左右滑动优先用于返回
          _swipeDistance += details.primaryDelta!;
          // 限制最大滑动距离，防止过度拉伸
          _swipeDistance = math.min(
            _swipeDistance,
            MediaQuery.of(context).size.width * 0.25,
          );
        });
        return;
      }

      // 向左滑动(负值)，只有答题完成后才能切题
      if (_interactionState != QuestionInteractionState.waitingForSelection &&
          details.primaryDelta! < 0) {
        setState(() {
          // 累加负值滑动距离
          _swipeDistance += details.primaryDelta!;

          // 限制最大滑动距离，最小值为负数最大屏幕宽度的四分之一
          _swipeDistance = _swipeDistance.clamp(
            -MediaQuery.of(context).size.width * 0.25,
            MediaQuery.of(context).size.width * 0.25,
          );
        });
      }
    });
  }

  // 处理水平滑动结束事件
  void _handleHorizontalDragEnd(DragEndDetails details) {
    final double screenWidth = MediaQuery.of(context).size.width;

    // 右滑返回 (正值)
    if (_swipeDistance > 0) {
      // 降低返回触发阈值，从20%降低到10%
      final bool shouldPop =
          _swipeDistance > screenWidth * 0.1 ||
          (details.primaryVelocity != null &&
              details.primaryVelocity! > 300); // 降低速度要求，从500降低到300

      if (shouldPop) {
        // 添加触觉反馈
        HapticFeedback.mediumImpact();
        // 直接返回到学习页面，而不是简单地pop
        _navigateBackToLearningPage();
      } else {
        // 如果不够触发返回，则恢复到原位
        _swipeController.animateBack(
          0.0,
          duration: const Duration(milliseconds: 250),
          curve: Curves.fastOutSlowIn,
        );
      }
    }
    // 左滑切题 (负值)
    else if (_swipeDistance < 0) {
      // 只有在已回答状态下才处理左滑切题
      if (_interactionState != QuestionInteractionState.waitingForSelection) {
        final bool shouldGoNext =
            _swipeDistance < -screenWidth * 0.1 ||
            (details.primaryVelocity != null &&
                details.primaryVelocity! < -300);

        if (shouldGoNext) {
          // 添加触觉反馈
          HapticFeedback.selectionClick();
          // 执行切换到下一题的动画
          _animateSwipeToNextQuestion();
        } else {
          // 取消滑动动画
          _cancelSwipeAnimation();
        }
      } else {
        // 在等待回答状态下重置滑动距离
        setState(() {
          _swipeDistance = 0;
        });
      }
    } else {
      // 滑动距离为0，重置状态
      _swipeController.animateBack(
        0.0,
        duration: const Duration(milliseconds: 150),
        curve: Curves.fastOutSlowIn,
      );
    }
  }

  // 执行滑动切题动画
  void _animateSwipeToNextQuestion() {
    // 先检查是否可以前往下一题
    if (_currentQuestionIndex >= _questionList.length - 1) {
      // 已经是最后一题，显示完成对话框
      _showCompletionDialog();
      // 重置滑动距离
      setState(() {
        _swipeDistance = 0;
      });
      return;
    }

    // 使用弹簧动画，增强流畅感
    final SpringDescription spring = SpringDescription.withDampingRatio(
      mass: 1.0,
      stiffness: 500.0,
      ratio: 0.8,
    );

    // 计算当前滑动距离相对于最大滑动距离的比例，作为动画起始值
    // 由于_swipeDistance是负值，使用绝对值计算比例
    final double startValue =
        _swipeDistance.abs() / (MediaQuery.of(context).size.width * 0.25);

    // 创建弹簧模拟器，动画目标值为1.0（表示完成）
    final SpringSimulation simulation = SpringSimulation(
      spring,
      startValue,
      1.0, // 目标值为1.0，表示完成动画
      0.0,
    );

    // 开始动画，动画完成后会通过状态监听器触发切换题目
    _swipeController.animateWith(simulation);
  }

  // 取消滑动动画
  void _cancelSwipeAnimation() {
    // 使用弹簧动画，增强流畅感
    final SpringDescription spring = SpringDescription.withDampingRatio(
      mass: 1.0,
      stiffness: 500.0,
      ratio: 0.8,
    );

    // 计算当前滑动距离相对于最大滑动距离的比例，作为动画起始值
    // 由于_swipeDistance是负值，使用绝对值计算比例
    final double startValue =
        _swipeDistance.abs() / (MediaQuery.of(context).size.width * 0.25);

    // 创建弹簧模拟器，动画目标值为0.0（表示取消）
    final SpringSimulation simulation = SpringSimulation(
      spring,
      startValue,
      0.0, // 目标值为0.0，表示取消动画
      0.0,
    );

    // 开始动画，重置滑动状态
    _swipeController.animateWith(simulation);
  }

  // 开始计时器
  void _startQuestionTimer() {
    // 取消现有计时器
    _questionTimer?.cancel();

    // 重置计时
    _questionTimeInSeconds = 0;

    // 创建新计时器，每秒更新一次
    _questionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _questionTimeInSeconds++;
      });
    });
  }

  // 停止计时器
  void _stopQuestionTimer() {
    _questionTimer?.cancel();
  }

  // 静默加载题目，不显示加载指示器
  Future<void> _loadQuestionsSilently() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn) {
        if (mounted) {
          setState(() {
            _errorMessage = "请先登录";
            _isLoading = false;
            _isInitialLoading = false;
          });
          _showErrorDialog("请先登录");
        }
        return;
      }

      // 确保页面进入动画已启动（如果尚未启动）
      if (!_pageEnterAnimationController.isAnimating &&
          _pageEnterAnimationController.status != AnimationStatus.completed) {
        _pageEnterAnimationController.forward();
      }

      // 先获取所有题目
      final allQuestions = await QuizService.getQuestions(
        widget.courseId,
        userProvider.token!,
      );

      // 检查是否需要过滤题目
      List<Question> filteredQuestions = allQuestions;
      if (widget.questionIds != null && widget.questionIds!.isNotEmpty) {
        print('使用指定的题目ID列表加载题目: ${widget.questionIds}');
        // 根据questionIds过滤题目
        filteredQuestions =
            allQuestions
                .where((q) => widget.questionIds!.contains(q.id))
                .toList();

        // 确保题目顺序与传入的ID列表顺序一致
        filteredQuestions.sort(
          (a, b) =>
              widget.questionIds!.indexOf(a.id) -
              widget.questionIds!.indexOf(b.id),
        );

        print('过滤后的题目数量: ${filteredQuestions.length}');
      }

      if (mounted) {
        setState(() {
          _questionList = filteredQuestions;
          _originalQuestionList = List.from(allQuestions); // 仍然保留原始题目列表备份
          _isLoading = false;
          _isInitialLoading = false;
        });

        if (_questionList.isNotEmpty) {
          // 在题目加载完成后，尝试恢复上次的答题状态
          await _loadQuizState();

          // 如果是等待选择状态，开始计时
          if (_interactionState ==
              QuestionInteractionState.waitingForSelection) {
            _startQuestionTimer();
          }
        } else {
          setState(() {
            _errorMessage = "未能加载到题目，请稍后再试。";
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = "加载题目失败: $e";
          _isLoading = false;
          _isInitialLoading = false;
        });
        _showErrorDialog("加载题目失败: $e");
      }
    }
  }

  // 加载题目列表
  Future<void> _loadQuestions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    await _loadQuestionsSilently();
  }

  void _showErrorDialog(String message) {
    if (!mounted) return;

    // 使用WidgetsBinding.instance.addPostFrameCallback确保在组件完全初始化后显示对话框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      showCupertinoDialog(
        context: context,
        builder:
            (context) => CupertinoAlertDialog(
              title: const Text('提示'),
              content: Text(message),
              actions: [
                CupertinoDialogAction(
                  child: const Text('确定'),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (message == "请先登录") {
                      Navigator.of(context).pop();
                    }
                  },
                ),
              ],
            ),
      );
    });
  }

  // 构建问题区域
  Widget _buildQuestionArea() {
    if (_currentQuestion == null) {
      return const Center(
        child: Text(
          "无题目信息",
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      );
    }

    // 准备完整显示文本，对于案例题，将题干和案例内容一起显示
    String displayText = _currentQuestion!.content;
    if (_currentQuestion!.type == QuestionType.caseAnalysis &&
        _currentQuestion!.caseContent != null &&
        _currentQuestion!.caseContent!.isNotEmpty) {
      displayText =
          "${_currentQuestion!.caseContent}\n\n${_currentQuestion!.content}";
    }

    return Hero(
      tag: 'quiz_card_trad_${widget.courseId}',
      flightShuttleBuilder: (
        BuildContext flightContext,
        Animation<double> animation,
        HeroFlightDirection flightDirection,
        BuildContext fromHeroContext,
        BuildContext toHeroContext,
      ) {
        return Material(
          color: Colors.transparent,
          child: ScaleTransition(
            scale: animation.drive(
              Tween<double>(
                begin: 1.0,
                end: 1.0,
              ).chain(CurveTween(curve: Curves.easeInOut)),
            ),
            child: FadeTransition(
              opacity: animation,
              child: GradientBorderGlassCard(
                text: displayText,
                height: 180.0,
                borderRadius: 24.0,
                borderWidth: 2.5,
              ),
            ),
          ),
        );
      },
      child: Stack(
        key: _questionAreaKey,
        children: [
          GradientBorderGlassCard(
            text: displayText,
            height:
                _currentQuestion!.type == QuestionType.caseAnalysis
                    ? 240.0
                    : 180.0, // 增加案例题的卡片高度
            borderRadius: 24.0,
            borderWidth: 2.5,
          ),

          // 添加能量按钮到右上角
          Positioned(
            right: 12,
            top: 12,
            child: EnergyButton(
              key: _energyButtonKey,
              onPressed: () => _showEnergyModal(),
              onEnergyChange: _handleEnergyChange,
            ),
          ),

          // 左下角显示做题次数
          Positioned(
            left: 16,
            bottom: 12,
            child: Text(
              '第$_attemptCount次',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black45,
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
            ),
          ),

          // 右下角显示计时器而非进度
          Positioned(
            right: 16,
            bottom: 12,
            child: Text(
              '${_questionTimeInSeconds.toString().padLeft(2, '0')}s',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black45,
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 添加临时存储用户答案的变量
  dynamic _tempSelectedAnswer;

  // 修改onAnswerSelected方法，只存储用户选择而不立即判断
  void _onAnswerSelected(dynamic answer) {
    if (_interactionState != QuestionInteractionState.waitingForSelection) {
      return; // 如果已经选择过答案，则不再处理
    }

    // 使用 addPostFrameCallback 确保 setState 不会在构建过程中调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _tempSelectedAnswer = answer;
      });

      // 打印用于调试
      print(
        '用户选择了答案: $_tempSelectedAnswer (类型: ${_tempSelectedAnswer?.runtimeType})',
      );
    });
  }

  // 添加提交答案的方法
  void _submitAnswer() {
    // 如果未选择答案或已经提交，则不处理
    if (_tempSelectedAnswer == null ||
        _interactionState != QuestionInteractionState.waitingForSelection) {
      return;
    }

    // 根据题型处理不同类型的答案
    bool isCorrect = false;

    switch (_currentQuestion!.type) {
      case QuestionType.singleChoice:
      case QuestionType.trueFalse:
      case QuestionType.caseAnalysis:
        // 处理单选题答案
        try {
          if (_tempSelectedAnswer is int) {
            _selectedOptionId = _tempSelectedAnswer as int;
            final selectedOption = _currentQuestion!.options.firstWhere(
              (option) => option.id == _selectedOptionId,
              orElse: () => Option(id: -1, content: "", isCorrect: false),
            );
            isCorrect = selectedOption.isCorrect;
          } else {
            print('单选题答案类型不正确: ${_tempSelectedAnswer.runtimeType}');
            isCorrect = false;
          }
        } catch (e) {
          print('单选题答案处理错误: $e');
          isCorrect = false;
        }
        break;

      case QuestionType.multipleChoice:
        // 处理多选题答案
        try {
          List<int> selectedIds;
          if (_tempSelectedAnswer is List<int>) {
            selectedIds = _tempSelectedAnswer as List<int>;
          } else if (_tempSelectedAnswer is List) {
            // 尝试转换为int列表
            selectedIds = [];
            for (var id in _tempSelectedAnswer as List) {
              if (id is int) {
                selectedIds.add(id);
              }
            }
          } else if (_tempSelectedAnswer is int) {
            // 单个整数也可能是多选题的一个选择
            selectedIds = [_tempSelectedAnswer as int];
          } else {
            print('多选题答案类型不正确: ${_tempSelectedAnswer.runtimeType}');
            selectedIds = [];
          }

          // 判断是否全部选中了正确选项，且没有选择错误选项
          List<int> correctOptionIds =
              _currentQuestion!.options
                  .where((option) => option.isCorrect)
                  .map((option) => option.id)
                  .toList();

          isCorrect =
              selectedIds.length == correctOptionIds.length &&
              selectedIds.every((id) => correctOptionIds.contains(id));
        } catch (e) {
          print('多选题答案处理错误: $e');
          isCorrect = false;
        }
        break;

      case QuestionType.fillBlank:
      case QuestionType.calculation:
        // 处理填空题和计算题，进行精确匹配
        try {
          String userAnswer;
          if (_tempSelectedAnswer is String) {
            userAnswer = _tempSelectedAnswer as String;
          } else {
            // 将非字符串类型转换为字符串
            userAnswer = _tempSelectedAnswer.toString();
            print('填空/计算题答案类型转换: ${_tempSelectedAnswer.runtimeType} -> String');
          }

          String correctAnswer = _currentQuestion!.answer ?? '';
          isCorrect = userAnswer.trim() == correctAnswer.trim();
        } catch (e) {
          print('填空/计算题答案处理错误: $e');
          isCorrect = false;
        }
        break;

      case QuestionType.shortAnswer:
        // 简答题可以进行关键词匹配或者提示用户需要人工评分
        try {
          String userAnswer;
          if (_tempSelectedAnswer is String) {
            userAnswer = _tempSelectedAnswer as String;
          } else {
            // 将非字符串类型转换为字符串
            userAnswer = _tempSelectedAnswer.toString();
            print('简答题答案类型转换: ${_tempSelectedAnswer.runtimeType} -> String');
          }

          // 这里简单实现，实际可能需要教师评分或更复杂的算法
          isCorrect = userAnswer.isNotEmpty; // 只要不为空就算"回答"
        } catch (e) {
          print('简答题答案处理错误: $e');
          isCorrect = false;
        }
        break;

      case QuestionType.matching:
        // 匹配题答案
        try {
          Map<String, String> userMatches;
          if (_tempSelectedAnswer is Map<String, String>) {
            userMatches = _tempSelectedAnswer as Map<String, String>;
          } else if (_tempSelectedAnswer is Map) {
            // 尝试转换Map
            userMatches = {};
            (_tempSelectedAnswer as Map).forEach((key, value) {
              if (key is String && value is String) {
                userMatches[key] = value;
              }
            });
          } else {
            print('匹配题答案类型不正确: ${_tempSelectedAnswer.runtimeType}');
            userMatches = {};
          }

          List<MatchingItem>? correctMatches = _currentQuestion!.matchingItems;
          if (correctMatches != null &&
              correctMatches.isNotEmpty &&
              userMatches.isNotEmpty) {
            // 检查是否所有匹配都正确
            bool allCorrect = correctMatches.every(
              (item) => userMatches[item.left] == item.right,
            );
            // 检查是否匹配了所有项目
            bool allMatched = userMatches.length == correctMatches.length;

            isCorrect = allCorrect && allMatched;
          } else {
            isCorrect = false;
          }
        } catch (e) {
          print('匹配题答案类型转换错误: $e');
          isCorrect = false;
        }
        break;

      case QuestionType.ordering:
        // 排序题答案
        try {
          // 安全地处理类型转换，支持多种可能的数据类型
          List<OrderingItem> userOrder;
          if (_tempSelectedAnswer is List<OrderingItem>) {
            userOrder = _tempSelectedAnswer as List<OrderingItem>;
          } else if (_tempSelectedAnswer is List) {
            // 尝试从List类型转换
            userOrder = [];
            for (var item in _tempSelectedAnswer as List) {
              if (item is OrderingItem) {
                userOrder.add(item);
              }
            }
          } else {
            // 如果不是期望的类型，则使用默认排序
            print('排序题答案数据类型不正确: ${_tempSelectedAnswer.runtimeType}');
            userOrder = _currentQuestion!.orderingItems ?? [];
            isCorrect = false;
            break;
          }

          // 检查每个项目是否在正确位置
          if (userOrder.length == _currentQuestion!.orderingItems?.length) {
            bool allInCorrectPosition = true;
            for (int i = 0; i < userOrder.length; i++) {
              if (userOrder[i].correctOrder != i + 1) {
                allInCorrectPosition = false;
                break;
              }
            }
            isCorrect = allInCorrectPosition;
          }
        } catch (e) {
          print('排序题答案类型转换错误: $e');
          isCorrect = false;
        }
        break;

      case QuestionType.programming:
        // 编程题可以进行简单的字符串比较或者实际运行代码
        try {
          String userCode;
          if (_tempSelectedAnswer is String) {
            userCode = _tempSelectedAnswer as String;
          } else {
            // 将非字符串类型转换为字符串
            userCode = _tempSelectedAnswer.toString();
            print('编程题答案类型转换: ${_tempSelectedAnswer.runtimeType} -> String');
          }

          isCorrect = userCode.contains(_currentQuestion!.answer ?? '');
        } catch (e) {
          print('编程题答案处理错误: $e');
          isCorrect = false;
        }
        break;
    }

    setState(() {
      _interactionState =
          isCorrect
              ? QuestionInteractionState.answerCorrect
              : QuestionInteractionState.answerIncorrect;
    });

    // 记录答题结果
    _recordAnswer(isCorrect);
  }

  // 构建答题区域，增加提交按钮
  Widget _buildOptionsArea() {
    if (_currentQuestion == null) return const SizedBox.shrink();

    // 使用答题区域工厂根据题型创建不同的答题UI
    dynamic selectedAnswer;

    // 根据题型确定选中的答案数据类型
    switch (_currentQuestion!.type) {
      case QuestionType.singleChoice:
      case QuestionType.trueFalse:
      case QuestionType.caseAnalysis:
        // 单选和判断题使用整数ID
        selectedAnswer = _tempSelectedAnswer ?? _selectedOptionId;
        break;
      case QuestionType.multipleChoice:
        // 多选题使用ID列表
        selectedAnswer =
            _tempSelectedAnswer ??
            (_selectedOptionId != null ? [_selectedOptionId] : []);
        break;
      case QuestionType.fillBlank:
      case QuestionType.shortAnswer:
      case QuestionType.programming:
      case QuestionType.calculation:
        // 填空和简答题使用字符串
        selectedAnswer = _tempSelectedAnswer;
        break;
      case QuestionType.matching:
        // 匹配题使用Map
        selectedAnswer = _tempSelectedAnswer ?? <String, String>{};
        break;
      case QuestionType.ordering:
        // 排序题使用排序项列表
        selectedAnswer = _tempSelectedAnswer ?? _currentQuestion!.orderingItems;
        break;
    }

    return Column(
      children: [
        // 答题区域
        AnswerAreaFactory.createAnswerAreaWidget(
          question: _currentQuestion!,
          onAnswerSelected: _onAnswerSelected,
          selectedAnswer: selectedAnswer,
          interactionState: _interactionState,
        ),

        // 提交按钮，仅在等待选择状态且已选择答案时显示
        if (_interactionState == QuestionInteractionState.waitingForSelection &&
            _tempSelectedAnswer != null &&
            _hasValidAnswer())
          Padding(
            padding: const EdgeInsets.only(top: 20.0),
            child: GestureDetector(
              onTap: _submitAnswer,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 15.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [const Color(0xFF8A00FF), const Color(0xFFCB3EFF)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.purple.withOpacity(0.4),
                      blurRadius: 10.0,
                      spreadRadius: 2.0,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                alignment: Alignment.center,
                child: const Text(
                  "提交答案",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Noto Sans SC',
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  // 检查是否有有效答案可提交
  bool _hasValidAnswer() {
    if (_tempSelectedAnswer == null) return false;

    switch (_currentQuestion!.type) {
      case QuestionType.singleChoice:
      case QuestionType.trueFalse:
      case QuestionType.caseAnalysis:
        // 单选题需要有选项ID
        return _tempSelectedAnswer is int;

      case QuestionType.multipleChoice:
        // 多选题需要至少选择一项
        if (_tempSelectedAnswer is List) {
          return (_tempSelectedAnswer as List).isNotEmpty;
        }
        return false;

      case QuestionType.fillBlank:
      case QuestionType.calculation:
      case QuestionType.shortAnswer:
      case QuestionType.programming:
        // 填空题、计算题、简答题、编程题需要有非空文本
        if (_tempSelectedAnswer is String) {
          return (_tempSelectedAnswer as String).trim().isNotEmpty;
        }
        return false;

      case QuestionType.matching:
        // 匹配题需要至少有一项匹配
        if (_tempSelectedAnswer is Map) {
          return (_tempSelectedAnswer as Map).isNotEmpty;
        }
        return false;

      case QuestionType.ordering:
        // 排序题需要有排序项列表
        if (_tempSelectedAnswer is List) {
          return (_tempSelectedAnswer as List).isNotEmpty;
        }
        return false;

      default:
        return false;
    }
  }

  Widget _buildAnswerFeedback() {
    if (_interactionState == QuestionInteractionState.waitingForSelection) {
      return const SizedBox.shrink();
    }

    final bool isCorrect =
        _interactionState == QuestionInteractionState.answerCorrect;

    return Container(
      key: _feedbackAreaKey, // 添加 GlobalKey
      margin: const EdgeInsets.symmetric(vertical: 12), // 减小垂直间距
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        color:
            isCorrect
                ? Colors.green.withOpacity(0.2)
                : Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCorrect ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isCorrect
                ? CupertinoIcons.check_mark_circled_solid
                : CupertinoIcons.xmark_circle_fill,
            color: isCorrect ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            isCorrect ? "回答正确！向左滑切题" : "回答错误，请查看解析，向左滑切题",
            style: TextStyle(
              color: isCorrect ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExplanationArea() {
    if (_currentQuestion == null ||
        _interactionState == QuestionInteractionState.waitingForSelection) {
      return const SizedBox.shrink();
    }

    // 获取正确答案的安全方法
    String getCorrectAnswer() {
      if (_currentQuestion!.type == QuestionType.fillBlank ||
          _currentQuestion!.type == QuestionType.shortAnswer ||
          _currentQuestion!.type == QuestionType.programming ||
          _currentQuestion!.type == QuestionType.calculation) {
        // 对于填空题、简答题、编程题、计算题，使用answer字段
        return _currentQuestion!.answer ?? '暂无正确答案';
      } else {
        // 对于选择题类型，查找isCorrect为true的选项
        try {
          final correctOption = _currentQuestion!.options.firstWhere(
            (o) => o.isCorrect,
            orElse: () => Option(id: -1, content: '暂无正确答案', isCorrect: false),
          );
          return correctOption.content;
        } catch (e) {
          return '暂无正确答案';
        }
      }
    }

    return CollapsibleSection(
      title: "解析",
      isExpanded: true,
      onToggle: () {},
      child: ExplanationCard(
        correctAnswer: getCorrectAnswer(),
        explanation: _currentQuestion!.explanation,
      ),
    );
  }

  Widget _buildMemoryTipsArea() {
    if (_interactionState == QuestionInteractionState.waitingForSelection) {
      return const SizedBox.shrink();
    }

    // 创建自定义标题组件，包含灯泡图标
    final titleWidget = Row(
      children: [
        const Icon(CupertinoIcons.lightbulb, color: Colors.amber, size: 22),
        const SizedBox(width: 10),
        const Text(
          "记忆技巧",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.amber,
            fontFamily: 'Noto Sans SC',
          ),
        ),
      ],
    );

    return CollapsibleSection(
      titleWidget: titleWidget,
      isExpanded: true,
      onToggle: () {},
      child: MemoryTipsCard(tips: _memoryTips),
    );
  }

  Widget _buildCommentsArea() {
    // 已禁用学习评论功能
    return const SizedBox.shrink();
  }

  void _recordAnswer(bool isCorrect) {
    // 保存当前状态
    _saveQuizState();

    // 启动反馈动画
    _feedbackAnimationController.forward(from: 0.0);

    // 如果答题错误，添加到错题库
    if (!isCorrect) {
      _addToWrongQuestions();
    }

    // 添加到学习历史
    _addToStudyHistory(isCorrect);

    // 提交答案到服务器
    _saveAnswerToServer(isCorrect);

    // 计算需要滚动到的位置：题干区域加上选项区域的高度，定位到答案反馈区域
    _scrollToFeedback();

    // 显示滑动提示
    _showSwipeHint();
  }

  // 添加到错题库的方法
  Future<void> _addToWrongQuestions() async {
    if (_currentQuestion == null) return;

    print('开始添加错题: 题目ID=${_currentQuestion!.id}, 课程ID=${widget.courseId}');

    try {
      final prefs = await SharedPreferences.getInstance();
      final wrongQuestionsJson = prefs.getString('wrong_questions') ?? '[]';
      print('获取到原错题列表: $wrongQuestionsJson');

      final List wrongQuestions = jsonDecode(wrongQuestionsJson) as List;
      print('原错题列表包含${wrongQuestions.length}个项目');

      // 检查题目是否已经在错题库中
      bool isAlreadyInWrongQuestions = wrongQuestions.any(
        (item) =>
            item is Map &&
            item['id'] == _currentQuestion!.id &&
            item['courseId'] == widget.courseId,
      );

      if (isAlreadyInWrongQuestions) {
        // 如果已经存在，增加错误次数
        for (int i = 0; i < wrongQuestions.length; i++) {
          if (wrongQuestions[i] is Map &&
              wrongQuestions[i]['id'] == _currentQuestion!.id &&
              wrongQuestions[i]['courseId'] == widget.courseId) {
            int wrongCount = wrongQuestions[i]['wrongCount'] ?? 0;
            wrongQuestions[i]['wrongCount'] = wrongCount + 1;
            wrongQuestions[i]['date'] = DateTime.now().toString().substring(
              0,
              10,
            );

            print('更新已有错题: ID=${_currentQuestion!.id}, 错误次数=${wrongCount + 1}');
            break;
          }
        }
      } else {
        // 创建新的错题项
        final newWrongQuestion = {
          'id': _currentQuestion!.id,
          'content': _currentQuestion!.content,
          'courseId': widget.courseId,
          'courseName': widget.courseName ?? '未知课程',
          'date': DateTime.now().toString().substring(0, 10),
          'wrongCount': 1,
        };

        print('创建新错题项: $newWrongQuestion');

        // 添加到错题列表
        wrongQuestions.add(newWrongQuestion);
      }

      print('更新后错题列表包含${wrongQuestions.length}个项目');

      // 保存更新后的错题列表
      final newWrongQuestionsJson = jsonEncode(wrongQuestions);
      print('新错题列表JSON: $newWrongQuestionsJson');
      await prefs.setString('wrong_questions', newWrongQuestionsJson);
      print('已保存新错题列表到SharedPreferences');
    } catch (e) {
      print('添加错题失败: $e');
      if (mounted) {
        _showNotification('添加到错题库失败，请重试');
      }
    }
  }

  // 添加到学习历史的方法
  Future<void> _addToStudyHistory(bool isCorrect) async {
    if (_currentQuestion == null) return;

    print('开始添加学习历史: 题目ID=${_currentQuestion!.id}, 课程ID=${widget.courseId}');

    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('study_history') ?? '[]';
      print('获取到原学习历史: $historyJson');

      final List historyList = jsonDecode(historyJson) as List;
      print('原学习历史包含${historyList.length}个项目');

      // 创建新的历史记录项，确保courseId是整数
      final newHistoryItem = {
        'id': _currentQuestion!.id,
        'content': _currentQuestion!.content,
        'courseId': widget.courseId, // 直接使用整数类型的courseId
        'courseName': widget.courseName ?? '未知课程',
        'date': DateTime.now().toString().substring(0, 10),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'isCorrect': isCorrect,
      };

      print('创建新历史项: $newHistoryItem');

      // 添加到历史列表开头，保持最新的记录在前面
      historyList.insert(0, newHistoryItem);

      // 只保留7天内的历史记录
      final sevenDaysAgo =
          DateTime.now().subtract(Duration(days: 7)).millisecondsSinceEpoch;
      final filteredHistoryList =
          historyList.where((item) {
            // 检查记录是否有timestamp字段且在7天内
            return item is Map &&
                item.containsKey('timestamp') &&
                (item['timestamp'] as int) >= sevenDaysAgo;
          }).toList();

      print('过滤后的历史列表包含${filteredHistoryList.length}个项目');

      // 保存更新后的历史列表
      final newHistoryJson = jsonEncode(filteredHistoryList);
      print('新历史列表JSON: $newHistoryJson');
      await prefs.setString('study_history', newHistoryJson);
      print('已保存新历史列表到SharedPreferences');
    } catch (e) {
      print('添加学习历史失败: $e');
      if (mounted) {
        _showNotification('添加到学习历史失败，请重试');
      }
    }
  }

  // 显示滑动提示
  void _showSwipeHint() {
    if (!mounted) return;

    // 使用临时浮动提示，显示后自动消失
    _showCustomToast('向左滑动进入下一题');
  }

  // 自定义Toast提示
  void _showCustomToast(String message) {
    // 创建覆盖层
    final overlay = Overlay.of(context);

    // 创建覆盖层条目
    OverlayEntry overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).size.height * 0.15,
            width: MediaQuery.of(context).size.width,
            child: Center(
              child: Material(
                color: Colors.transparent, // Material 本身透明
                child: ClipRRect(
                  // 用于实现圆角毛玻璃
                  borderRadius: BorderRadius.circular(10), // 统一圆角
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 15,
                      sigmaY: 15,
                    ), // 匹配AppleStyleMenu的模糊参数
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 18,
                      ), // 调整水平和垂直内边距
                      decoration: BoxDecoration(
                        color: const Color(
                          0xFF1C1C1E,
                        ).withOpacity(0.92), // 匹配AppleStyleMenu的颜色和透明度
                        borderRadius: BorderRadius.circular(10), // 统一圆角
                      ),
                      child: Row(
                        // 使用 Row 排列图标和文本
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min, // Row 的大小适应内容
                        children: [
                          const Icon(
                            // 使用实心填充图标
                            CupertinoIcons
                                .check_mark_circled_solid, // 更符合图片中的实心风格
                            color: Colors.white,
                            size: 16, // 调整图标大小以匹配文本视觉高度
                          ),
                          const SizedBox(width: 8), // 减小图标和文本之间的间距
                          Text(
                            message,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14, // 调整字体大小
                              fontWeight: FontWeight.w900, // 使文本更粗
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
    );

    // 插入覆盖层
    overlay.insert(overlayEntry);

    // 2秒后移除
    Future.delayed(const Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  Future<void> _saveAnswerToServer(bool isCorrect) async {
    if (_currentQuestion == null) return;
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn) return;

      // 根据题型获取不同的答案内容
      String answerContent = "";

      switch (_currentQuestion!.type) {
        case QuestionType.singleChoice:
        case QuestionType.trueFalse:
        case QuestionType.caseAnalysis:
          // 单选题型答案
          if (_selectedOptionId != null) {
            try {
              final selectedOption = _currentQuestion!.options.firstWhere(
                (opt) => opt.id == _selectedOptionId,
              );
              answerContent = selectedOption.content;
            } catch (e) {
              // 如果找不到选项，使用ID作为内容
              answerContent = "选项ID: $_selectedOptionId";
            }
          } else {
            answerContent = "未选择选项";
          }
          break;

        case QuestionType.multipleChoice:
          // 多选题答案
          if (_tempSelectedAnswer is List) {
            final selectedIds = _tempSelectedAnswer as List;
            if (selectedIds.isNotEmpty) {
              final selectedOptions =
                  _currentQuestion!.options
                      .where((opt) => selectedIds.contains(opt.id))
                      .map((opt) => opt.content)
                      .toList();
              answerContent = selectedOptions.join(', ');
            } else {
              answerContent = "未选择选项";
            }
          } else {
            answerContent = "未选择选项";
          }
          break;

        case QuestionType.fillBlank:
        case QuestionType.shortAnswer:
        case QuestionType.programming:
        case QuestionType.calculation:
          // 文本类答案
          answerContent = _tempSelectedAnswer?.toString() ?? "未填写内容";
          break;

        case QuestionType.matching:
          // 匹配题答案
          if (_tempSelectedAnswer is Map<String, String>) {
            final matches = _tempSelectedAnswer as Map<String, String>;
            if (matches.isNotEmpty) {
              final matchPairs =
                  matches.entries
                      .map((entry) => "${entry.key} -> ${entry.value}")
                      .toList();
              answerContent = matchPairs.join('; ');
            } else {
              answerContent = "未完成匹配";
            }
          } else {
            answerContent = "未完成匹配";
          }
          break;

        case QuestionType.ordering:
          // 排序题答案
          if (_tempSelectedAnswer is List) {
            final orderedItems = _tempSelectedAnswer as List;
            if (orderedItems.isNotEmpty) {
              try {
                // 排序题项目可能是OrderingItem类型，需要特殊处理
                final orderedContents =
                    orderedItems.map((item) {
                      if (item is OrderingItem) {
                        return item.content ?? "项目${item.id}";
                      } else {
                        return item.toString();
                      }
                    }).toList();
                answerContent = orderedContents.join(' -> ');
              } catch (e) {
                answerContent = "排序数据无法处理: $e";
              }
            } else {
              answerContent = "未完成排序";
            }
          } else {
            answerContent = "未完成排序";
          }
          break;

        default:
          answerContent = "未支持的题型";
      }

      // 尝试使用API提交答案
      bool success = false;
      try {
        success = await QuizService.submitAnswer(
          _currentQuestion!.id,
          answerContent,
          isCorrect,
          userProvider.token!,
        );
      } catch (apiError) {
        debugPrint('API提交答案出错: $apiError');
        success = false;
      }

      // 如果API提交失败，使用本地模拟实现
      if (!success) {
        // 模拟成功响应
        success = _simulateSuccessfulSubmission(
          _currentQuestion!.id,
          answerContent,
          isCorrect,
        );
      }

      if (success) {
        debugPrint(
          '答案提交成功: Question ${_currentQuestion!.id}, Correct: $isCorrect, Answer: $answerContent',
        );
      } else {
        throw Exception('无法提交答案');
      }
    } catch (e) {
      debugPrint('提交答案出错: $e');
      if (mounted) {
        _showErrorDialog("提交答案时出错，请检查网络连接。");
      }
    }
  }

  // 模拟成功提交答案
  bool _simulateSuccessfulSubmission(
    int questionId,
    String answerContent,
    bool isCorrect,
  ) {
    // 这是一个本地模拟实现，总是返回成功
    debugPrint(
      '使用模拟实现提交答案: Question $questionId, Answer: $answerContent, Correct: $isCorrect',
    );
    return true;
  }

  void _scrollToFeedback() {
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_scrollController.hasClients &&
          _feedbackAreaKey.currentContext != null) {
        final RenderBox? feedbackBox =
            _feedbackAreaKey.currentContext!.findRenderObject() as RenderBox?;
        if (feedbackBox != null) {
          // 计算 feedbackBox 相对于 CustomScrollView 的位置
          // CustomScrollView 的 viewport 的 offset 是 _scrollController.offset
          // RenderBox 的 localToGlobal(Offset.zero) 给出的是相对于屏幕左上角的位置
          // 我们需要的是 feedbackBox 顶部在 Scrollable 内容中的位置
          // 这可以通过将 feedbackBox 的全局位置转换为 CustomScrollView 的局部坐标，然后加上当前的滚动偏移来实现
          // 更简单的方式是直接使用 Scrollable.ensureVisible

          Scrollable.ensureVisible(
            _feedbackAreaKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOut,
            alignment: 0.0, // 0.0 表示滚动到顶部，0.5 表示中间，1.0 表示底部
            alignmentPolicy: ScrollPositionAlignmentPolicy.explicit,
          ).then((_) {
            // 滚动完成后，可以考虑是否需要更新导航栏标题的可见性
            // 但当前需求是滚动到反馈区域顶部，导航栏标题逻辑可能不需要在此处特殊处理
            // 如果需要，可以调用 _ensureNavTitleVisibilityAfterScroll 或 _updateNavTitleVisibility
            // 暂时保持简单，不主动调整导航栏标题
            if (mounted) {
              // 可以选择性地更新导航栏标题，如果需要的话
              // _updateNavTitleVisibility();
            }
          });
        } else {
          // 如果找不到 feedbackBox，可以尝试之前的滚动逻辑作为备选，或记录一个错误
          // 为了简单起见，这里不执行备选滚动
          debugPrint("Could not find RenderBox for feedbackAreaKey");
        }
      }
    });
  }

  // 添加滚动到顶部的方法
  void _scrollToTop() {
    if (_scrollController.hasClients) {
      // 先设置标题不可见
      setState(() {
        _navTitleOpacity = 0.0;
      });

      _scrollController
          .animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          )
          .then((_) {
            // 滚动完成后确保更新标题状态
            if (mounted) {
              // 确保标题在滚动到顶部后隐藏
              setState(() {
                _navTitleOpacity = 0.0;
              });

              // 延迟更新标题可见性状态，确保动画完成后UI已更新
              Future.delayed(Duration(milliseconds: 100), () {
                if (mounted) {
                  _updateNavTitleVisibility();
                }
              });
            }
          });
    }
  }

  // 添加前往下一题的方法
  void _goToNextQuestion() {
    if (_currentQuestionIndex < _questionList.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _selectedOptionId = null;
        _tempSelectedAnswer = null; // 重置临时答案
        _interactionState = QuestionInteractionState.waitingForSelection;

        // 重置问题状态
        _questionTimeInSeconds = 0;
        if (_questionTimer != null) {
          _questionTimer!.cancel();
          _questionTimer = null;
        }
        _startQuestionTimer();

        // 立即设置导航栏标题透明度为0
        _navTitleOpacity = 0.0;

        // 重置滑动距离
        _swipeDistance = 0;

        // 更新课程信息（如果提供了courseInfoMap）
        _updateCourseInfoForCurrentQuestion();
      });

      // 保存当前状态
      _saveQuizState();

      // 滚动到顶部
      _scrollToTop();

      // 检查新题目的收藏状态
      _checkCurrentQuestionFavoriteStatus();
    } else {
      _showCompletionDialog();
    }
  }

  // 添加前往上一题的方法
  void _goToPreviousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
        _selectedOptionId = null;
        _tempSelectedAnswer = null; // 重置临时答案
        _interactionState = QuestionInteractionState.waitingForSelection;

        // 重置问题状态
        _questionTimeInSeconds = 0;
        if (_questionTimer != null) {
          _questionTimer!.cancel();
          _questionTimer = null;
        }
        _startQuestionTimer();

        // 立即设置导航栏标题透明度为0
        _navTitleOpacity = 0.0;

        // 更新课程信息（如果提供了courseInfoMap）
        _updateCourseInfoForCurrentQuestion();
      });

      // 保存当前状态
      _saveQuizState();

      // 滚动到顶部
      _scrollToTop();

      // 检查新题目的收藏状态
      _checkCurrentQuestionFavoriteStatus();
    }
  }

  // 添加一个新方法用于更新当前题目对应的课程信息
  void _updateCourseInfoForCurrentQuestion() {
    // 如果没有提供courseInfoMap或当前没有选中的题目，则跳过
    if (widget.courseInfoMap == null || _currentQuestion == null) return;

    // 获取当前题目的ID
    final int currentQuestionId = _currentQuestion!.id;

    // 检查是否有该题目的课程信息
    if (widget.courseInfoMap!.containsKey(currentQuestionId)) {
      // 更新课程ID和名称
      final courseInfo = widget.courseInfoMap![currentQuestionId]!;
      if (courseInfo.containsKey('courseId') &&
          courseInfo.containsKey('courseName')) {
        final int newCourseId = courseInfo['courseId'];
        final String newCourseName = courseInfo['courseName'];
        print('更新课程信息: ID=$newCourseId, 名称=$newCourseName');

        // 实际更新课程信息
        // 这里我们无法直接修改widget的属性，但可以创建一个动态属性来保存当前显示的课程名称
        _currentDisplayedCourseName = newCourseName;

        // 强制更新UI
        setState(() {});
      }
    }
  }

  void _resetQuestionState() {
    _selectedOptionId = null;
    _tempSelectedAnswer = null; // 重置临时答案
    _interactionState = QuestionInteractionState.waitingForSelection;
    _feedbackAnimationController.reset();
  }

  void _showCompletionDialog() {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: const Text('恭喜!'),
            content: const Text('你已完成所有题目。'),
            actions: [
              CupertinoDialogAction(
                child: const Text('重新开始'),
                onPressed: () {
                  Navigator.of(context).pop();
                  // 清除保存的状态
                  _clearQuizState();
                  setState(() {
                    _currentQuestionIndex = 0;
                    _selectedOptionId = null;
                    _tempSelectedAnswer = null; // 确保临时答案被重置
                    _interactionState =
                        QuestionInteractionState.waitingForSelection;
                    // 增加做题次数
                    _attemptCount++;
                    _loadQuestions();
                  });
                },
              ),
              CupertinoDialogAction(
                isDefaultAction: true,
                child: const Text('返回'),
                onPressed: () {
                  Navigator.of(context).pop();
                  // 保留状态，直接返回
                  _navigateBackToLearningPage();
                },
              ),
            ],
          ),
    );
  }

  // 切换解析区域显示状态
  void _toggleExplanation() {
    // 该功能已被移除，保留函数避免错误
  }

  // 切换记忆技巧区域显示状态
  void _toggleMemoryTips() {
    // 该功能已被移除，保留函数避免错误
  }

  // 切换评论区域显示状态
  void _toggleComments() {
    // 该功能已被移除，保留函数避免错误
  }

  // 创建背景渐变
  Widget _buildBackground() {
    // 直接使用资源加载图片
    try {
      return Image.asset(
        'assets/images/traditional_quiz_bg.png',
        fit: BoxFit.cover,
      );
    } catch (e) {
      print('❌ 背景图片加载失败: $e');
      return Image.asset('assets/images/learning_bg3.png', fit: BoxFit.cover);
    }
  }

  // 构建下拉返回指示条
  Widget _buildDragHandle() {
    return Container(
      height: 5,
      width: 36,
      margin: const EdgeInsets.only(top: 8, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(2.5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 移除WillPopScope，允许默认返回行为
    return Material(
      color: Colors.black,
      child: AnimatedBuilder(
        animation: _pageEnterAnimationController,
        builder: (context, child) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            appBar: null,
            backgroundColor: Colors.black,
            body: GestureDetector(
              onHorizontalDragUpdate: _handleHorizontalDragUpdate,
              onHorizontalDragEnd: _handleHorizontalDragEnd,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // 背景图片延伸到整个页面
                  Positioned.fill(child: _buildBackground()),

                  // 添加顶部黑色遮罩层，覆盖状态栏区域，透明度随滚动变化
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: MediaQuery.of(context).padding.top + kToolbarHeight,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      // 从完全透明到纯黑色
                      color:
                          _navTitleOpacity <= 0.01
                              ? Colors
                                  .transparent // 完全透明
                              : Color.lerp(
                                Colors.transparent, // 初始完全透明
                                Colors.black, // 最终纯黑色
                                _navTitleOpacity,
                              ),
                    ),
                  ),

                  SafeArea(
                    child: Stack(
                      children: [
                        AnimatedBuilder(
                          animation: Listenable.merge([
                            _dragController,
                            _swipeController,
                          ]),
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(
                                _swipeDistance,
                                _dragDistance * (1 - _dragController.value),
                              ),
                              child: child,
                            );
                          },
                          child: _buildCollapsibleQuizView(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // 缓入缓出动画函数，使过渡更加平滑自然
  double _parametricEaseInOut(double t) {
    // 三次贝塞尔曲线缓动函数
    double t2 = t * t;
    double t3 = t2 * t;
    return 3 * t2 - 2 * t3; // 缓入缓出曲线
  }

  // 构建可折叠标题的滚动视图
  Widget _buildCollapsibleQuizView() {
    // 移除加载指示器判断，改为内容过渡动画
    if (_isInitialLoading) {
      // 首次加载时显示空白背景，不显示骨架屏
      return SizedBox.expand();
    }

    if (_questionList.isEmpty && _errorMessage != null) {
      return _buildEmptyOrErrorState();
    }

    // 封装内容在动画容器中
    return FadeTransition(
      opacity: _pageEnterOpacityAnimation,
      child: ScaleTransition(
        scale: _pageEnterScaleAnimation,
        child: NotificationListener<ScrollNotification>(
          onNotification: (scrollNotification) {
            // 处理滚动通知，更新导航栏标题可见性
            if (scrollNotification is ScrollUpdateNotification) {
              _updateNavTitleVisibility();
            }
            return false;
          },
          child: CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ), // 添加回弹效果，增强iOS风格体验
            slivers: [
              // 顶部导航栏与标题 - 使用SliverAppBar
              SliverAppBar(
                pinned: true,
                floating: false,
                snap: false,
                stretch: true,
                backgroundColor: Colors.transparent, // 默认为透明背景
                elevation: 0,
                expandedHeight: 45.0, // 减小高度，使标题更接近导航栏
                centerTitle: true, // 确保标题居中
                leadingWidth: 125, // 增加返回按钮区域宽度，解决溢出问题
                titleSpacing: 0, // 减少标题间距
                // 返回按钮
                leading: Container(
                  height: kToolbarHeight,
                  alignment: Alignment.centerLeft,
                  child: GestureDetector(
                    onTap: _navigateBackToLearningPage,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          // 背景色随滚动状态变化，与操作按钮保持一致
                          color:
                              _navTitleOpacity <= 0.01
                                  ? Colors.black.withOpacity(0.2) // 滚动前使用较低透明度
                                  : Color.lerp(
                                    Colors.black.withOpacity(0.4), // 初始稍深的浅黑色
                                    Colors.black.withOpacity(0.6), // 滚动后的深黑色
                                    _navTitleOpacity,
                                  ),
                          shape: BoxShape.circle,
                        ),
                        child: BackButtonWidget(
                          text: "", // 移除文本内容
                          color: Colors.white, // 修改为白色
                          onPressed: _navigateBackToLearningPage,
                        ),
                      ),
                    ),
                  ),
                ),
                title: AnimatedOpacity(
                  opacity: _navTitleOpacity,
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    _currentDisplayedCourseName,
                    key: _navTitleKey,
                    style:
                        Platform.isIOS &&
                                Localizations.localeOf(context).languageCode !=
                                    'zh'
                            ? TextStyle(
                              fontSize: 18, // 与返回按钮保持一致
                              fontWeight: FontWeight.w800, // 使用相同的粗细
                              color: Colors.white,
                              letterSpacing: -0.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.7),
                                  blurRadius: 3,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            )
                            : AppFonts.createTitleStyle(
                              fontSize: 18, // 与返回按钮保持一致
                              isLatinText:
                                  Localizations.localeOf(
                                    context,
                                  ).languageCode !=
                                  'zh',
                            ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
                actions: <Widget>[_buildTopActions()],
                // 优化的flexibleSpace，背景色随着滚动变化
                flexibleSpace: LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                    // 使用颜色插值效果从透明到纯黑色
                    return Container(
                      decoration: BoxDecoration(
                        // 从完全透明到纯黑色的渐变
                        color:
                            _navTitleOpacity <= 0.01
                                ? Colors
                                    .transparent // 完全透明
                                : Color.lerp(
                                  Colors.transparent, // 初始完全透明
                                  Colors.black, // 最终纯黑色
                                  _navTitleOpacity,
                                ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // 添加一条细线作为分隔，透明度也随滚动变化
                          Container(
                            height: 0.5,
                            color: Colors.white.withOpacity(
                              0.1 * _navTitleOpacity,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),

              // 内容区域 - 使用SliverList
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(
                  24.0, // 将左边距从16px改为24px，与学习页面保持一致
                  2.0, // 减小顶部填充，使标题与导航栏更紧凑
                  16.0,
                  16.0,
                ),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // 课程标题 - 作为普通内容添加到列表开头
                    Padding(
                      key: _titleKey, // 添加key以便监听位置
                      padding: const EdgeInsets.only(bottom: 16.0), // 减少底部间距
                      child: Text(
                        _currentDisplayedCourseName,
                        style:
                            Platform.isIOS &&
                                    Localizations.localeOf(
                                          context,
                                        ).languageCode !=
                                        'zh'
                                ? TextStyle(
                                  fontSize: 32, // 减小字体尺寸
                                  fontWeight: FontWeight.w800,
                                  color: Colors.white,
                                  letterSpacing: -0.5,
                                  height: 1.2, // 添加行高控制，使多行标题看起来更协调
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.7),
                                      blurRadius: 3,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                )
                                : AppFonts.createTitleStyle(
                                  fontSize: 32, // 减小安卓标题尺寸
                                  isLatinText:
                                      Localizations.localeOf(
                                        context,
                                      ).languageCode !=
                                      'zh',
                                ),
                        maxLines: 2, // 允许标题最多两行
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    _buildQuestionArea(),
                    const SizedBox(height: 16), // 减小间距
                    _buildOptionsArea(),
                    _buildAnswerFeedback(),
                    _buildExplanationArea(),
                    _buildMemoryTipsArea(),
                    // 底部间距
                    SizedBox(
                      height:
                          _interactionState !=
                                  QuestionInteractionState.waitingForSelection
                              ? 80 // 固定按钮高度 + 边距
                              : 20,
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 空状态或错误状态显示
  Widget _buildEmptyOrErrorState() {
    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      CupertinoIcons.exclamationmark_triangle,
                      color: Colors.white,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Noto Sans SC',
                      ),
                    ),
                    const SizedBox(height: 24),
                    GlassButton(text: '重试', onPressed: _loadQuestions),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      return Center(
        child: Text(
          '没有找到相关题目。',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Noto Sans SC',
          ),
        ),
      );
    }
  }

  // 通过key查找渲染盒子
  RenderBox? _findRenderBoxForWidget(String keyName) {
    try {
      final GlobalKey key = _getKeyByName(keyName);
      final RenderObject? renderObject = key.currentContext?.findRenderObject();
      if (renderObject != null && renderObject is RenderBox) {
        return renderObject;
      }
    } catch (e) {
      debugPrint('查找渲染盒子出错: $e');
    }
    return null;
  }

  // 获取相应的key
  GlobalKey _getKeyByName(String name) {
    switch (name) {
      case "question_area":
        return _questionAreaKey;
      case "options_area":
        return _optionsAreaKey;
      case "feedback_area":
        return _feedbackAreaKey;
      default:
        return GlobalKey();
    }
  }

  // 监听所有滚动变化
  void _onScrollChanged() {
    if (!_scrollController.position.isScrollingNotifier.value) {
      // 滚动停止时更新导航栏标题
      _updateNavTitleVisibility();
    }
  }

  // 打开菜单方法 - 替换原来的侧边栏方法
  void _openDrawer() {
    // 先检查一次收藏状态，确保显示正确
    _checkCurrentQuestionFavoriteStatus().then((_) {
      print(
        '菜单打开前检查收藏状态: _isCurrentQuestionFavorited = $_isCurrentQuestionFavorited',
      );

      // 使用GlobalKey获取按钮的精确位置
      final RenderBox? renderBox =
          _menuButtonKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      // 计算按钮中心点的全局位置
      final Offset position = renderBox.localToGlobal(
        Offset(renderBox.size.width / 2, renderBox.size.height / 2),
      );

      // 准备菜单选项
      final List<AppleMenuOption> menuOptions = [];

      // 手动创建每个菜单选项，而不是使用map转换
      for (final item in _tabItems) {
        final title = item['title'] as String;
        final icon = item['icon'] as IconData?;
        final isSpecial = (item['isSpecial'] ?? false) as bool;

        // 特殊处理收藏菜单项
        if (title == '添加到收藏库') {
          // 根据收藏状态决定显示的文本和图标
          final favoriteTitle =
              _isCurrentQuestionFavorited ? '从收藏库删除' : '添加到收藏库';
          final favoriteIcon =
              _isCurrentQuestionFavorited
                  ? CupertinoIcons.bookmark_fill
                  : CupertinoIcons.bookmark;

          print(
            '创建收藏菜单项: 标题=$favoriteTitle, 图标=${favoriteIcon.toString()}, 状态=$_isCurrentQuestionFavorited',
          );

          menuOptions.add(
            AppleMenuOption(
              title: favoriteTitle,
              icon: _getOutlinedIcon(favoriteIcon),
              isSpecial: isSpecial,
              customTextColor: _isCurrentQuestionFavorited ? Colors.red : null,
              customIconColor: _isCurrentQuestionFavorited ? Colors.red : null,
              onTap: () {
                print('点击收藏菜单项: 当前状态=$_isCurrentQuestionFavorited');
                // 根据收藏状态执行不同操作
                if (_isCurrentQuestionFavorited) {
                  _removeFromFavorites();
                } else {
                  _addToFavorites();
                }
              },
            ),
          );
          continue; // 跳过后续处理
        }

        // 判断是否为"退出学习"菜单项
        final bool isExitItem = title == '退出学习';

        menuOptions.add(
          AppleMenuOption(
            title: title,
            icon: _getOutlinedIcon(icon),
            isSpecial: isSpecial,
            // 自定义颜色属性
            customTextColor: isExitItem ? Colors.red : null,
            customIconColor: isExitItem ? Colors.red : null,
            onTap: () {
              // 处理菜单项点击事件
              debugPrint('点击了菜单项: $title');

              if (title == '前往收藏库') {
                // 导航到收藏本页面
                Navigator.of(context).push(
                  CupertinoPageRoute(
                    builder:
                        (context) => FavoritesPage(courseId: widget.courseId),
                  ),
                );
              } else if (title == '前往错题库') {
                // 导航到错题本页面
                Navigator.of(context).push(
                  CupertinoPageRoute(
                    builder:
                        (context) =>
                            WrongQuestionsPage(courseId: widget.courseId),
                  ),
                );
              } else if (title == '学习历史') {
                // 导航到学习历史页面
                Navigator.of(context).push(
                  CupertinoPageRoute(
                    builder:
                        (context) =>
                            StudyHistoryPage(courseId: widget.courseId),
                  ),
                );
              } else if (title == '日排行榜') {
                // 导航到排行榜页面
                Navigator.of(context).push(
                  CupertinoPageRoute(builder: (context) => LeaderboardPage()),
                );
              } else if (title == '进行模拟考') {
                // 进行模拟考试功能
                _showNotification('模拟考试功能即将推出');
              } else if (title == '退出学习') {
                // 退出学习
                _navigateBackToLearningPage();
              }
            },
          ),
        );
      }

      // 显示菜单
      AppleStyleMenu.show(
        context: context,
        options: menuOptions,
        position: position,
      );
    });
  }

  // 在答题区域滚动结束后确保标题可见
  void _ensureNavTitleVisibilityAfterScroll(double scrollTarget) {
    _scrollController
        .animateTo(
          scrollTarget,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOut,
        )
        .then((_) {
          // 强制显示导航栏标题
          setState(() {
            _navTitleOpacity = 1.0;
          });

          // 延迟再次检查，确保在所有动画完成后标题可见
          Future.delayed(const Duration(milliseconds: 200), () {
            if (mounted) {
              _updateNavTitleVisibility();
            }
          });
        });
  }

  // 显示通知对话框的辅助方法
  void _showNotification(String message) {
    if (!mounted) return;
    // 使用CupertinoDialog而不是SnackBar
    showCupertinoDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        // 自动关闭对话框的定时器
        Future.delayed(const Duration(seconds: 2), () {
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
        });

        return CupertinoAlertDialog(content: Text(message));
      },
    );
  }

  // 显示筛选弹窗
  void _showFilterPopup() async {
    try {
      // 构建筛选组和选项
      final List<FilterGroup> filterGroups = [
        // 题目类型
        FilterGroup(
          title: '题目类型',
          options: [
            FilterOption(id: 'single_choice', title: '单选题'),
            FilterOption(id: 'multiple_choice', title: '多选题'),
            FilterOption(id: 'true_false', title: '判断题'),
            FilterOption(id: 'fill_blank', title: '填空题'),
          ],
          isMultiSelect: true,
        ),
        // 题目难度
        FilterGroup(
          title: '题目难度',
          options: [
            FilterOption(id: 'easy', title: '简单'),
            FilterOption(id: 'medium', title: '中等'),
            FilterOption(id: 'hard', title: '困难'),
          ],
          isMultiSelect: true,
        ),
        // 知识点分类
        FilterGroup(
          title: '知识点分类',
          options: [
            FilterOption(id: 'core_concept', title: '核心概念'),
            FilterOption(id: 'key_formula', title: '关键公式'),
            FilterOption(id: 'application', title: '实际应用'),
            FilterOption(id: 'common_error', title: '常见错误'),
          ],
          isMultiSelect: true,
        ),
        // 做题状态
        FilterGroup(
          title: '做题状态',
          options: [
            FilterOption(id: 'all', title: '全部题目'),
            FilterOption(id: 'correct', title: '做对的题'),
            FilterOption(id: 'incorrect', title: '做错的题'),
            FilterOption(id: 'unanswered', title: '未做的题'),
          ],
        ),
      ];

      // 如果已有筛选条件，更新选项选中状态
      if (_hasActiveFilters) {
        for (var group in filterGroups) {
          final selectedIds =
              _currentFilter!.selectedFilters[group.title] ?? [];
          for (var option in group.options) {
            option.isSelected = selectedIds.contains(option.id);
          }
        }
      }

      // 显示筛选弹窗
      final result = await FilterPopup.show(
        context: context,
        filterGroups: filterGroups,
        defaultToSmartRecommendation: !_hasActiveFilters,
      );

      // 处理筛选结果
      if (result != null) {
        setState(() {
          _currentFilter = result;

          // 检查是否为智能出题模式
          if (result.selectedFilters.containsKey('smartMode') &&
              result.selectedFilters['smartMode']!.contains('enabled')) {
            // 如果是智能出题模式，重置为默认状态
            _resetToDefaultQuestions();
          } else {
            // 否则应用其他筛选条件
            _applyFilters();
          }
        });
      }
    } catch (e) {
      print('筛选弹窗错误: $e');
      // 使用新方法显示通知
      _showNotification('显示筛选弹窗时发生错误，请稍后再试');
    }
  }

  // 应用筛选条件
  void _applyFilters() {
    if (_currentFilter == null || _currentFilter!.isEmpty) {
      // 重置为默认状态
      _resetToDefaultQuestions();
      return;
    }

    _filterQuestions();
  }

  // 重置为默认题目列表
  void _resetToDefaultQuestions() {
    setState(() {
      // 恢复原始题目列表
      if (_originalQuestionList.isNotEmpty) {
        _questionList = List.from(_originalQuestionList);
        _currentQuestionIndex = 0;
        _resetQuestionState();
      }
    });
  }

  // 根据筛选条件过滤题目
  void _filterQuestions() {
    if (_questionList.isEmpty || _currentFilter == null) return;

    try {
      // 创建一个过滤后的题目列表
      List<Question> filteredQuestions = List.from(_questionList);

      // 题目类型筛选
      final questionTypes = _currentFilter!.selectedFilters['题目类型'] ?? [];
      if (questionTypes.isNotEmpty) {
        filteredQuestions =
            filteredQuestions.where((question) {
              // 根据Question类的type属性进行匹配
              String typeId = '';
              switch (question.type) {
                case QuestionType.singleChoice:
                  typeId = 'single_choice';
                  break;
                case QuestionType.multipleChoice:
                  typeId = 'multiple_choice';
                  break;
                case QuestionType.trueFalse:
                  typeId = 'true_false';
                  break;
                case QuestionType.fillBlank:
                  typeId = 'fill_blank';
                  break;
                default:
                  typeId = 'other';
              }
              return questionTypes.contains(typeId);
            }).toList();
      }

      // 难度筛选 - 使用模拟数据
      final difficulties = _currentFilter!.selectedFilters['题目难度'] ?? [];
      if (difficulties.isNotEmpty) {
        // 模拟给每个题目分配一个难度
        filteredQuestions =
            filteredQuestions.where((question) {
              // 使用题目ID作为随机数种子
              final difficultySeed = question.id % 3;
              String difficulty;

              // 根据ID模拟不同难度
              switch (difficultySeed) {
                case 0:
                  difficulty = 'easy';
                  break;
                case 1:
                  difficulty = 'medium';
                  break;
                case 2:
                  difficulty = 'hard';
                  break;
                default:
                  difficulty = 'medium';
              }

              return difficulties.contains(difficulty);
            }).toList();
      }

      // 知识点分类筛选 - 使用模拟数据
      final categories = _currentFilter!.selectedFilters['知识点分类'] ?? [];
      if (categories.isNotEmpty) {
        // 模拟给每个题目分配一个知识点分类
        filteredQuestions =
            filteredQuestions.where((question) {
              // 使用题目ID作为随机数种子
              final categorySeed = (question.id * 7) % 4;
              String category;

              // 根据ID模拟不同知识点分类
              switch (categorySeed) {
                case 0:
                  category = 'core_concept';
                  break;
                case 1:
                  category = 'key_formula';
                  break;
                case 2:
                  category = 'application';
                  break;
                case 3:
                  category = 'common_error';
                  break;
                default:
                  category = 'core_concept';
              }

              return categories.contains(category);
            }).toList();
      }

      // 做题状态筛选 - 需要结合用户答题记录实现
      final statuses = _currentFilter!.selectedFilters['做题状态'] ?? [];
      if (statuses.isNotEmpty && !statuses.contains('all')) {
        // 默认显示所有题目，除非特定选择了其他状态
        // 这里模拟题目状态
        filteredQuestions =
            filteredQuestions.where((question) {
              // 使用题目ID作为随机数种子
              final statusSeed = (question.id * 13) % 3;
              String status;

              // 根据ID模拟不同做题状态
              switch (statusSeed) {
                case 0:
                  status = 'correct'; // 已做对
                  break;
                case 1:
                  status = 'incorrect'; // 已做错
                  break;
                case 2:
                  status = 'unanswered'; // 未做
                  break;
                default:
                  status = 'unanswered';
              }

              return statuses.contains(status);
            }).toList();
      }

      // 更新当前显示的题目
      if (filteredQuestions.isEmpty) {
        // 如果过滤后没有题目，仍然显示提示
        _showNotification('没有符合条件的题目');
      } else {
        // 更新题目列表并重置当前题目索引
        setState(() {
          _questionList = filteredQuestions;
          _currentQuestionIndex = 0;

          // 如果需要更新UI，确保调用相关方法
          _resetQuestionState();
        });

        // 移除筛选成功提示
      }
    } catch (e) {
      print('筛选题目错误: $e');
      _showNotification('筛选题目时发生错误，请稍后再试');
    }
  }

  // 修改actions部分
  Widget _buildTopActions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 筛选按钮
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 4.0),
          decoration: BoxDecoration(
            // 背景色随滚动状态变化，但始终保持比导航栏略深
            color:
                _navTitleOpacity <= 0.01
                    ? Colors.black.withOpacity(0.2) // 滚动前使用较低透明度
                    : Color.lerp(
                      Colors.black.withOpacity(0.4), // 初始稍深的浅黑色
                      Colors.black.withOpacity(0.6), // 滚动后的深黑色
                      _navTitleOpacity,
                    ),
            borderRadius: BorderRadius.circular(15),
          ),
          child: FilterButton(
            size: 28,
            onPressed: _showFilterPopup,
            hasActiveFilters: _hasActiveFilters,
            backgroundColor: Colors.transparent,
          ),
        ),
        // 添加间距
        const SizedBox(width: 8.0),
        Padding(
          padding: const EdgeInsets.only(right: 12.0),
          child: Container(
            decoration: BoxDecoration(
              // 背景色随滚动状态变化，但始终保持比导航栏略深
              color:
                  _navTitleOpacity <= 0.01
                      ? Colors.black.withOpacity(0.2) // 滚动前使用较低透明度
                      : Color.lerp(
                        Colors.black.withOpacity(0.4), // 初始稍深的浅黑色
                        Colors.black.withOpacity(0.6), // 滚动后的深黑色
                        _navTitleOpacity,
                      ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: CustomMenuButton(
              size: 28,
              key: _menuButtonKey,
              onPressed: _openDrawer,
              backgroundColor: Colors.transparent,
            ),
          ),
        ),
      ],
    );
  }

  // 显示能量弹窗
  void _showEnergyModal([bool isEnergyEmpty = false]) {
    // 简单直接地显示能量耗尽对话框
    EnergyDialog.show(
      context: context,
      onContinueChallenge: () {
        // 关闭对话框
        Navigator.of(context).pop();

        // 显示广告加载对话框
        showCupertinoDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => CupertinoAlertDialog(
                title: const Text('Loading Ad'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 16),
                    const CupertinoActivityIndicator(),
                    const SizedBox(height: 16),
                    const Text('Loading advertisement, please wait...'),
                  ],
                ),
              ),
        );

        // 模拟广告加载和播放时间
        Future.delayed(const Duration(seconds: 2), () {
          // 关闭加载提示
          Navigator.of(context).pop();

          // 补满能量
          EnergyButton.resetEnergyToFull();

          // 显示能量已恢复的提示
          showCupertinoDialog(
            context: context,
            barrierDismissible: true,
            builder:
                (context) => CupertinoAlertDialog(
                  title: const Text('Energy Restored'),
                  content: const Text(
                    'Thanks for watching the ad. Your energy has been fully restored!',
                  ),
                  actions: [
                    CupertinoDialogAction(
                      child: const Text('OK'),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
          );
        });
      },
    );
  }

  // 处理能量变化的回调
  void _handleEnergyChange(int energy) {
    // 当能量为0时，可以在这里处理全局状态
    print('当前能量值: $energy');
  }

  // 修改返回按钮处理逻辑
  void _handleBackAction() {
    // 保存当前状态
    _saveQuizState();
    // 返回上一页
    _navigateBackToLearningPage();
  }

  // 获取空心图标的辅助方法
  IconData _getOutlinedIcon(IconData? solidIcon) {
    // 将实心图标转换为对应的空心图标
    // 这里列出常用的实心图标和对应的空心图标
    if (solidIcon == Icons.favorite) return Icons.favorite_border;
    if (solidIcon == Icons.star) return Icons.star_border;
    if (solidIcon == Icons.bookmark) return Icons.bookmark_border;
    if (solidIcon == Icons.access_time) return Icons.access_time_outlined;
    if (solidIcon == Icons.person) return Icons.person_outline;
    if (solidIcon == Icons.settings) return Icons.settings_outlined;
    if (solidIcon == Icons.history) return Icons.history_outlined;
    if (solidIcon == Icons.notifications) return Icons.notifications_outlined;
    if (solidIcon == Icons.add) return Icons.add_outlined;
    if (solidIcon == CupertinoIcons.heart_fill) return CupertinoIcons.heart;
    if (solidIcon == CupertinoIcons.star_fill) return CupertinoIcons.star;
    if (solidIcon == CupertinoIcons.bookmark_fill)
      return CupertinoIcons.bookmark;
    if (solidIcon == CupertinoIcons.time_solid) return CupertinoIcons.time;
    if (solidIcon == CupertinoIcons.person_fill) return CupertinoIcons.person;
    if (solidIcon == CupertinoIcons.settings_solid)
      return CupertinoIcons.settings;

    // 如果没有匹配的空心图标，则返回原图标
    return solidIcon ?? Icons.more_horiz_outlined;
  }

  // 切换题目时检查收藏状态的方法
  Future<void> _checkCurrentQuestionFavoriteStatus() async {
    if (_currentQuestion == null) return;

    print('开始检查题目收藏状态: 题目ID=${_currentQuestion!.id}, 课程ID=${widget.courseId}');

    // 这里应该从持久化存储或API获取当前题目的收藏状态
    // 为了演示，这里使用SharedPreferences模拟
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorites') ?? '[]';
      print('获取到收藏列表JSON: $favoritesJson');

      final favorites = jsonDecode(favoritesJson) as List;
      print('收藏列表中有${favorites.length}个项目');

      // 检查当前题目是否在收藏列表中
      final isFavorited = favorites.any((item) {
        final bool result =
            item is Map &&
            item['id'] == _currentQuestion!.id &&
            item['courseId'] == widget.courseId;

        if (result) {
          print('找到匹配的收藏项: ${item.toString()}');
        }

        return result;
      });

      print('题目收藏状态: ${isFavorited ? "已收藏" : "未收藏"}');

      if (mounted) {
        setState(() {
          _isCurrentQuestionFavorited = isFavorited;
        });
        print('已更新收藏状态变量: _isCurrentQuestionFavorited = $isFavorited');
      }
    } catch (e) {
      print('检查收藏状态失败: $e');
    }
  }

  // 添加收藏的方法
  Future<void> _addToFavorites() async {
    if (_currentQuestion == null) return;

    print('开始添加收藏: 题目ID=${_currentQuestion!.id}, 课程ID=${widget.courseId}');

    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorites') ?? '[]';
      print('获取到原收藏列表: $favoritesJson');

      final favorites = jsonDecode(favoritesJson) as List;
      print('原收藏列表包含${favorites.length}个项目');

      // 创建新的收藏项
      final newFavorite = {
        'id': _currentQuestion!.id,
        'content': _currentQuestion!.content,
        'courseId': widget.courseId,
        'courseName': widget.courseName ?? '未知课程',
        'date': DateTime.now().toString().substring(0, 10),
      };

      print('创建新收藏项: $newFavorite');

      // 添加到收藏列表
      favorites.add(newFavorite);
      print('更新后收藏列表包含${favorites.length}个项目');

      // 保存更新后的收藏列表
      final newFavoritesJson = jsonEncode(favorites);
      print('新收藏列表JSON: $newFavoritesJson');
      await prefs.setString('favorites', newFavoritesJson);
      print('已保存新收藏列表到SharedPreferences');

      if (mounted) {
        setState(() {
          _isCurrentQuestionFavorited = true;
        });
        print('已更新收藏状态变量: _isCurrentQuestionFavorited = true');

        // 显示自定义收藏成功提示
        _showSuccessToast('已添加到收藏库');
        print('已显示成功提示');
      }
    } catch (e) {
      print('添加收藏失败: $e');
      if (mounted) {
        _showNotification('添加收藏失败，请重试');
      }
    }
  }

  // 移除收藏的方法
  Future<void> _removeFromFavorites() async {
    if (_currentQuestion == null) return;

    // 显示自定义的底部确认弹窗
    bool? confirmed = await showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final bottomPadding = MediaQuery.of(context).padding.bottom;

        // 辅助方法构建操作项
        Widget buildActionItem(
          String text,
          VoidCallback onPressed, {
          bool isDestructive = false,
          FontWeight fontWeight = FontWeight.normal,
        }) {
          return GestureDetector(
            onTap: onPressed,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              alignment: Alignment.center,
              child: Text(
                text,
                style: TextStyle(
                  color: const Color(0xFFE0464C), // 统一使用自定义红色
                  fontSize: 17.0, // 统一字体大小
                  fontWeight: fontWeight,
                ),
              ),
            ),
          );
        }

        return Padding(
          // 外层Padding，用于屏幕边缘和底部安全区域
          padding: EdgeInsets.only(
            left: 8.0,
            right: 8.0,
            bottom: bottomPadding > 0 ? bottomPadding : 8.0,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end, // 整体底部对齐
            children: [
              // ----- 标题和主要操作区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15.0,
                    sigmaY: 15.0,
                  ), // AppleStyleMenu模糊
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withOpacity(0.92), // AppleStyleMenu背景色
                      // borderRadius已由ClipRRect处理
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                            horizontal: 20.0,
                          ),
                          child: Text(
                            "确定要从收藏库删除这个题目吗？",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Color(0xFFE5E5E7),
                              fontSize: 13.0,
                              fontWeight: FontWeight.bold, // 标题加粗
                            ),
                          ),
                        ),
                        // 分隔线
                        Divider(
                          height: 0.5,
                          thickness: 0.5,
                          color: Colors.grey[700]?.withOpacity(0.5),
                        ),
                        // "从收藏库删除" 按钮
                        buildActionItem(
                          "从收藏库删除",
                          () => Navigator.of(context).pop(true),
                          fontWeight: FontWeight.bold, // "从收藏库删除"按钮加粗
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8.0), // 主要操作区和取消按钮之间的间距
              // ----- 取消按钮区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15.0,
                    sigmaY: 15.0,
                  ), // AppleStyleMenu模糊
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withOpacity(0.92), // AppleStyleMenu背景色
                      // borderRadius已由ClipRRect处理
                    ),
                    child: buildActionItem(
                      "取消",
                      () => Navigator.of(context).pop(false),
                      fontWeight: FontWeight.bold, // "取消"按钮加粗
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    // 如果用户确认删除
    if (confirmed == true) {
      print(
        '用户确认从收藏库删除: 题目ID=${_currentQuestion!.id}, 课程ID=${widget.courseId}',
      );
      try {
        final prefs = await SharedPreferences.getInstance();
        final favoritesJson = prefs.getString('favorites') ?? '[]';
        print('获取到原收藏列表: $favoritesJson');

        final List favorites = jsonDecode(favoritesJson);
        print('原收藏列表包含${favorites.length}个项目');

        // 移除匹配的收藏项
        final updatedFavorites =
            favorites.where((item) {
              final bool shouldKeep =
                  !(item is Map &&
                      item['id'] == _currentQuestion!.id &&
                      item['courseId'] == widget.courseId);
              if (!shouldKeep) {
                print('找到要移除的收藏项: ${item.toString()}');
              }
              return shouldKeep;
            }).toList();

        print('更新后收藏列表包含${updatedFavorites.length}个项目');

        // 保存更新后的收藏列表
        final newFavoritesJson = jsonEncode(updatedFavorites);
        print('新收藏列表JSON: $newFavoritesJson');
        await prefs.setString('favorites', newFavoritesJson);
        print('已保存新收藏列表到SharedPreferences');

        if (mounted) {
          setState(() {
            _isCurrentQuestionFavorited = false;
          });
          print('已更新收藏状态变量: _isCurrentQuestionFavorited = false');
          // 根据用户要求，此处不再显示任何提示
          // print('已显示删除成功提示'); // 原有的提示已移除
        }
      } catch (e) {
        print('移除收藏失败 (在确认后): $e');
        if (mounted) {
          // 如果实际删除操作出错，仍然显示错误提示
          _showNotification('从收藏库删除失败，请重试');
        }
      }
    } else {
      print('用户取消从收藏库删除');
    }
  }

  // 显示收藏成功的自定义提示
  void _showSuccessToast(String message) {
    if (!mounted) return;

    // 创建覆盖层
    final overlay = Overlay.of(context);

    // 创建覆盖层条目
    OverlayEntry overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).padding.bottom + 30, // 调整到底部，并增加一些边距
            left: 0,
            right: 0,
            child: Center(
              // 使提示框水平居中
              child: Material(
                color: Colors.transparent, // Material 本身透明
                child: ClipRRect(
                  // 用于实现圆角毛玻璃
                  borderRadius: BorderRadius.circular(10), // 统一圆角
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 15,
                      sigmaY: 15,
                    ), // 匹配AppleStyleMenu的模糊参数
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 18,
                      ), // 调整水平和垂直内边距
                      decoration: BoxDecoration(
                        color: const Color(
                          0xFF1C1C1E,
                        ).withOpacity(0.92), // 匹配AppleStyleMenu的颜色和透明度
                        borderRadius: BorderRadius.circular(10), // 统一圆角
                      ),
                      child: Row(
                        // 使用 Row 排列图标和文本
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min, // Row 的大小适应内容
                        children: [
                          const Icon(
                            // 使用实心填充图标
                            CupertinoIcons
                                .check_mark_circled_solid, // 更符合图片中的实心风格
                            color: Colors.white,
                            size: 16, // 调整图标大小以匹配文本视觉高度
                          ),
                          const SizedBox(width: 8), // 减小图标和文本之间的间距
                          Text(
                            message,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14, // 调整字体大小
                              fontWeight: FontWeight.w900, // 使文本更粗
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
    );

    // 插入覆盖层
    overlay.insert(overlayEntry);

    // 2秒后移除
    Future.delayed(const Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }
}

/// 选项 Widget
class OptionWidget extends StatelessWidget {
  final String optionText;
  final bool isSelected;
  final bool showCorrect;
  final bool showIncorrect;
  final VoidCallback? onTap;

  const OptionWidget({
    super.key,
    required this.optionText,
    required this.isSelected,
    this.showCorrect = false,
    this.showIncorrect = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    Color textColor = Colors.white;
    Color borderColor = Colors.white.withOpacity(0.2);
    Color backgroundColor = Colors.black.withOpacity(0.2);
    IconData? trailingIcon;

    // 判断状态来设置样式
    if (showCorrect) {
      borderColor = Colors.green;
      backgroundColor = Colors.green.withOpacity(0.3);
      trailingIcon = CupertinoIcons.check_mark_circled_solid;
    } else if (showIncorrect) {
      borderColor = Colors.red;
      backgroundColor = Colors.red.withOpacity(0.3);
      trailingIcon = CupertinoIcons.xmark_circle_fill;
    } else if (isSelected) {
      borderColor = Colors.white;
      backgroundColor = Colors.white.withOpacity(0.1);
    }

    return GestureDetector(
      onTap: onTap, // 可能为null，此时不可点击
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 16.0,
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(color: borderColor, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    spreadRadius: 1,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      optionText,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        fontFamily: 'Noto Sans SC',
                      ),
                    ),
                  ),
                  if (trailingIcon != null)
                    Icon(
                      trailingIcon,
                      color: showCorrect ? Colors.green : Colors.red,
                      size: 20,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// 玻璃效果按钮
class GlassButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;

  const GlassButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onPressed,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.2),
                  Colors.white.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Icon(icon, color: Colors.white, size: 20),
                  ),
                Text(
                  text,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 17,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Noto Sans SC',
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 解析卡片 Widget
class ExplanationCard extends StatefulWidget {
  final String correctAnswer;
  final String explanation;

  const ExplanationCard({
    super.key,
    required this.correctAnswer,
    required this.explanation,
  });

  @override
  State<ExplanationCard> createState() => _ExplanationCardState();
}

class _ExplanationCardState extends State<ExplanationCard>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  // AI问答相关状态
  final TextEditingController _questionController = TextEditingController();
  String? _aiAnswer;
  bool _isLoadingAnswer = false;
  final GlobalKey _aiAnswerKey = GlobalKey();

  // 猜你想问相关状态
  List<String> _suggestedQuestions = [];
  String? _lastUserQuestion;
  bool _isUpdatingSuggestions = false;

  // 猜你想问的初始问题列表
  final List<String> _initialSuggestions = [
    '这个知识点的应用场景有哪些？',
    '为什么这个答案是正确的？',
    '如何记忆这个知识点？',
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    )..repeat();

    // 初始化猜你想问问题
    _suggestedQuestions = List.from(_initialSuggestions);
  }

  @override
  void dispose() {
    _controller.dispose();
    _questionController.dispose();
    super.dispose();
  }

  // 处理提问
  void _handleQuestion(String question) {
    if (question.isEmpty) return;

    // 保存用户当前问题，用于后续生成新的建议
    _lastUserQuestion = question;

    setState(() {
      _isLoadingAnswer = true;
    });

    // 模拟AI回答加载
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final fullAnswer =
            '这是关于"$question"的AI回答。\n\n该知识点主要涉及到...(此处为AI生成的详细回答)。\n\n建议你可以通过以下方式加深理解：\n1. 联系实际案例\n2. 对比相似概念\n3. 做更多相关习题';
        setState(() {
          _aiAnswer = "";
          _isLoadingAnswer = false;
        });

        // 启动流式打字效果
        _startTypingAnimation(
          fullAnswer,
          onComplete: () {
            // 打字完成后更新猜你想问
            _updateSuggestedQuestions();
          },
        );
      }
    });
  }

  // 更新猜你想问内容
  void _updateSuggestedQuestions() {
    if (_lastUserQuestion == null || _aiAnswer == null) return;

    setState(() {
      _isUpdatingSuggestions = true;
    });

    // 模拟生成新的猜你想问问题
    // 实际项目中可以调用后端API或本地模型来生成
    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        // 基于用户问题和AI回答生成新的建议问题
        // 此处为模拟逻辑，实际应调用AI接口
        final newSuggestions = _generateNewSuggestions(
          _lastUserQuestion!,
          _aiAnswer!,
        );

        setState(() {
          _suggestedQuestions = newSuggestions;
          _isUpdatingSuggestions = false;
        });
      }
    });
  }

  // 生成新的猜你想问问题（模拟）
  List<String> _generateNewSuggestions(String userQuestion, String aiAnswer) {
    // 这里是模拟逻辑，实际项目中可能通过API调用或本地模型生成

    // 简单的模拟逻辑：根据用户问题内容提供相关后续问题
    if (userQuestion.contains('应用场景') || userQuestion.contains('场景')) {
      return ['有哪些具体的例子？', '这些场景有什么共同点？', '如何在实际工作中应用？'];
    } else if (userQuestion.contains('为什么') || userQuestion.contains('原因')) {
      return ['有没有其他可能的解释？', '这个原理适用于所有情况吗？', '这与其他相关概念有什么联系？'];
    } else if (userQuestion.contains('记忆') || userQuestion.contains('学习')) {
      return ['有没有更好的记忆方法？', '如何检验我是否真正理解了？', '相似概念如何区分？'];
    } else {
      // 默认的通用后续问题
      return ['能详细解释一下吗？', '有什么需要注意的地方？', '这与课程中其他知识点的关系？'];
    }
  }

  // 流式打字动画效果
  void _startTypingAnimation(String text, {VoidCallback? onComplete}) {
    final characters = text.characters.toList();
    int currentIndex = 0;

    // 创建定时器，定期添加新字符
    Timer.periodic(const Duration(milliseconds: 15), (timer) {
      if (currentIndex < characters.length) {
        setState(() {
          _aiAnswer = (_aiAnswer ?? '') + characters[currentIndex];
        });
        currentIndex++;
      } else {
        // 结束动画
        timer.cancel();
        if (onComplete != null) {
          onComplete();
        }
      }
    });
  }

  // 发送用户输入的问题
  void _sendQuestion() {
    final question = _questionController.text.trim();
    if (question.isEmpty) return;

    _handleQuestion(question);
    _questionController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '正确答案: ${widget.correctAnswer}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontFamily: 'Noto Sans SC',
            ),
          ),
          if (widget.explanation.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                widget.explanation,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  height: 1.5,
                  fontFamily: 'Noto Sans SC',
                ),
              ),
            ),

          // AI提问系统
          const Padding(
            padding: EdgeInsets.only(top: 24.0, bottom: 12.0),
            child: Text(
              '猜你想问',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontFamily: 'Noto Sans SC',
              ),
            ),
          ),

          // 猜你想问问题列表 - 使用动画切换
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.0, 0.2),
                    end: Offset.zero,
                  ).animate(animation),
                  child: child,
                ),
              );
            },
            child:
                _isUpdatingSuggestions
                    ? const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      key: ValueKey('loading'),
                      child: Row(
                        children: [
                          CupertinoActivityIndicator(
                            color: Colors.white,
                            radius: 8,
                          ),
                          SizedBox(width: 8),
                          Text(
                            '更新中...',
                            style: TextStyle(color: Colors.white, fontSize: 13),
                          ),
                        ],
                      ),
                    )
                    : Wrap(
                      key: ValueKey(_suggestedQuestions.join(',')),
                      spacing: 8.0,
                      runSpacing: 8.0,
                      children:
                          _suggestedQuestions
                              .map((question) => _buildSuggestionChip(question))
                              .toList(),
                    ),
          ),

          // AI提问输入框
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 44,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(22),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _questionController,
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                      textAlign: TextAlign.left,
                      textAlignVertical: TextAlignVertical.center,
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        border: InputBorder.none,
                        hintText: '输入你的问题...',
                        hintStyle: TextStyle(
                          color: Colors.white.withOpacity(0.5),
                        ),
                        isDense: true,
                      ),
                      onSubmitted: (_) => _sendQuestion(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: _sendQuestion,
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF8A00FF),
                          const Color(0xFFCB3EFF),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: const Icon(
                      CupertinoIcons.arrow_up,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // AI回答区域
          if (_isLoadingAnswer)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: _buildLoadingIndicator(),
            )
          else if (_aiAnswer != null)
            Padding(
              key: _aiAnswerKey,
              padding: const EdgeInsets.only(top: 16.0),
              child: _buildAiAnswerBox(_aiAnswer!),
            ),
        ],
      ),
    );
  }

  // 构建猜你想问的问题标签
  Widget _buildSuggestionChip(String question) {
    return GestureDetector(
      onTap: () => _handleQuestion(question),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
        ),
        child: Text(
          question,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 13,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  // 构建AI回答加载指示器
  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF8A00FF).withOpacity(0.5),
            const Color(0xFFCB3EFF).withOpacity(0.5),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          const CupertinoActivityIndicator(color: Colors.white),
          const SizedBox(width: 12),
          Text(
            "AI思考中...",
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // 构建AI回答框
  Widget _buildAiAnswerBox(String answer) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF8A00FF).withOpacity(0.5),
            const Color(0xFFCB3EFF).withOpacity(0.5),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Text(
        answer,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 14,
          height: 1.6,
        ),
      ),
    );
  }
}

/// 渐变边框绘制器 - 从GradientBorderGlassCard提取
class _GradientBorderPainter extends CustomPainter {
  final double animationValue;
  final double borderRadius;
  final double borderWidth;
  final List<Color> gradientColors;

  _GradientBorderPainter({
    required this.animationValue,
    required this.borderRadius,
    this.borderWidth = 2.5, // 默认与题干区域一致的粗细
    List<Color>? gradientColors,
  }) : gradientColors =
           gradientColors ??
           [
             const Color(0xFF8A00FF), // 与题干区域一致的渐变色
             const Color(0xFFCB3EFF),
             const Color(0xFFFF5C93),
           ];

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 创建与题干区域一致的渐变
    final gradient = LinearGradient(
      colors: gradientColors,
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      transform: GradientRotation(animationValue * 2 * 3.1416),
    );

    // 定义圆角矩形
    final rRect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    // 创建外发光效果 - 这是关键部分
    for (double i = 0.0; i < 3; i += 1.0) {
      final Paint glowPaint =
          Paint()
            ..shader = gradient.createShader(rect)
            ..style = PaintingStyle.stroke
            ..strokeWidth = borderWidth - i * 0.5
            ..maskFilter = MaskFilter.blur(BlurStyle.normal, i * 0.5)
            ..strokeCap = StrokeCap.round
            ..strokeJoin = StrokeJoin.round
            ..isAntiAlias = true;

      canvas.drawRRect(rRect, glowPaint);
    }

    // 主边框
    final paint =
        Paint()
          ..shader = gradient.createShader(rect)
          ..style = PaintingStyle.stroke
          ..strokeWidth = borderWidth
          ..strokeCap = StrokeCap.round
          ..strokeJoin = StrokeJoin.round
          ..isAntiAlias = true;

    canvas.drawRRect(rRect, paint);
  }

  @override
  bool shouldRepaint(covariant _GradientBorderPainter oldDelegate) =>
      animationValue != oldDelegate.animationValue ||
      borderWidth != oldDelegate.borderWidth;
}

/// 记忆技巧卡片
class MemoryTipsCard extends StatelessWidget {
  final String tips;

  const MemoryTipsCard({super.key, required this.tips});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(color: Colors.transparent, width: 1.5),
      ),
      child: Text(
        tips,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          height: 1.5,
          fontFamily: 'Noto Sans SC',
        ),
      ),
    );
  }
}

/// 评论区卡片
class CommentsCard extends StatelessWidget {
  final List<Map<String, dynamic>> comments;

  const CommentsCard({super.key, required this.comments});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16.0),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8.0),
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: Colors.purple.withOpacity(0.15),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(
              color: Colors.purple.withOpacity(0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    CupertinoIcons.chat_bubble_2,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${comments.length}条学习评论',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Noto Sans SC',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ...comments.map((comment) => _buildCommentItem(comment)).toList(),
              const SizedBox(height: 8),
              _buildAddCommentButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
                child: const Center(
                  child: Icon(
                    CupertinoIcons.person_fill,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Text(
                comment['user'],
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                comment['time'],
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 42, top: 6),
            child: Text(
              comment['content'],
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.9),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddCommentButton() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '分享你的学习感悟...',
              style: TextStyle(
                color: Colors.white.withOpacity(0.5),
                fontSize: 14,
              ),
            ),
          ),
          Icon(
            CupertinoIcons.paperplane,
            color: Colors.white.withOpacity(0.5),
            size: 18,
          ),
        ],
      ),
    );
  }
}

/// 可折叠区域组件
class CollapsibleSection extends StatelessWidget {
  final String title;
  final Widget? titleWidget;
  final Widget child;
  final bool isExpanded;
  final VoidCallback onToggle;

  const CollapsibleSection({
    super.key,
    this.title = "",
    this.titleWidget,
    required this.child,
    required this.isExpanded,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题栏
        Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 4, left: 4, right: 4),
          child:
              titleWidget ??
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontFamily: 'Noto Sans SC',
                ),
              ),
        ),
        // 内容区域 - 始终显示
        child,
      ],
    );
  }
}
