import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // 用于触觉反馈
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../../widgets/animated_avatar_widget.dart';
import '../../constants/fonts.dart';
import '../../models/article_model.dart';
import '../../services/article_service.dart';
import '../../services/course_service.dart';
import '../../widgets/article_widgets.dart';
import '../common/article_detail_page.dart';
import '../../providers/user_provider.dart';
import 'dart:developer' as developer;
import '../../utils/page_transitions.dart'; // 引入页面转场动画
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../services/auth_service.dart'; // 添加AuthService导入

class InformationHubPage extends StatefulWidget {
  final int? courseId; // 接收所选课程ID参数
  final String? courseName; // 接收所选课程名称参数

  const InformationHubPage({super.key, this.courseId, this.courseName});

  @override
  State<InformationHubPage> createState() => _InformationHubPageState();
}

// 省份数据模型
class Province {
  final String code;
  final String name;

  Province({required this.code, required this.name});
}

// 全国省份数据
final List<Province> provinces = [
  Province(code: 'all', name: '全国'),
  Province(code: 'beijing', name: '北京'),
  Province(code: 'tianjin', name: '天津'),
  Province(code: 'hebei', name: '河北'),
  Province(code: 'shanxi', name: '山西'),
  Province(code: 'neimenggu', name: '内蒙古'),
  Province(code: 'liaoning', name: '辽宁'),
  Province(code: 'jilin', name: '吉林'),
  Province(code: 'heilongjiang', name: '黑龙江'),
  Province(code: 'shanghai', name: '上海'),
  Province(code: 'jiangsu', name: '江苏'),
  Province(code: 'zhejiang', name: '浙江'),
  Province(code: 'anhui', name: '安徽'),
  Province(code: 'fujian', name: '福建'),
  Province(code: 'jiangxi', name: '江西'),
  Province(code: 'shandong', name: '山东'),
  Province(code: 'henan', name: '河南'),
  Province(code: 'hubei', name: '湖北'),
  Province(code: 'hunan', name: '湖南'),
  Province(code: 'guangdong', name: '广东'),
  Province(code: 'guangxi', name: '广西'),
  Province(code: 'hainan', name: '海南'),
  Province(code: 'chongqing', name: '重庆'),
  Province(code: 'sichuan', name: '四川'),
  Province(code: 'guizhou', name: '贵州'),
  Province(code: 'yunnan', name: '云南'),
  Province(code: 'xizang', name: '西藏'),
  Province(code: 'shaanxi', name: '陕西'),
  Province(code: 'gansu', name: '甘肃'),
  Province(code: 'qinghai', name: '青海'),
  Province(code: 'ningxia', name: '宁夏'),
  Province(code: 'xinjiang', name: '新疆'),
];

// 自定义自定义文章模型，支持本地资产图片
class LocalArticle extends Article {
  final String? localAssetImage;

  LocalArticle({
    required String id,
    required String title,
    String subtitle = '',
    required String content,
    required DateTime publishDate,
    this.localAssetImage,
    String thumbnailUrl = '',
    String authorName = '',
    String source = '',
    List<String> tags = const [],
    List<String> categoryIds = const [],
    bool isFeatured = false,
    int readTimeMinutes = 0,
  }) : super(
         id: id,
         title: title,
         subtitle: subtitle,
         content: content,
         publishDate: publishDate,
         thumbnailUrl: thumbnailUrl,
         authorName: authorName,
         source: source,
         tags: tags,
         categoryIds: categoryIds,
         isFeatured: isFeatured,
         readTimeMinutes: readTimeMinutes,
       );
}

// 自定义课程分类模型，用于显示用户已加入课程
class CourseCategoryItem {
  final int courseId;
  final String id; // 用于文章分类的ID
  final String name; // 课程名称
  final String? imageUrl; // 课程图片

  CourseCategoryItem({
    required this.courseId,
    required this.id,
    required this.name,
    this.imageUrl,
  });
}

class _InformationHubPageState extends State<InformationHubPage> {
  final ArticleService _articleService = ArticleService();
  final CourseService _courseService = CourseService();
  List<dynamic> _latestArticles = []; // 使用dynamic类型以支持混合文章类型
  List<CourseCategoryItem> _courseCategories = []; // 用户已加入的课程作为分类
  bool _isLoading = true;
  String? _selectedCategoryId;
  int? _selectedCourseId; // 当前选择的课程ID
  String _selectedCourseName = ''; // 当前选择的课程名称

  // 新增省份筛选相关状态
  String _selectedProvinceCode = 'all'; // 默认选择"全国"
  String _selectedProvinceName = '全国';

  // 课程ID与文章分类ID的映射关系
  // 使用每个课程ID的字符串形式作为分类ID
  // 这样可以确保每个课程都有唯一的分类ID
  Map<int, String> _getCategoryIdForCourse(int courseId) {
    // 根据课程ID生成唯一的分类ID
    return {courseId: 'course_$courseId'};
  }

  // 获取本地图片资源
  String _getLocalImageForIndex(int index) {
    final images = [
      'assets/images/learning_bg1.png',
      'assets/images/learning_bg2.png',
    ];
    return images[index % images.length];
  }

  @override
  void initState() {
    super.initState();

    // 如果有传入的课程ID和课程名称，则设置为当前选中的课程
    if (widget.courseId != null && widget.courseName != null) {
      _selectedCourseId = widget.courseId;
      _selectedCourseName = widget.courseName!;
      developer.log(
        '资讯库页面接收到课程: id=${widget.courseId}, name=${widget.courseName}',
      );
    }

    _loadData();
  }

  // 显示省份选择器
  void _showProvinceSelector() {
    HapticFeedback.selectionClick();

    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 300,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Column(
            children: [
              // 标题栏
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.white.withOpacity(0.1),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 16,
                        ),
                      ),
                    ),
                    Text(
                      '选择地区',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '确定',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 省份列表
              Expanded(
                child: CupertinoPicker(
                  backgroundColor: Colors.transparent,
                  itemExtent: 44,
                  scrollController: FixedExtentScrollController(
                    initialItem: provinces.indexWhere(
                      (p) => p.code == _selectedProvinceCode,
                    ),
                  ),
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _selectedProvinceCode = provinces[index].code;
                      _selectedProvinceName = provinces[index].name;
                    });
                    _loadData(); // 重新加载数据
                  },
                  children:
                      provinces.map((Province province) {
                        return Center(
                          child: Text(
                            province.name,
                            style: TextStyle(color: Colors.white, fontSize: 18),
                          ),
                        );
                      }).toList(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _loadData() async {
    if (!mounted) return; // 添加mounted检查

    setState(() {
      _isLoading = true;
    });

    try {
      // 先获取用户已加入的课程
      final userCourses = await _getUserEnrolledCourses();
      developer.log('获取到用户课程数量: ${userCourses.length}');

      // 将用户的课程转换为分类项
      final courseCategoryItems = _createCourseCategoryItems(userCourses);

      // 如果有指定课程ID，则自动选择该课程对应的分类
      if (_selectedCourseId != null) {
        for (var category in courseCategoryItems) {
          if (category.courseId == _selectedCourseId) {
            _selectedCategoryId = category.id;
            break;
          }
        }
      }

      // 为每个课程生成文章
      List<dynamic> allCourseArticles = [];

      // 如果有选中的分类ID
      if (_selectedCategoryId != null) {
        final selectedCategory = courseCategoryItems.firstWhere(
          (category) => category.id == _selectedCategoryId,
          orElse:
              () =>
                  courseCategoryItems.isNotEmpty
                      ? courseCategoryItems.first
                      : CourseCategoryItem(courseId: 0, id: '', name: '未知课程'),
        );

        // 为选中的课程生成文章
        final courseArticles = _generateArticlesForCourse(selectedCategory);
        allCourseArticles = courseArticles;
      } else {
        // 否则生成所有课程的文章
        for (var course in courseCategoryItems) {
          final courseArticles = _generateArticlesForCourse(course);
          allCourseArticles.addAll(courseArticles);
        }
      }

      // 根据选择的省份生成地区相关文章
      final provinceArticles = _generateProvinceArticles();
      allCourseArticles.addAll(provinceArticles);

      // 只有当没有课程相关文章时，才尝试获取通用文章
      List<dynamic> articles;
      if (allCourseArticles.isEmpty) {
        // 获取默认文章并替换为本地图片
        List<dynamic> defaultArticles = _getDefaultArticles();
        articles = defaultArticles;
      } else {
        articles = allCourseArticles;
      }

      // 添加mounted检查
      if (!mounted) return;

      setState(() {
        _courseCategories = courseCategoryItems;
        _latestArticles =
            articles..sort((a, b) => b.publishDate.compareTo(a.publishDate));
        _isLoading = false;
      });

      developer.log(
        '资讯库页面加载完成，共有${_courseCategories.length}个课程分类，${_latestArticles.length}篇文章',
      );
    } catch (e) {
      developer.log('加载资讯库数据出错: $e');

      // 添加mounted检查
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });
    }
  }

  // 生成省份相关的文章
  List<LocalArticle> _generateProvinceArticles() {
    if (_selectedProvinceCode == 'all') {
      return _getDefaultArticles();
    }

    final now = DateTime.now();
    final provinceIndex = _selectedProvinceCode.hashCode;

    return [
      LocalArticle(
        id: 'province_${_selectedProvinceCode}_1',
        title: '${_selectedProvinceName}地区政策解读',
        subtitle: '最新政策动态',
        content:
            '深入解读${_selectedProvinceName}地区最新的教育政策和考试政策变化，帮助考生及时了解政策动向，把握考试机会。包括最新的报名政策、考试安排、证书管理等重要信息。',
        publishDate: now.subtract(Duration(days: 1)),
        localAssetImage: _getLocalImageForIndex(provinceIndex),
        authorName: 'WakeUp团队',
        tags: [_selectedProvinceName, '政策解读', '考试动态'],
        readTimeMinutes: 8,
      ),
      LocalArticle(
        id: 'province_${_selectedProvinceCode}_2',
        title: '${_selectedProvinceName}考点分布与交通指南',
        subtitle: '考试场地信息',
        content:
            '详细介绍${_selectedProvinceName}地区各个考点的具体位置、交通路线、周边设施等信息。为考生提供全面的考点指南，包括地铁公交路线、停车场信息、住宿推荐等实用信息。',
        publishDate: now.subtract(Duration(days: 2)),
        localAssetImage: _getLocalImageForIndex(provinceIndex + 1),
        authorName: 'WakeUp团队',
        tags: [_selectedProvinceName, '考点信息', '交通指南'],
        readTimeMinutes: 6,
      ),
      LocalArticle(
        id: 'province_${_selectedProvinceCode}_3',
        title: '${_selectedProvinceName}历年考试数据分析',
        subtitle: '统计数据报告',
        content:
            '基于${_selectedProvinceName}地区历年考试数据，分析考试难度趋势、通过率变化、热门专业选择等。为考生提供数据支持，帮助制定更有效的备考策略。',
        publishDate: now.subtract(Duration(days: 3)),
        localAssetImage: _getLocalImageForIndex(provinceIndex + 2),
        authorName: 'WakeUp团队',
        tags: [_selectedProvinceName, '数据分析', '考试统计'],
        readTimeMinutes: 10,
      ),
      LocalArticle(
        id: 'province_${_selectedProvinceCode}_4',
        title: '${_selectedProvinceName}学习资源推荐',
        subtitle: '本地化学习指南',
        content:
            '推荐${_selectedProvinceName}地区优质的学习资源，包括培训机构、图书馆、学习小组等。结合当地实际情况，为考生提供个性化的学习建议和资源信息。',
        publishDate: now.subtract(Duration(days: 4)),
        localAssetImage: _getLocalImageForIndex(provinceIndex + 3),
        authorName: 'WakeUp团队',
        tags: [_selectedProvinceName, '学习资源', '培训推荐'],
        readTimeMinutes: 7,
      ),
    ];
  }

  // 创建默认文章，使用本地图片
  List<LocalArticle> _getDefaultArticles() {
    final now = DateTime.now();

    return [
      LocalArticle(
        id: 'default_1',
        title: '学习方法精要',
        subtitle: '高效学习指南',
        content: '高效学习不仅关乎时间管理，还涉及到正确的学习方法和策略。本文介绍多种实用学习技巧，帮助你在较短时间内掌握更多知识。',
        publishDate: now.subtract(const Duration(days: 2)),
        localAssetImage: _getLocalImageForIndex(0),
        authorName: 'WakeUp团队',
        tags: ['学习方法', '效率提升'],
        readTimeMinutes: 7,
      ),
      LocalArticle(
        id: 'default_2',
        title: '记忆力提升训练',
        subtitle: '增强知识记忆与保持',
        content:
            '通过特定的记忆技巧和规律性练习，可以显著提高你的记忆力。本文介绍了多种科学的记忆方法，包括间隔重复、思维导图等技术，让你的学习成效倍增。',
        publishDate: now.subtract(const Duration(days: 4)),
        localAssetImage: _getLocalImageForIndex(1),
        authorName: 'WakeUp团队',
        tags: ['记忆技巧', '学习效率'],
        readTimeMinutes: 9,
      ),
    ];
  }

  // 为课程生成示例文章
  List<LocalArticle> _generateArticlesForCourse(CourseCategoryItem course) {
    final now = DateTime.now();
    final courseIndex = course.courseId.hashCode;

    return [
      LocalArticle(
        id: 'auto_${course.courseId}_1',
        title: '${course.name}学习指南',
        subtitle: '快速入门必读',
        content:
            '这是一篇关于${course.name}的入门学习指南。本文将介绍${course.name}的基本概念、学习路径和实践方法，帮助你快速掌握核心知识点。',
        publishDate: now.subtract(const Duration(days: 1)),
        localAssetImage: _getLocalImageForIndex(courseIndex),
        authorName: 'WakeUp团队',
        tags: [course.name, '入门指南', '学习路径'],
        categoryIds: [course.id],
        readTimeMinutes: 5,
      ),
      LocalArticle(
        id: 'auto_${course.courseId}_2',
        title: '${course.name}实战技巧',
        subtitle: '提升实践能力',
        content:
            '这篇文章总结了${course.name}的实战技巧和常见问题解决方案。通过这些技巧，你将能够更高效地应用所学知识，解决实际问题。',
        publishDate: now.subtract(const Duration(days: 3)),
        localAssetImage: _getLocalImageForIndex(courseIndex + 1),
        authorName: 'WakeUp团队',
        tags: [course.name, '实战技巧', '问题解决'],
        categoryIds: [course.id],
        readTimeMinutes: 8,
      ),
      LocalArticle(
        id: 'auto_${course.courseId}_3',
        title: '${course.name}高级进阶',
        subtitle: '深入探索与应用',
        content:
            '通过这篇高级教程，你将了解${course.name}的进阶知识和最佳实践。我们将深入探讨复杂概念，并通过实际案例展示如何应用这些知识解决真实世界的问题。',
        publishDate: now.subtract(const Duration(days: 5)),
        localAssetImage: _getLocalImageForIndex(courseIndex + 2),
        authorName: 'WakeUp团队',
        tags: [course.name, '高级教程', '最佳实践'],
        categoryIds: [course.id],
        readTimeMinutes: 12,
      ),
    ];
  }

  // 获取用户已加入的课程
  Future<List<Map<String, dynamic>>> _getUserEnrolledCourses() async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn) {
        developer.log('用户未登录，无法获取课程');
        return [];
      }

      // 注意：这里不使用CourseService中的getUserCourses方法，
      // 因为我们需要获取用户在本应用中选择加入的课程，而不是购买的课程
      final userId = userProvider.userId;
      final token = userProvider.token;

      if (token == null) {
        developer.log('用户token为空，无法获取课程');
        return [];
      }

      // 使用user_course/list API直接获取用户已加入的课程
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/user_course/list/$userId'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> courses = jsonDecode(response.body);
        developer.log('成功获取到${courses.length}个用户课程');
        return List<Map<String, dynamic>>.from(courses);
      } else {
        developer.log('获取用户课程失败：${response.statusCode} ${response.body}');
        return [];
      }
    } catch (e) {
      developer.log('获取用户课程出错: $e');
      return [];
    }
  }

  // 将用户课程转换为分类项
  List<CourseCategoryItem> _createCourseCategoryItems(
    List<Map<String, dynamic>> userCourses,
  ) {
    final courseCategoryItems = <CourseCategoryItem>[];

    for (var i = 0; i < userCourses.length; i++) {
      final course = userCourses[i];

      // 兼容不同的API返回格式
      final courseId = course['course_id'] ?? course['id'] ?? 0;
      if (courseId == 0) continue;

      final courseName = course['course_name'] ?? course['name'] ?? '未命名课程';
      final courseImageUrl = course['cover_image'] ?? course['image_url'];

      // 为每个课程生成唯一的分类ID
      final categoryId = _getCategoryIdForCourse(courseId)[courseId]!;

      developer.log('添加课程分类: id=$courseId, 名称=$courseName, 分类ID=$categoryId');

      courseCategoryItems.add(
        CourseCategoryItem(
          courseId: courseId,
          id: categoryId,
          name: courseName,
          imageUrl: courseImageUrl,
        ),
      );
    }

    developer.log('用户共有${courseCategoryItems.length}个课程分类');
    return courseCategoryItems;
  }

  Future<void> _refreshData() async {
    await _loadData();
  }

  void _navigateToArticleDetail(dynamic article) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => ArticleDetailPage(article: article),
      ),
    );
  }

  void _selectCategory(String categoryId) {
    if (!mounted) return; // 添加mounted检查

    setState(() {
      if (_selectedCategoryId == categoryId) {
        _selectedCategoryId = null;
      } else {
        _selectedCategoryId = categoryId;
      }
    });

    _loadArticlesByCategory();
  }

  Future<void> _loadArticlesByCategory() async {
    if (_selectedCategoryId == null) {
      await _loadData();
      return;
    }

    if (!mounted) return; // 添加mounted检查

    setState(() {
      _isLoading = true;
    });

    try {
      // 找到选中的课程
      final selectedCourse = _courseCategories.firstWhere(
        (category) => category.id == _selectedCategoryId,
        orElse: () => CourseCategoryItem(courseId: 0, id: '', name: '未知课程'),
      );

      // 如果是有效的课程，生成文章
      if (selectedCourse.courseId > 0) {
        final courseArticles = _generateArticlesForCourse(selectedCourse);

        if (!mounted) return; // 添加mounted检查

        setState(() {
          _latestArticles = courseArticles;
          _isLoading = false;
        });

        developer.log('已加载${selectedCourse.name}的${courseArticles.length}篇文章');
        return;
      }

      // 没有有效课程时，返回默认文章
      final defaultArticles = _getDefaultArticles();

      if (!mounted) return; // 添加mounted检查

      setState(() {
        _latestArticles = defaultArticles;
        _isLoading = false;
      });
    } catch (e) {
      developer.log('加载分类文章出错: $e');

      if (!mounted) return; // 添加mounted检查

      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final titleText =
        AppLocalizations.of(context)!.informationHubPageTitle ?? '资讯库';
    final isChinese = Localizations.localeOf(context).languageCode == 'zh';

    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        // 使用Stack来叠加背景图片
        fit: StackFit.expand,
        children: [
          // 背景图片 - 与选题页面保持一致
          Positioned.fill(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: Image.asset(
                'assets/images/traditional_quiz_bg.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  developer.log('❌ 背景图片加载失败: $error');
                  // 出错时使用备选背景
                  return Image.asset(
                    'assets/images/learning_bg3.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  );
                },
              ),
            ),
          ),
          // 内容区域
          SafeArea(
            child: RefreshIndicator(
              backgroundColor: Colors.grey[900],
              color: Colors.white,
              onRefresh: _refreshData,
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                slivers: [
                  // 标题栏
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child:
                                (Platform.isIOS || Platform.isMacOS) &&
                                        !isChinese
                                    ? AppFonts.createBoldTextStack(
                                      titleText,
                                      fontSize: 36,
                                      letterSpacing: -0.5,
                                    )
                                    : Text(
                                      titleText,
                                      style: AppFonts.createTitleStyle(
                                        fontSize: 36,
                                        isLatinText: !isChinese,
                                      ),
                                    ),
                          ),
                          // 省份筛选按钮
                          Container(
                            margin: const EdgeInsets.only(right: 12),
                            child: CupertinoButton(
                              padding: EdgeInsets.zero,
                              onPressed: _showProvinceSelector,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.2),
                                    width: 0.5,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      CupertinoIcons.location,
                                      size: 14,
                                      color: Colors.white.withOpacity(0.8),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      _selectedProvinceName,
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.8),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(width: 2),
                                    Icon(
                                      CupertinoIcons.chevron_down,
                                      size: 12,
                                      color: Colors.white.withOpacity(0.6),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const AnimatedAvatarWidget(size: 36),
                        ],
                      ),
                    ),
                  ),

                  // 分类选择器
                  SliverToBoxAdapter(child: _buildCategorySelector()),

                  if (_isLoading)
                    const SliverFillRemaining(
                      child: Center(
                        child: CupertinoActivityIndicator(color: Colors.white),
                      ),
                    )
                  else ...[
                    // 最新文章
                    if (_latestArticles.isNotEmpty)
                      SliverToBoxAdapter(
                        child: CollectionHeader(
                          title:
                              _selectedCategoryId != null
                                  ? _courseCategories
                                      .firstWhere(
                                        (category) =>
                                            category.id == _selectedCategoryId,
                                        orElse:
                                            () => CourseCategoryItem(
                                              courseId: 0,
                                              id: '',
                                              name: '分类文章',
                                            ),
                                      )
                                      .name
                                  : _selectedProvinceCode != 'all'
                                  ? '${_selectedProvinceName}资讯'
                                  : '最新资讯',
                        ),
                      ),

                    if (_latestArticles.isEmpty)
                      const SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: Center(
                            child: Text(
                              '暂无文章',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      )
                    else
                      SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          final article = _latestArticles[index];
                          return Column(
                            children: [
                              _buildArticleCard(
                                article,
                                () => _navigateToArticleDetail(article),
                              ),
                              if (index < _latestArticles.length - 1)
                                const ArticleDivider(),
                            ],
                          );
                        }, childCount: _latestArticles.length),
                      ),
                  ],

                  // 底部间距
                  const SliverToBoxAdapter(child: SizedBox(height: 50)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建文章卡片，支持本地资源图片
  Widget _buildArticleCard(dynamic article, VoidCallback onTap) {
    // 直接使用HorizontalArticleCard，它现在是纯文本样式
    if (article is Article) {
      return HorizontalArticleCard(article: article, onTap: onTap);
    } else if (article is LocalArticle) {
      // 如果需要，可以为LocalArticle创建一个转换到Article的方法，或者确保其兼容Article
      // 这里假设LocalArticle的属性与Article兼容，或者已经处理了转换
      return HorizontalArticleCard(
        article: Article(
          id: article.id,
          title: article.title,
          subtitle: article.subtitle,
          content: article.content,
          publishDate: article.publishDate,
          thumbnailUrl: article.thumbnailUrl, // ThumbnailUrl仍保留，以防未来需要
          authorName: article.authorName,
          source: article.source,
          tags: article.tags,
          categoryIds: article.categoryIds,
          isFeatured: article.isFeatured,
          readTimeMinutes: article.readTimeMinutes,
        ),
        onTap: onTap,
      );
    } else {
      // 对于未知类型，可以返回一个占位符或者错误提示
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text('未知文章类型', style: TextStyle(color: Colors.red)),
      );
    }
  }

  Widget _buildCategorySelector() {
    return Container(
      height: 56, // 稍微增加高度
      margin: const EdgeInsets.only(top: 20, bottom: 8), // 调整上下间距
      child:
          _courseCategories.isEmpty
              ? Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                  ), // 增加水平padding
                  child: Text(
                    '您尚未加入任何学习，请先在"探索"中加入学习',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.5), // 更微妙的颜色
                      fontSize: 15, // 稍大的字号
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.1,
                    ),
                    textAlign: TextAlign.center, // 居中对齐
                  ),
                ),
              )
              : ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _courseCategories.length,
                padding: const EdgeInsets.symmetric(
                  horizontal: 14,
                ), // 调整水平padding
                itemBuilder: (context, index) {
                  final courseCategory = _courseCategories[index];
                  final isSelected = courseCategory.id == _selectedCategoryId;

                  return _buildCourseCategoryCard(
                    courseCategory: courseCategory,
                    isSelected: isSelected,
                    onTap: () => _selectCategory(courseCategory.id),
                  );
                },
              ),
    );
  }

  // 构建课程分类卡片 - Apple风格的选择按钮
  Widget _buildCourseCategoryCard({
    required CourseCategoryItem courseCategory,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 6), // 稍微调整间距
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          HapticFeedback.selectionClick();
          onTap();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 12,
          ), // 增大内边距
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Colors.white.withOpacity(0.15) // 选中状态的微妙背景
                    : Colors.white.withOpacity(0.05), // 未选中状态的极淡背景
            borderRadius: BorderRadius.circular(22), // 更大的圆角，Apple风格
            border: Border.all(
              color:
                  isSelected
                      ? Colors.white.withOpacity(0.3)
                      : Colors.white.withOpacity(0.1), // 微妙的边框
              width: 0.5,
            ),
          ),
          child: Text(
            courseCategory.name,
            style: TextStyle(
              color:
                  isSelected
                      ? Colors.white
                      : Colors.white.withOpacity(0.7), // 更微妙的颜色变化
              fontWeight:
                  isSelected ? FontWeight.w600 : FontWeight.w500, // Apple风格的字重
              fontSize: 14,
              letterSpacing: 0.1, // 微调字符间距
            ),
          ),
        ),
      ),
    );
  }

  // 静态方法，从课程信息导航到资讯库页面
  static void navigateFromCourse(
    BuildContext context,
    int courseId,
    String courseName,
  ) {
    // 添加轻微触觉反馈增强交互体验
    HapticFeedback.selectionClick();

    developer.log('从课程导航到资讯库: courseId=$courseId, courseName=$courseName');

    Navigator.of(context).push(
      AppPageTransitions.zeroTransitionRoute(
        page: InformationHubPage(courseId: courseId, courseName: courseName),
        maintainState: true,
        settings: const RouteSettings(name: 'InformationHubPage'),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    // 在这里添加清理代码
  }
}
