import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:io';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../constants/fonts.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../../services/preload_service.dart';
import '../../widgets/shimmer_loading.dart';
import '../../widgets/user_avatar_button.dart';
import '../../widgets/animated_avatar_widget.dart';
import '../../widgets/page_title_bar.dart';
import '../../widgets/gradient_course_card.dart';
import '../../utils/page_transitions.dart';
import '../../widgets/skeleton_quiz_page.dart';
import '../quiz/traditional_quiz_page.dart';
import '../auth/login_page.dart';
import 'main_page.dart';

class LearningPage extends StatefulWidget {
  const LearningPage({super.key});

  @override
  State<LearningPage> createState() => _LearningPageState();
}

class _LearningPageState extends State<LearningPage>
    with TickerProviderStateMixin {
  Future<List<Map<String, dynamic>>>? _userCourses;
  bool _isLoading = false;
  String? _errorMessage;

  // 当前选中的课程索引
  int _currentCourseIndex = 0;

  // 滑动控制器
  late PageController _pageController;

  // 我的课程卡片滑动控制器 - 在声明时直接初始化，避免LateInitializationError
  late PageController _courseCardController = PageController(
    viewportFraction: 0.85, // 使用0.85的视口比例，确保卡片居中显示
    initialPage: 0,
  );

  // 动画控制器（用于滑块动画）
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  // 滑动处理函数
  bool _isSliding = false;

  // 预加载服务实例
  final PreloadService _preloadService = PreloadService();

  @override
  void initState() {
    super.initState();

    // 初始化纹理
    NoiseTexture.initialize();

    // 初始化页面控制器，用于视差效果
    _pageController = PageController(
      viewportFraction: 0.85,
      initialPage: _currentCourseIndex,
    );

    // 已在声明时初始化了_courseCardController，这里不需要再次初始化

    // 给课程卡片控制器添加页面变化监听器
    _courseCardController.addListener(() {
      // 当页面滑动停止时更新当前选中的课程索引
      if (_courseCardController.position.isScrollingNotifier.value == false &&
          _courseCardController.page != null) {
        final int newIndex = _courseCardController.page!.round();
        if (newIndex != _currentCourseIndex) {
          setState(() {
            _currentCourseIndex = newIndex;
          });
          // 确保卡片精确对齐到整数位置
          _courseCardController.animateToPage(
            newIndex,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut,
          );
        }
      }
    });

    // 初始化滑块动画控制器
    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(1.5, 0),
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    // 滑块动画完成后的处理
    _slideController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 获取课程列表
        _userCourses!.then((courses) {
          if (courses.isEmpty) {
            _navigateToCourse(); // 没有课程时跳转到课程页面
          } else {
            _navigateToQuiz(); // 有课程时跳转到答题页面
          }
        });
      }
    });

    // 延迟加载，确保Provider可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCourses();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 检查用户课程数据是否发生变化 - 使用listen: true确保能监听到变化
    final userProvider = Provider.of<UserProvider>(context, listen: true);
    if (userProvider.courseChanged) {
      print("检测到课程数据变化，重新加载课程列表");
      // 重置变化标志
      userProvider.resetCourseChanged();
      // 重新加载课程
      _loadCourses();
    }
  }

  @override
  void didUpdateWidget(LearningPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当页面重新构建时，检查是否需要刷新数据
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (userProvider.courseChanged) {
      print("didUpdateWidget检测到课程数据变化，重新加载课程列表");
      userProvider.resetCourseChanged();
      _loadCourses();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _courseCardController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _loadCourses() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // 只有登录状态才加载课程
    if (userProvider.isLoggedIn) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _userCourses = fetchUserCourses();
      });
    }
  }

  Future<List<Map<String, dynamic>>> fetchUserCourses() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (!userProvider.isLoggedIn) {
      return []; // 未登录返回空列表
    }

    try {
      print(
        '获取用户课程: userId=${userProvider.userId}, token=${userProvider.token}',
      );

      final response = await http.get(
        Uri.parse(
          '${AuthService.baseUrl}/api/user_course/list/${userProvider.userId}',
        ),
        headers: {
          'Authorization': 'Bearer ${userProvider.token}',
          'Content-Type': 'application/json',
        },
      );

      print(
        '获取用户课程响应: statusCode=${response.statusCode}, body=${response.body}',
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        final List<Map<String, dynamic>> courses =
            data.cast<Map<String, dynamic>>();

        // 打印解析后的课程数据，帮助调试
        print('成功获取到${courses.length}个用户课程:');
        for (int i = 0; i < courses.length; i++) {
          final course = courses[i];
          print(
            '课程 $i: id=${course['course_id']}, 名称=${course['course_name']}, 类型: ${course['course_id'].runtimeType}',
          );
        }

        // 获取完课程数据后，预加载当前课程的数据
        if (courses.isNotEmpty && userProvider.token != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // 延迟执行，确保不影响UI渲染
            _preloadCurrentCourseData(courses, userProvider.token!);
          });
        }

        return courses;
      } else if (response.statusCode == 401) {
        // 401错误特殊处理
        print('授权令牌无效，准备清除登录状态');

        // 使用延迟执行，避免在build过程中更新状态
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            userProvider.clearUser(); // 清除用户登录状态

            // 使用Cupertino风格的提示，替代ScaffoldMessenger
            showCupertinoDialog(
              context: context,
              barrierDismissible: true,
              builder: (BuildContext context) {
                return CupertinoAlertDialog(
                  title: const Text('登录已过期'),
                  content: const Text('您的登录已过期，请重新登录'),
                  actions: [
                    CupertinoDialogAction(
                      child: const Text('确定'),
                      onPressed: () {
                        Navigator.of(context).pop();
                        // 可选：导航到登录页面
                        Navigator.of(context).push(
                          CupertinoPageRoute(
                            builder: (context) => const LoginPage(),
                          ),
                        );
                      },
                    ),
                  ],
                );
              },
            );
          }
        });

        setState(() {
          _errorMessage = '无效的授权令牌，请重新登录';
        });
        return [];
      } else {
        String errorMessage = '服务器返回错误: ${response.statusCode}';
        try {
          final Map<String, dynamic> errorData = jsonDecode(response.body);
          if (errorData.containsKey('message')) {
            errorMessage = errorData['message'];
          }
        } catch (e) {
          // 解析错误响应失败，使用默认错误信息
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      // 捕获异常并返回空列表，让界面显示备用视图
      setState(() {
        _errorMessage = '加载学习记录失败: $e';
      });
      return [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 预加载当前选中课程的数据
  void _preloadCurrentCourseData(
    List<Map<String, dynamic>> courses,
    String token,
  ) {
    if (courses.isEmpty) return;

    // 确保索引在有效范围内
    final currentIndex = math.min(_currentCourseIndex, courses.length - 1);
    final currentCourse = courses[currentIndex];
    final courseId = currentCourse['course_id'];

    if (courseId != null) {
      debugPrint('🔄 开始预加载课程数据: courseId=$courseId');

      // 预加载分类标签
      _preloadService.preloadCategoryTabs(courseId, token);

      // 预加载问题
      _preloadService.preloadQuestions(courseId, token);
    }
  }

  // 导航到AI答题页面
  void _navigateToAiQuizPage(int courseId, String courseName) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!mounted) return;

    // 确保用户已登录
    if (!userProvider.isLoggedIn || userProvider.token == null) {
      showCupertinoToast(context, "请先登录");
      return;
    }

    // 添加轻微触觉反馈增强交互体验
    HapticFeedback.selectionClick();

    print('👉 准备导航到传统答题页面: courseId=$courseId, courseName=$courseName');

    // 开始预加载课程数据
    try {
      // 并行执行预加载操作以提高效率
      Future.wait([
        _preloadService.preloadCategoryTabs(courseId, userProvider.token!),
        _preloadService.preloadQuestions(courseId, userProvider.token!),
      ]).catchError((e) {
        print('预加载数据错误: $e');
      });
    } catch (e) {
      print('预加载数据错误: $e');
    }

    // 使用无动画路由，立即显示答题页面并保持当前页面状态
    if (mounted) {
      print('👉 开始导航到传统答题页面');

      // 先检查并移除导航堆栈中已有的传统答题页面实例
      Navigator.of(
        context,
      ).popUntil((route) => route.settings.name != 'TraditionalQuizPage');

      // 然后再添加新的传统答题页面
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => TraditionalQuizPage(
                courseId: courseId,
                courseName: courseName,
              ),
          settings: const RouteSettings(name: 'TraditionalQuizPage'),
        ),
      );
    }
  }

  // 显示一个临时的Cupertino风格提示（类似Toast）
  void showCupertinoToast(BuildContext context, String message) {
    // 创建一个悬浮的半透明提示，模拟Toast效果
    final overlay = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: CupertinoColors.darkBackgroundGray.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  message,
                  style: const TextStyle(color: CupertinoColors.white),
                ),
              ),
            ),
          ),
    );

    // 显示提示
    Overlay.of(context).insert(overlay);

    // 短暂延迟后移除
    Future.delayed(const Duration(milliseconds: 800), () {
      overlay.remove();
    });
  }

  // 导航到课程页面
  void _navigateToCourse() {
    // 重置滑块位置
    _slideController.reset();
    _isSliding = false;

    // 跳转到课程页面，使用水平滑动动画
    Navigator.of(context).pushReplacement(
      AppPageTransitions.slideHorizontal(page: const MainPage(initialIndex: 1)),
    );
  }

  // 导航到答题页面
  void _navigateToQuiz() {
    if (_userCourses == null) return;

    _userCourses!.then((courses) {
      // 重置滑块位置
      _slideController.reset();
      _isSliding = false;

      if (courses.isEmpty || _currentCourseIndex >= courses.length) return;

      final selectedCourse = courses[_currentCourseIndex];

      // 确保course_id存在且为int类型
      if (!selectedCourse.containsKey('course_id')) {
        print('❌ 课程数据中没有course_id字段: $selectedCourse');
        // 安全地使用ScaffoldMessenger
        if (mounted &&
            context.findAncestorWidgetOfExactType<ScaffoldMessenger>() !=
                null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('课程数据错误，请重试'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final dynamic rawCourseId = selectedCourse['course_id'];
      final int courseId =
          rawCourseId is int
              ? rawCourseId
              : (rawCourseId is String ? int.tryParse(rawCourseId) ?? 0 : 0);

      if (courseId <= 0) {
        print('❌ 无效的course_id: $rawCourseId');
        // 安全地使用ScaffoldMessenger
        if (mounted &&
            context.findAncestorWidgetOfExactType<ScaffoldMessenger>() !=
                null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('课程ID无效，请重试'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final courseName = selectedCourse['course_name'] ?? '课程';

      print('✅ 导航到课程: id=$courseId, name=$courseName');
      _navigateToAiQuizPage(courseId, courseName);
    });
  }

  // 添加一个自定义的磨砂纹理Widget
  Widget _buildGradientContainer({required Widget child}) {
    return Container(
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment(0.9, -0.9), // 右上角
          radius: 0.6,
          colors: [Color(0xFFBB0000), Colors.transparent],
          stops: [0.0, 0.9],
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.black,
              const Color(0xFF151515),
              const Color(0xFF330000),
            ],
            stops: const [0.3, 0.7, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // 右下角的红色渐变
            Positioned(
              right: 0,
              bottom: 0,
              width: MediaQuery.of(context).size.width * 1.0,
              height: MediaQuery.of(context).size.height * 0.7,
              child: Container(
                decoration: const BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(0.6, 0.6),
                    radius: 1.0,
                    colors: [Color(0xCCCC0000), Colors.transparent],
                    stops: [0.0, 0.9],
                  ),
                ),
              ),
            ),
            // 内容层
            BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 1.0, sigmaY: 1.0),
              child: child,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotLoggedInView() {
    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.lock_outline, size: 64, color: Colors.white),
                  const SizedBox(height: 24),
                  Text(
                    AppLocalizations.of(context)!.pleaseLoginToLearn,
                    style: AppFonts.createMixedStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        AppPageTransitions.fadeTransition(
                          page: const LoginPage(),
                        ),
                      ).then((_) {
                        // 登录后重新加载
                        if (Provider.of<UserProvider>(
                          context,
                          listen: false,
                        ).isLoggedIn) {
                          _loadCourses();
                        }
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFAA0000),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.loginNow,
                      style: AppFonts.createMixedStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 添加用户头像按钮在右上角
            Positioned(top: 8, right: 16, child: UserAvatarButton(size: 36)),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage ?? "加载失败",
                    style: AppFonts.bodyStyle,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _loadCourses,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFAA0000),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.retry,
                      style: AppFonts.subheadingStyle,
                    ),
                  ),
                ],
              ),
            ),

            // 添加用户头像按钮在右上角
            Positioned(top: 8, right: 16, child: UserAvatarButton(size: 36)),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            const LearningPageShimmer(),

            // 添加用户头像按钮在右上角
            Positioned(top: 8, right: 16, child: UserAvatarButton(size: 36)),
          ],
        ),
      ),
    );
  }

  Widget _buildMainView(List<Map<String, dynamic>> courses) {
    if (courses.isEmpty) {
      return _buildGradientContainer(
        child: Stack(
          children: [
            // 主要内容
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题栏和用户头像
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 24.0,
                        vertical: 16.0,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.noCourses,
                            style: AppFonts.createMixedStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          // 用户头像按钮已被移除，因为使用了PageTitleBar
                        ],
                      ),
                    ),

                    Expanded(child: Container()), // 填充顶部空间
                    // 内容区域
                    Padding(
                      padding: const EdgeInsets.only(bottom: 24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题和提示信息
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24.0,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Icon(
                                  Icons.school_outlined,
                                  size: 64,
                                  color: Colors.white,
                                ),
                                const SizedBox(height: 15), // 从24减少到15
                                Text(
                                  AppLocalizations.of(context)!.noCourses,
                                  style: AppFonts.createMixedStyle(
                                    fontSize: 36,
                                    fontWeight: FontWeight.w900,
                                    color: Colors.white,
                                  ).copyWith(
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.5),
                                        offset: const Offset(0, 1),
                                        blurRadius: 1,
                                      ),
                                      Shadow(
                                        color: Colors.white.withOpacity(0.2),
                                        offset: const Offset(0, -0.5),
                                        blurRadius: 0,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 5), // 从10减少到5
                                SizedBox(
                                  width: MediaQuery.of(context).size.width - 48,
                                  child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      AppLocalizations.of(
                                        context,
                                      )!.selectCourseInterest,
                                      style: TextStyle(
                                        fontFamily:
                                            AppFonts.platformChineseFont,
                                        fontSize: 32,
                                        fontWeight: FontWeight.bold,
                                        foreground:
                                            Paint()
                                              ..shader = LinearGradient(
                                                colors: [
                                                  const Color(0xFFFF3333),
                                                  const Color(0xFF990000),
                                                ],
                                              ).createShader(
                                                const Rect.fromLTWH(
                                                  0.0,
                                                  0.0,
                                                  200.0,
                                                  70.0,
                                                ),
                                              ),
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withOpacity(
                                              0.3,
                                            ),
                                            offset: const Offset(1, 1),
                                            blurRadius: 2,
                                          ),
                                        ],
                                        fontFamilyFallback: [
                                          AppFonts.platformLatinFont,
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 15), // 从30减少到15
                          // 底部滑块区域（可选保留或删除）
                          Container(
                            margin: const EdgeInsets.only(
                              bottom: 80.0,
                            ), // 增加底部边距，为导航栏留出空间
                            padding: const EdgeInsets.all(12.0),
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(50),
                            ),
                            child: SlideTransition(
                              position: _slideAnimation,
                              child: GestureDetector(
                                onHorizontalDragUpdate: (details) {
                                  if (_isSliding) return;

                                  if (details.primaryDelta! > 0) {
                                    _isSliding = true;
                                    _slideController.forward();
                                  }
                                },
                                child: Row(
                                  children: [
                                    Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFAA0000),
                                        borderRadius: BorderRadius.circular(30),
                                        border: Border.all(
                                          color: const Color(0xFFAA0000),
                                          width: 3,
                                        ),
                                      ),
                                      child: const Icon(
                                        Icons.chevron_right,
                                        color: Colors.white,
                                        size: 32,
                                      ),
                                    ),
                                    const SizedBox(width: 20),
                                    Text(
                                      AppLocalizations.of(
                                        context,
                                      )!.goToCoursePage,
                                      style: AppFonts.createMixedStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // 当前选中的课程
    final currentCourse = courses[_currentCourseIndex];

    // 课程英文名称（如果不存在，则使用默认文本）
    final String englishName =
        currentCourse['english_name'] ??
        'Learning course ${_currentCourseIndex + 1}';

    return _buildGradientContainer(
      child: SafeArea(
        child: Stack(
          children: [
            // 主要内容
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: Container()), // 填充顶部空间
                  // 滑块区域
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 80.0,
                    ), // 增加底部边距，为导航栏留出空间
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 课程标题
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.course,
                                style: AppFonts.createMixedStyle(
                                  fontSize: 36,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ).copyWith(
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.5),
                                      offset: const Offset(0, 1),
                                      blurRadius: 1,
                                    ),
                                    Shadow(
                                      color: Colors.white.withOpacity(0.2),
                                      offset: const Offset(0, -0.5),
                                      blurRadius: 0,
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width:
                                    MediaQuery.of(context).size.width -
                                    48, // 左右各24的padding
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    currentCourse['course_name'] ?? 'Course',
                                    style: AppFonts.createMixedStyle(
                                      fontWeight: FontWeight.w900,
                                      fontSize: 36,
                                      color: Colors.white,
                                    ).copyWith(
                                      shadows: [
                                        Shadow(
                                          color: Colors.black.withOpacity(0.5),
                                          offset: const Offset(0, 1),
                                          blurRadius: 1,
                                        ),
                                        Shadow(
                                          color: Colors.white.withOpacity(0.2),
                                          offset: const Offset(0, -0.5),
                                          blurRadius: 0,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 5), // 从10减少到5
                              SizedBox(
                                width:
                                    MediaQuery.of(context).size.width -
                                    48, // 左右各24的padding
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  alignment: Alignment.centerLeft,
                                  child: Text.rich(
                                    TextSpan(
                                      text: englishName,
                                      style: TextStyle(
                                        fontFamily: AppFonts.platformLatinFont,
                                        fontSize: 32,
                                        fontWeight: FontWeight.w900,
                                        foreground:
                                            Paint()
                                              ..shader = LinearGradient(
                                                colors: [
                                                  const Color(0xFFFF3333),
                                                  const Color(0xFF990000),
                                                ],
                                              ).createShader(
                                                const Rect.fromLTWH(
                                                  0.0,
                                                  0.0,
                                                  200.0,
                                                  70.0,
                                                ),
                                              ),
                                        shadows: [
                                          Shadow(
                                            color: Colors.black.withOpacity(
                                              0.5,
                                            ),
                                            offset: const Offset(1, 1),
                                            blurRadius: 2,
                                          ),
                                          Shadow(
                                            color: Colors.white.withOpacity(
                                              0.1,
                                            ),
                                            offset: const Offset(-0.5, -0.5),
                                            blurRadius: 0,
                                          ),
                                        ],
                                        fontFamilyFallback: [
                                          AppFonts.platformChineseFont,
                                        ],
                                      ),
                                    ),
                                    style: null, // 移除额外的阴影样式
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 15), // 从30减少到15
                        // 开始学习按钮
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 24.0),
                          padding: const EdgeInsets.all(12.0),
                          decoration: BoxDecoration(
                            color: Colors.black54,
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: GestureDetector(
                              onHorizontalDragUpdate: (details) {
                                if (_isSliding) return;

                                if (details.primaryDelta! > 0) {
                                  _isSliding = true;
                                  _slideController.forward().then((_) {
                                    // 滑块动画完成后直接导航
                                    try {
                                      print("滑动完成，即将导航");

                                      // 确保组件仍然挂载
                                      if (!mounted) return;

                                      // 安全地获取courseId
                                      if (!currentCourse.containsKey(
                                        'course_id',
                                      )) {
                                        print(
                                          '❌ 课程数据中没有course_id字段: $currentCourse',
                                        );
                                        if (mounted) {
                                          showCupertinoToast(
                                            context,
                                            "课程数据错误，缺少ID字段",
                                          );
                                        }
                                        _isSliding = false;
                                        _slideController.reset();
                                        return;
                                      }

                                      final dynamic rawCourseId =
                                          currentCourse['course_id'];
                                      final int courseId =
                                          rawCourseId is int
                                              ? rawCourseId
                                              : (rawCourseId is String
                                                  ? int.tryParse(rawCourseId) ??
                                                      0
                                                  : 0);

                                      if (courseId <= 0) {
                                        print('❌ 无效的course_id: $rawCourseId');
                                        if (mounted) {
                                          showCupertinoToast(context, "课程ID无效");
                                        }
                                        _isSliding = false;
                                        _slideController.reset();
                                        return;
                                      }

                                      final courseName =
                                          currentCourse['course_name'] ?? '课程';

                                      print(
                                        "滑动完成，立即导航到课程: id=$courseId, name=$courseName",
                                      );

                                      // 延迟执行导航，避免在动画过程中的状态问题
                                      WidgetsBinding.instance
                                          .addPostFrameCallback((_) {
                                            if (mounted) {
                                              _navigateToAiQuizPage(
                                                courseId,
                                                courseName,
                                              );
                                            }
                                          });
                                    } catch (e) {
                                      print("导航过程出错: $e");
                                      // 显示错误消息
                                      if (mounted) {
                                        showCupertinoToast(context, "导航失败: $e");
                                      }
                                    } finally {
                                      // 重置滑块位置
                                      _slideController.reset();
                                      _isSliding = false;
                                    }
                                  });
                                }
                              },
                              child: Row(
                                children: [
                                  Container(
                                    width: 60,
                                    height: 60,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFAA0000),
                                      borderRadius: BorderRadius.circular(30),
                                      border: Border.all(
                                        color: const Color(0xFFAA0000),
                                        width: 3,
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.chevron_right,
                                      color: Colors.white,
                                      size: 32,
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  // 使用粗体效果展示"开始学习"文本
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: AppFonts.createBoldTextStack(
                                      AppLocalizations.of(
                                        context,
                                      )!.startLearning,
                                      fontSize: 18,
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      useCompactSpacing: false,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 添加顶部标题栏 - 包含标题和头像在同一行
            Positioned(
              top: 8,
              left: 0,
              right: 0,
              child: SafeArea(
                child: Column(
                  children: [
                    // 移除返回指示条
                    SizedBox(height: 16),
                    // 标题行
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 左侧标题部分
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.only(left: 20),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child:
                                  Platform.isIOS &&
                                          Localizations.localeOf(
                                                context,
                                              ).languageCode !=
                                              'zh'
                                      // 英文标题使用加强版超粗字体效果
                                      ? AppFonts.createBoldTextStack(
                                        AppLocalizations.of(
                                              context,
                                            )?.learningPageTitle ??
                                            "Learn",
                                        fontSize: 36,
                                        letterSpacing: -0.5, // 使用负值使字符更加紧凑
                                      )
                                      // 中文标题使用原有方法
                                      : Text(
                                        AppLocalizations.of(
                                              context,
                                            )?.learningPageTitle ??
                                            "学习",
                                        style: AppFonts.createTitleStyle(
                                          fontSize: 36,
                                          isLatinText:
                                              Localizations.localeOf(
                                                context,
                                              ).languageCode !=
                                              'zh',
                                        ),
                                      ),
                            ),
                          ),
                        ),

                        // 右侧头像按钮
                        Expanded(
                          flex: 1,
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 16),
                              child: AnimatedAvatarWidget(size: 36),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Apple Music 风格的横向滑动课程卡片区域
                    const SizedBox(height: 20),

                    // 横向滑动课程卡片 - 使用PageView替代ListView实现分页吸附效果
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0),
                      child: SizedBox(
                        height: 290, // 从260增加到290，容纳更大的卡片
                        width:
                            MediaQuery.of(context).size.width - 20.0, // 减去左边距
                        child:
                            courses.isEmpty
                                ? const Center(
                                  child: Text(
                                    "暂无课程",
                                    style: TextStyle(color: Colors.white54),
                                  ),
                                )
                                : PageView.builder(
                                  controller: _courseCardController,
                                  itemCount: courses.length,
                                  padEnds: false,
                                  physics: const PageScrollPhysics(),
                                  onPageChanged: (index) {
                                    // 更新当前课程索引并刷新UI
                                    setState(() {
                                      _currentCourseIndex = index;
                                    });
                                  },
                                  itemBuilder: (context, index) {
                                    final course = courses[index];
                                    final description = course['description'];
                                    final courseName =
                                        course['course_name'] ?? 'Python 入门实战课';
                                    final courseTag =
                                        course['tag'] ?? "LEARNING PROGRESS";
                                    final courseSubtitle =
                                        course['subtitle'] ??
                                        "WakeUp Skill Boosting";

                                    // 调整卡片padding，使所有卡片位置统一
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                        right: 20.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          // 文字区域
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              // 使用粗体效果展示课程标签
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child:
                                                    AppFonts.createBoldTextStack(
                                                      courseTag.toUpperCase(),
                                                      fontSize: 12,
                                                      color: const Color(
                                                        0xFF8D8C92,
                                                      ),
                                                      letterSpacing: 0.0,
                                                      height: 1.2, // 设置行高为1.2倍
                                                      useCompactSpacing: false,
                                                    ),
                                              ),
                                              const SizedBox(height: 2),
                                              Text(
                                                courseName,
                                                style:
                                                    AppFonts.createMixedStyle(
                                                      fontWeight:
                                                          FontWeight.w900,
                                                      fontSize: 18,
                                                      color: Colors.white,
                                                      height: 1.2, // 设置行高为1.2倍
                                                    ).copyWith(
                                                      shadows: [
                                                        Shadow(
                                                          color: Colors.black
                                                              .withOpacity(0.5),
                                                          offset: const Offset(
                                                            0,
                                                            1,
                                                          ),
                                                          blurRadius: 1,
                                                        ),
                                                        Shadow(
                                                          color: Colors.white
                                                              .withOpacity(0.2),
                                                          offset: const Offset(
                                                            0,
                                                            -0.5,
                                                          ),
                                                          blurRadius: 0,
                                                        ),
                                                      ],
                                                    ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 2),
                                              // 第三行文本，加粗并修改颜色
                                              SizedBox(
                                                width: double.infinity,
                                                child: Align(
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child:
                                                      AppFonts.createBoldTextStack(
                                                        courseSubtitle,
                                                        fontSize: 14,
                                                        color: const Color(
                                                          0xFF8D8C92,
                                                        ),
                                                        letterSpacing: 0.0,
                                                        height:
                                                            1.2, // 设置行高为1.2倍
                                                        useCompactSpacing:
                                                            false,
                                                      ),
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 8,
                                              ), // 设置文本区域与下方卡片的间距为8pt
                                            ],
                                          ),

                                          // 课程卡片部分 - 使用新的渐变卡片组件
                                          Hero(
                                            tag: 'quiz_card_learn_${course['course_id']}_$index',
                                            child: GradientCourseCard(
                                              courseName: courseName,
                                              description: description,
                                              categoryId: course['course_id'] is int
                                                  ? course['course_id']
                                                  : int.tryParse(
                                                        course['course_id'].toString(),
                                                      ),
                                              height: 190,
                                              borderRadius: 24,
                                              onTap: () {
                                                final courseId =
                                                    course['course_id'] is int
                                                        ? course['course_id']
                                                        : int.tryParse(
                                                              course['course_id']
                                                                  .toString(),
                                                            ) ??
                                                            0;
                                                if (courseId > 0) {
                                                  _navigateToAiQuizPage(
                                                    courseId,
                                                    courseName,
                                                  );
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建学习选项
  Widget _buildLearningOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.red.shade800,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.white),
      ),
      title: Text(
        title,
        style: AppFonts.createMixedStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppFonts.createMixedStyle(
          color: Colors.grey.shade400,
          fontSize: 12,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: Colors.white,
        size: 16,
      ),
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);

    return Scaffold(
      body:
          userProvider.isLoggedIn
              ? (_isLoading
                  ? _buildLoadingView()
                  : _errorMessage != null
                  ? _buildErrorView()
                  : _userCourses == null
                  ? _buildLoadingView() // 防止未初始化的情况
                  : FutureBuilder<List<Map<String, dynamic>>>(
                    future: _userCourses,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return _buildLoadingView();
                      }
                      if (snapshot.hasError) {
                        return _buildErrorView();
                      }

                      final List<Map<String, dynamic>> data =
                          snapshot.data ?? [];
                      return _buildMainView(data);
                    },
                  ))
              : _buildNotLoggedInView(),
    );
  }
}

// 噪点纹理生成类
class NoiseTexture {
  static late ui.Image noiseTexture;

  static Future<void> initialize() async {
    // 创建一个简单的1x1透明图像，不再生成噪点
    final size = 1;
    final pixels = Uint8List(size * size * 4);
    // 设置为完全透明
    pixels[0] = 0;
    pixels[1] = 0;
    pixels[2] = 0;
    pixels[3] = 0;

    final completer = Completer<ui.Image>();
    ui.decodeImageFromPixels(
      pixels,
      size,
      size,
      ui.PixelFormat.rgba8888,
      completer.complete,
    );
    noiseTexture = await completer.future;
  }
}

// 移除噪点纹理效果，只返回子组件
class GrainOverlay extends StatelessWidget {
  final Widget child;
  final double opacity;

  const GrainOverlay({super.key, required this.child, this.opacity = 0.15});

  @override
  Widget build(BuildContext context) {
    // 直接返回子组件，不再添加噪点纹理
    return child;
  }
}

// 添加自定义的页面切换路由
class SlideUpPageRoute extends PageRouteBuilder {
  final Widget page;

  SlideUpPageRoute({required this.page})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // 定义更加丝滑的曲线
          var curve = Curves.easeOutCubic;

          // 滑动动画: 从底部向上
          var slideAnimation = Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(parent: animation, curve: curve));

          // 缩放动画: 从略小到正常大小
          var scaleAnimation = Tween<double>(
            begin: 0.95,
            end: 1.0,
          ).animate(CurvedAnimation(parent: animation, curve: curve));

          // 透明度动画
          var fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: animation,
              curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
            ),
          );

          // 组合多个动画效果
          return FadeTransition(
            opacity: fadeAnimation,
            child: SlideTransition(
              position: slideAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    20 * (1 - animation.value),
                  ), // 动态调整圆角
                  child: child,
                ),
              ),
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 450),
        reverseTransitionDuration: const Duration(milliseconds: 350),
        opaque: false,
        barrierColor: Colors.black12,
        barrierDismissible: true,
      );
}
