import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../../utils/page_transitions.dart';
import '../course/course_page.dart';
import 'learning_page.dart';
import 'profile_page.dart';
import '../auth/login_page.dart';
import '../../providers/api_cache_provider.dart';
import '../../widgets/glass_bottom_navigation_bar.dart';
import '../../widgets/auth_wrapper.dart';
import '../knowledge_card/knowledge_card_page.dart';
import '../resources/resource_library_page.dart';
import '../test_glass_card.dart';

class MainPage extends StatefulWidget {
  final int initialIndex;

  const MainPage({super.key, this.initialIndex = 0});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  late int _currentIndex;
  bool _isLoggedIn = false;

  // 数据加载状态
  bool _isLoadingPrimaryData = true;
  bool _isLoadingSecondaryData = true;
  bool _isBackgroundLoadingComplete = false;
  String _errorMessage = '';

  // 数据存储
  Map<String, dynamic> _profileData = {};
  List<Map<String, dynamic>> _coursesData = [];
  List<Map<String, dynamic>> _recommendationsData = [];

  // 添加一个key来强制重建LearningPage
  Key _learningPageKey = UniqueKey();

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex.clamp(0, 3); // 限制索引范围为0-3
    _checkLoginState();
    _loadDataInBackground();
  }

  @override
  void dispose() {
    // 确保在组件销毁时清理任何未完成的异步操作
    // 注意：Future本身不能被取消，但我们通过mounted检查来避免setState调用
    super.dispose();
  }

  // 后台数据加载
  void _loadDataInBackground() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final apiCacheProvider = Provider.of<ApiCacheProvider>(
      context,
      listen: false,
    );

    if (!userProvider.isLoggedIn) {
      if (mounted) {
        setState(() {
          _isLoadingPrimaryData = false;
          _isLoadingSecondaryData = false;
          _isBackgroundLoadingComplete = true;
        });
      }
      return;
    }

    Map<String, String> headers = {
      'Authorization': 'Bearer ${userProvider.token}',
    };

    try {
      // 加载高优先级数据
      await _loadPrimaryData(apiCacheProvider, headers);

      // 加载低优先级数据
      await _loadSecondaryData(apiCacheProvider, headers);

      if (mounted) {
        setState(() {
          _isBackgroundLoadingComplete = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoadingPrimaryData = false;
          _isLoadingSecondaryData = false;
          _isBackgroundLoadingComplete = true;
        });
      }
    }
  }

  // 加载高优先级数据（用户资料、课程）
  Future<void> _loadPrimaryData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      // 并行加载用户资料和课程数据
      final futures = await Future.wait([
        apiCacheProvider.getWithCache(
          '${AuthService.baseUrl}/api/profile',
          headers: headers,
          useMemoryCache: true,
        ),
        apiCacheProvider.getWithCache(
          '${AuthService.baseUrl}/api/courses',
          headers: headers,
          useMemoryCache: true,
        ),
      ]);

      if (mounted) {
        setState(() {
          // API直接返回数据，不是包装在data字段中
          if (futures[0] is Map<String, dynamic>) {
            _profileData = Map<String, dynamic>.from(futures[0]);
          } else {
            _profileData = {};
          }
          // API直接返回数据，不是包装在data字段中
          if (futures[1] is List) {
            _coursesData = List<Map<String, dynamic>>.from(futures[1]);
          } else {
            _coursesData = [];
          }
          _isLoadingPrimaryData = false;
        });
      }
    } catch (e) {
      print('主要数据加载错误: $e');
      if (mounted) {
        setState(() {
          _isLoadingPrimaryData = false;
        });
      }
    }
  }

  // 加载低优先级数据（推荐）
  Future<void> _loadSecondaryData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      final recommendations = await apiCacheProvider.getWithCache(
        '${AuthService.baseUrl}/api/recommendations',
        headers: headers,
        useMemoryCache: true,
      );

      if (mounted) {
        setState(() {
          // API直接返回数据，不是包装在data字段中
          if (recommendations is List) {
            _recommendationsData = List<Map<String, dynamic>>.from(recommendations);
          } else {
            _recommendationsData = [];
          }
          _isLoadingSecondaryData = false;
        });
      }
    } catch (e) {
      print('推荐数据加载错误: $e');
      if (mounted) {
        setState(() {
          _isLoadingSecondaryData = false;
        });
      }
    }
  }

  // 刷新所有数据
  Future<void> _refreshData() async {
    final apiCacheProvider = Provider.of<ApiCacheProvider>(
      context,
      listen: false,
    );
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    Map<String, String> headers = {};
    if (userProvider.isLoggedIn) {
      headers['Authorization'] = 'Bearer ${userProvider.token}';
    }

    // 强制刷新所有缓存数据
    if (mounted) {
      setState(() {
        _isLoadingPrimaryData = true;
        _isLoadingSecondaryData = true;
        _isBackgroundLoadingComplete = false;
        _errorMessage = '';
      });
    }

    // 清除缓存强制刷新
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/profile');
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/courses');
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/recommendations');

    // 重新加载数据
    _loadDataInBackground();
  }

  // 检查登录状态
  void _checkLoginState() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (mounted) {
      setState(() {
        _isLoggedIn = userProvider.isLoggedIn;
      });
    }
  }

  // 导航到指定页面
  void _navigateToPage(int index) {
    // 如果点击的是学习页面(index=0)，总是强制刷新数据
    if (index == 0) {
      print("点击学习页面，强制刷新课程数据");
      // 生成新的key来强制重建LearningPage
      if (mounted) {
        setState(() {
          _learningPageKey = UniqueKey();
          _currentIndex = index;
        });
      }

      // 同时重置UserProvider中的课程变化标志
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (userProvider.courseChanged) {
        userProvider.resetCourseChanged();
      }
      return;
    }

    if (mounted) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  // 使用getter来动态生成页面列表，确保LearningPage使用最新的key
  List<Widget> get _pages => [
    LearningPage(key: _learningPageKey),
    CoursePage(),
    KnowledgeCardPage(),
    ResourceLibraryPage(),
  ];

  @override
  Widget build(BuildContext context) {
    // 检测当前主题模式
    final isDarkMode =
        MediaQuery.platformBrightnessOf(context) == Brightness.dark;

    // 使用 AuthWrapper 包装整个页面
    return AuthWrapper(
      child: Scaffold(
        body: Stack(
          children: [
            // 主要内容区域
            IndexedStack(index: _currentIndex, children: _pages),

            // 测试按钮
            Positioned(
              top: 100,
              right: 20,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const TestGlassCardPage(),
                    ),
                  );
                },
                backgroundColor: const Color(0xFFE91E63),
                child: const Icon(Icons.visibility, color: Colors.white),
              ),
            ),

            // 底部导航栏作为悬浮元素
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              // 添加zIndex确保导航栏始终在最上层
              child: Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -1),
                    ),
                  ],
                ),
                child: GlassBottomNavigationBar(
                  items: [
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.bubble_left_bubble_right),
                      activeIcon: const Icon(
                        CupertinoIcons.bubble_left_bubble_right_fill,
                      ),
                      label: AppLocalizations.of(context)!.navLearning,
                    ),
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.square_list),
                      activeIcon: const Icon(CupertinoIcons.square_list_fill),
                      label: AppLocalizations.of(context)!.navCourses,
                    ),
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.collections),
                      activeIcon: const Icon(CupertinoIcons.collections_solid),
                      label: AppLocalizations.of(context)!.navKnowledgeCard,
                    ),
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.folder),
                      activeIcon: const Icon(CupertinoIcons.folder_fill),
                      label: AppLocalizations.of(context)!.navResources,
                    ),
                  ],
                  currentIndex: _currentIndex,
                  onTap: _navigateToPage,
                  blur: 10.0,
                  opacity: 0.3,
                  darkMode: true,
                  height: 52.0,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
