import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../../services/user_service.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({super.key});

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  bool _isLoading = true;
  bool _isSaving = false;
  File? _selectedImage;
  Map<String, dynamic>? _userInfo;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (!userProvider.isLoggedIn) {
        Navigator.of(context).pop();
        return;
      }

      final info = await AuthService.getUserInfo(
        userProvider.userId,
        userProvider.token ?? '',
      );

      setState(() {
        _userInfo = info;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('获取用户信息失败: $e');
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    showCupertinoModalPopup(
      context: context,
      builder:
          (BuildContext context) => CupertinoActionSheet(
            title: const Text('选择头像'),
            actions: <CupertinoActionSheetAction>[
              CupertinoActionSheetAction(
                onPressed: () async {
                  Navigator.pop(context);
                  final pickedFile = await picker.pickImage(
                    source: ImageSource.camera,
                  );
                  if (pickedFile != null) {
                    setState(() {
                      _selectedImage = File(pickedFile.path);
                    });
                  }
                },
                child: const Text('拍照'),
              ),
              CupertinoActionSheetAction(
                onPressed: () async {
                  Navigator.pop(context);
                  final pickedFile = await picker.pickImage(
                    source: ImageSource.gallery,
                  );
                  if (pickedFile != null) {
                    setState(() {
                      _selectedImage = File(pickedFile.path);
                    });
                  }
                },
                child: const Text('从相册选择'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
          ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _editNickname() {
    final TextEditingController controller = TextEditingController();
    controller.text = _userInfo?['nickname'] ?? '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2C2C2E),
          title: const Text('编辑昵称', style: TextStyle(color: Colors.white)),
          content: TextField(
            controller: controller,
            style: const TextStyle(color: Colors.white),
            decoration: const InputDecoration(
              hintText: '请输入昵称',
              hintStyle: TextStyle(color: Colors.grey),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.blue),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () async {
                final nickname = controller.text.trim();
                if (nickname.isNotEmpty) {
                  await _updateUserInfo({'nickname': nickname});
                  Navigator.pop(context);
                }
              },
              child: const Text('确定', style: TextStyle(color: Colors.blue)),
            ),
          ],
        );
      },
    );
  }

  void _editUsername() {
    final TextEditingController controller = TextEditingController();
    controller.text = _userInfo?['username'] ?? '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2C2C2E),
          title: const Text('编辑用户名', style: TextStyle(color: Colors.white)),
          content: TextField(
            controller: controller,
            style: const TextStyle(color: Colors.white),
            decoration: const InputDecoration(
              hintText: '请输入用户名',
              hintStyle: TextStyle(color: Colors.grey),
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.grey),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.blue),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () async {
                final username = controller.text.trim();
                if (username.isNotEmpty) {
                  await _updateUserInfo({'username': username});
                  Navigator.pop(context);
                }
              },
              child: const Text('确定', style: TextStyle(color: Colors.blue)),
            ),
          ],
        );
      },
    );
  }

  void _selectGender() {
    showCupertinoModalPopup(
      context: context,
      builder:
          (BuildContext context) => CupertinoActionSheet(
            title: const Text('选择性别'),
            actions: <CupertinoActionSheetAction>[
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _updateUserInfo({'gender': '男'});
                },
                child: const Text('男'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _updateUserInfo({'gender': '女'});
                },
                child: const Text('女'),
              ),
              CupertinoActionSheetAction(
                onPressed: () {
                  Navigator.pop(context);
                  _updateUserInfo({'gender': '其他'});
                },
                child: const Text('其他'),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
          ),
    );
  }

  void _selectRegion() {
    final regions = [
      '中国大陆',
      '中国香港',
      '中国台湾',
      '美国',
      '英国',
      '日本',
      '韩国',
      '新加坡',
      '马来西亚',
      '澳大利亚',
      '其他',
    ];

    showCupertinoModalPopup(
      context: context,
      builder:
          (BuildContext context) => Container(
            height: 300,
            color: const Color(0xFF2C2C2E),
            child: Column(
              children: [
                Container(
                  height: 44,
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey, width: 0.5),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text(
                          '取消',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                      const Text(
                        '选择地区',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text(
                          '确定',
                          style: TextStyle(color: Colors.blue),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: CupertinoPicker(
                    backgroundColor: const Color(0xFF2C2C2E),
                    itemExtent: 40,
                    onSelectedItemChanged: (int index) {
                      _updateUserInfo({'region': regions[index]});
                    },
                    children:
                        regions.map((String region) {
                          return Center(
                            child: Text(
                              region,
                              style: const TextStyle(color: Colors.white),
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Future<void> _updateUserInfo(Map<String, dynamic> updateData) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final success = await UserService().updateUserInfo(updateData);

      if (success) {
        // 更新本地数据
        setState(() {
          _userInfo = {...?_userInfo, ...updateData};
        });

        // 更新UserProvider
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        if (updateData.containsKey('nickname')) {
          userProvider.setNickname(updateData['nickname']);
        }

        _showSnackBar('更新成功');
      } else {
        _showSnackBar('更新失败');
      }
    } catch (e) {
      _showSnackBar('更新失败: $e');
    }

    setState(() {
      _isSaving = false;
    });
  }

  Widget _buildProfileItem({
    required String label,
    required String value,
    required VoidCallback onTap,
    Widget? trailing,
    bool showAvatar = false,
  }) {
    return Container(
      color: const Color(0xFF1C1C1E),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showAvatar) ...[
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey.shade600,
                backgroundImage:
                    _selectedImage != null
                        ? FileImage(_selectedImage!)
                        : (_userInfo?['avatarUrl'] != null
                                ? NetworkImage(
                                  _userInfo!['avatarUrl'] as String,
                                )
                                : null)
                            as ImageProvider?,
                child:
                    (_selectedImage == null && _userInfo?['avatarUrl'] == null)
                        ? const Icon(
                          Icons.person,
                          size: 24,
                          color: Colors.white,
                        )
                        : null,
              ),
              const SizedBox(width: 12),
            ],
            if (!showAvatar) ...[
              Flexible(
                child: Text(
                  value,
                  style: TextStyle(color: Colors.grey.shade400, fontSize: 16),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
            ],
            trailing ??
                const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildSectionDivider() {
    return Container(height: 20, color: const Color(0xFF000000));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF000000),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1C1C1E),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '个人资料',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body:
          _isLoading
              ? const Center(
                child: CupertinoActivityIndicator(
                  radius: 20,
                  color: Colors.white,
                ),
              )
              : SingleChildScrollView(
                child: Column(
                  children: [
                    // 头像部分
                    _buildProfileItem(
                      label: '头像',
                      value: '',
                      showAvatar: true,
                      onTap: _pickImage,
                    ),

                    _buildSectionDivider(),

                    // 基本信息部分
                    _buildProfileItem(
                      label: '昵称',
                      value: _userInfo?['nickname'] ?? '未设置',
                      onTap: _editNickname,
                    ),

                    Container(
                      height: 0.5,
                      color: Colors.grey.shade800,
                      margin: const EdgeInsets.only(left: 16),
                    ),

                    _buildProfileItem(
                      label: '用户名',
                      value: _userInfo?['username'] ?? '未设置',
                      onTap: _editUsername,
                    ),

                    Container(
                      height: 0.5,
                      color: Colors.grey.shade800,
                      margin: const EdgeInsets.only(left: 16),
                    ),

                    _buildProfileItem(
                      label: '性别',
                      value: _userInfo?['gender'] ?? '未设置',
                      onTap: _selectGender,
                    ),

                    Container(
                      height: 0.5,
                      color: Colors.grey.shade800,
                      margin: const EdgeInsets.only(left: 16),
                    ),

                    _buildProfileItem(
                      label: '地区',
                      value: _userInfo?['region'] ?? '未设置',
                      onTap: _selectRegion,
                    ),

                    Container(
                      height: 0.5,
                      color: Colors.grey.shade800,
                      margin: const EdgeInsets.only(left: 16),
                    ),

                    _buildProfileItem(
                      label: '手机号',
                      value: _userInfo?['phone'] ?? '未绑定',
                      onTap: () {
                        // TODO: 实现手机号修改功能
                        _showSnackBar('手机号修改功能待实现');
                      },
                    ),

                    _buildSectionDivider(),

                    // 其他功能部分
                    _buildProfileItem(
                      label: '我的二维码',
                      value: '',
                      onTap: () {
                        _showSnackBar('二维码功能待实现');
                      },
                      trailing: const Icon(
                        Icons.qr_code,
                        color: Colors.grey,
                        size: 20,
                      ),
                    ),

                    Container(
                      height: 0.5,
                      color: Colors.grey.shade800,
                      margin: const EdgeInsets.only(left: 16),
                    ),

                    _buildProfileItem(
                      label: '个性签名',
                      value: '保持真诚，但不会开心',
                      onTap: () {
                        _showSnackBar('个性签名功能待实现');
                      },
                    ),

                    _buildSectionDivider(),

                    // 空白区域
                    const SizedBox(height: 40),
                  ],
                ),
              ),
    );
  }
}
