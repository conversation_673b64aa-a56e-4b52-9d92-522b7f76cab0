import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/api_cache_provider.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../../core/services/unified_api_service.dart';
import '../../core/utils/logger.dart';
import '../../shared/widgets/common_widgets.dart';
import 'dart:async';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // 主要数据状态
  bool _isLoadingHeader = true;
  bool _isLoadingMainContent = true;
  // 次要数据状态
  bool _isLoadingRecommendations = true;
  bool _isLoadingActivities = true;

  // 数据
  Map<String, dynamic> _headerData = {};
  List<Map<String, dynamic>> _mainContentData = [];
  List<Map<String, dynamic>> _recommendationsData = [];
  List<Map<String, dynamic>> _activitiesData = [];

  // 错误信息
  String _headerError = '';
  String _mainContentError = '';
  String _recommendationsError = '';
  String _activitiesError = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  // 加载所有数据
  Future<void> _loadAllData() async {
    final apiCacheProvider = Provider.of<ApiCacheProvider>(
      context,
      listen: false,
    );
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    final Map<String, String> headers = {};
    if (userProvider.isLoggedIn) {
      headers['Authorization'] = 'Bearer ${userProvider.token}';
    }

    // 优先加载关键数据（并发）
    _loadPrimaryData(apiCacheProvider, headers);

    // 延迟50毫秒后加载次要数据（并发）
    // 这样可以优先处理关键数据的网络请求
    Future.delayed(const Duration(milliseconds: 50), () {
      _loadSecondaryData(apiCacheProvider, headers);
    });
  }

  // 加载主要数据（页面标题和主内容）
  Future<void> _loadPrimaryData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    // 并发加载主要数据
    await Future.wait([
      _loadHeaderData(apiCacheProvider, headers),
      _loadMainContent(apiCacheProvider, headers),
    ]);
  }

  // 加载次要数据（推荐和活动）
  Future<void> _loadSecondaryData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    // 并发加载次要数据
    await Future.wait([
      _loadRecommendations(apiCacheProvider, headers),
      _loadActivities(apiCacheProvider, headers),
    ]);
  }

  // 加载页面标题数据
  Future<void> _loadHeaderData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      final response = await UnifiedApiService().getHomeHeader(context);
      final data = response.isSuccess ? response.data : null;

      setState(() {
        _headerData = data ?? {};
        _isLoadingHeader = false;
      });
    } catch (e) {
      setState(() {
        _headerError = e.toString();
        _isLoadingHeader = false;
      });
    }
  }

  // 加载主要内容
  Future<void> _loadMainContent(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      final response = await UnifiedApiService().getHomeContent(context);
      final data = response.isSuccess ? response.data : [];

      setState(() {
        _mainContentData = List<Map<String, dynamic>>.from(data ?? []);
        _isLoadingMainContent = false;
      });
    } catch (e) {
      setState(() {
        _mainContentError = e.toString();
        _isLoadingMainContent = false;
      });
    }
  }

  // 加载推荐内容（次要）
  Future<void> _loadRecommendations(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      final response = await UnifiedApiService().getRecommendations(context);
      final data = response.isSuccess ? response.data : [];

      setState(() {
        _recommendationsData = List<Map<String, dynamic>>.from(data ?? []);
        _isLoadingRecommendations = false;
      });
    } catch (e) {
      setState(() {
        _recommendationsError = e.toString();
        _isLoadingRecommendations = false;
      });
    }
  }

  // 加载活动内容（次要）
  Future<void> _loadActivities(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      final data = await apiCacheProvider.getWithCache(
        '${AuthService.baseUrl}/api/activities',
        headers: headers,
        useMemoryCache: true,
      );

      setState(() {
        _activitiesData = List<Map<String, dynamic>>.from(data);
        _isLoadingActivities = false;
      });
    } catch (e) {
      setState(() {
        _activitiesError = e.toString();
        _isLoadingActivities = false;
      });
    }
  }

  // 刷新所有数据
  Future<void> _refreshData() async {
    setState(() {
      _isLoadingHeader = true;
      _isLoadingMainContent = true;
      _isLoadingRecommendations = true;
      _isLoadingActivities = true;

      _headerError = '';
      _mainContentError = '';
      _recommendationsError = '';
      _activitiesError = '';
    });

    final apiCacheProvider = Provider.of<ApiCacheProvider>(
      context,
      listen: false,
    );
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // 清除缓存
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/home/<USER>');
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/home/<USER>');
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/recommendations');
    apiCacheProvider.clearCache('${AuthService.baseUrl}/api/activities');

    // 重新加载数据
    _loadAllData();

    // 通知刷新完成
    return Future.value();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('首页', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.black,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _refreshData),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 1. 头部内容 - 高优先级渲染
              _buildHeader(),

              // 2. 主要内容 - 高优先级渲染
              _buildMainContent(),

              // 空间分隔
              const SizedBox(height: 20),

              // 3. 推荐内容 - 低优先级渲染
              _buildRecommendations(),

              // 4. 活动内容 - 低优先级渲染
              _buildActivities(),

              // 底部间距
              const SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }

  // 构建页面标题（高优先级）
  Widget _buildHeader() {
    if (_isLoadingHeader) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: SizedBox(height: 80, child: CircularProgressIndicator()),
        ),
      );
    }

    if (_headerError.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          '加载标题失败: $_headerError',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    // 从后端获取的数据
    final title = _headerData['title'] ?? '欢迎使用WAKEUP';
    final subtitle = _headerData['subtitle'] ?? '开始您的学习之旅';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      color: Colors.black,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: const TextStyle(fontSize: 16, color: Colors.white70),
          ),
        ],
      ),
    );
  }

  // 构建主要内容（高优先级）
  Widget _buildMainContent() {
    if (_isLoadingMainContent) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (_mainContentError.isNotEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          '加载内容失败: $_mainContentError',
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (_mainContentData.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Text('暂无内容', style: TextStyle(color: Colors.white70)),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _mainContentData.length,
      itemBuilder: (context, index) {
        final item = _mainContentData[index];
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item['title'] ?? '标题',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                item['description'] ?? '描述',
                style: const TextStyle(color: Colors.white70),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建推荐内容（低优先级）
  Widget _buildRecommendations() {
    if (_isLoadingRecommendations) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: LinearProgressIndicator()),
      );
    }

    // 即使加载失败，也不会显示错误，而是静默失败
    // 这样主要内容不受影响
    if (_recommendationsError.isNotEmpty || _recommendationsData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            '推荐课程',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _recommendationsData.length,
            itemBuilder: (context, index) {
              final item = _recommendationsData[index];
              return Container(
                width: 160,
                margin: EdgeInsets.only(
                  left: index == 0 ? 16 : 8,
                  right: index == _recommendationsData.length - 1 ? 16 : 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[850],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      child: Container(
                        height: 100,
                        color: Colors.grey[800],
                        // 这里可以放置图片
                        child: Center(
                          child: Text(
                            item['imageLabel'] ?? 'Course',
                            style: const TextStyle(color: Colors.white70),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item['title'] ?? 'Title',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            item['description'] ?? 'Description',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white70,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建活动内容（低优先级）
  Widget _buildActivities() {
    if (_isLoadingActivities) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: LinearProgressIndicator()),
      );
    }

    // 即使加载失败，也不会显示错误，而是静默失败
    if (_activitiesError.isNotEmpty || _activitiesData.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            '最新活动',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _activitiesData.length > 3 ? 3 : _activitiesData.length,
          itemBuilder: (context, index) {
            final item = _activitiesData[index];
            return Container(
              margin: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 8.0,
              ),
              padding: const EdgeInsets.all(12.0),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[800]!),
              ),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[800],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        item['icon'] ?? 'A',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['title'] ?? 'Activity',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          item['time'] ?? 'Time',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white54,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(Icons.chevron_right, color: Colors.grey[600]),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}
