import 'package:http/http.dart' as http;
import 'dart:convert';
import '../utils/api_utils.dart';
import 'auth_service.dart';
import 'package:flutter/material.dart';

/// API服务基类
class ApiService {
  /// 保存context引用
  BuildContext? _context;

  /// 设置context
  void setContext(BuildContext context) {
    _context = context;
  }

  /// 获取请求头
  Map<String, String> getHeaders({String? token}) {
    Map<String, String> headers = {'Content-Type': 'application/json'};

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// 执行GET请求
  Future<dynamic> get(
    String endpoint, {
    String? token,
    Map<String, dynamic>? queryParams,
    bool expectList = true,
    BuildContext? context,
  }) async {
    try {
      final Uri uri = Uri.parse(
        '${AuthService.baseUrl}$endpoint',
      ).replace(queryParameters: queryParams);

      final response = await _client.get(
        uri,
        headers: getHeaders(token: token),
      );

      return ApiUtils.parseJsonResponse(
        response,
        expectList: expectList,
        context: context ?? _context,
      );
    } catch (e) {
      print('GET请求异常: $e');
      return expectList ? [] : {'error': e.toString()};
    }
  }

  /// 执行POST请求
  Future<dynamic> post(
    String endpoint, {
    String? token,
    Map<String, dynamic>? body,
    bool expectList = false,
    BuildContext? context,
  }) async {
    try {
      final response = await _client.post(
        Uri.parse('${AuthService.baseUrl}$endpoint'),
        headers: getHeaders(token: token),
        body: body != null ? json.encode(body) : null,
      );

      return ApiUtils.parseJsonResponse(
        response,
        expectList: expectList,
        context: context ?? _context,
      );
    } catch (e) {
      print('POST请求异常: $e');
      return expectList ? [] : {'error': e.toString()};
    }
  }

  /// HTTP客户端
  final http.Client _client = http.Client();

  /// 关闭客户端
  void dispose() {
    _client.close();
  }

  /// 当API不可用时获取模拟课程数据
  static List<Map<String, dynamic>> getMockCourseData() {
    return [
      {
        'category': '计算机科学',
        'id': 1,
        'courses': [
          {
            'id': 1,
            'name': 'Python基础',
            'description': '零基础入门Python编程，掌握基本语法和编程思维',
            'highlight': '最受欢迎的入门编程语言',
            'english_name': 'Python Basics',
            'subcategories': [
              {
                'id': 11,
                'name': 'Python语法',
                'description': '掌握Python基础语法',
                'highlight': '入门必学',
                'english_name': 'Python Syntax',
                'subcategories': [
                  {
                    'id': 111,
                    'name': '变量和数据类型',
                    'description': '学习Python的基本数据类型',
                    'highlight': '基础核心',
                    'english_name': 'Variables and Data Types',
                    'subcategories': [
                      {
                        'id': 1111,
                        'name': '字符串操作',
                        'description': '掌握字符串的各种操作方法',
                        'highlight': '实用技能',
                        'english_name': 'String Operations',
                        'tab_type': 'quiz',
                      },
                      {
                        'id': 1112,
                        'name': '数字运算',
                        'description': '学习数字类型和运算',
                        'highlight': '数学基础',
                        'english_name': 'Numeric Operations',
                        'tab_type': 'quiz',
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            'id': 2,
            'name': 'Java基础',
            'description': 'Java编程入门到精通，掌握面向对象编程',
            'highlight': '企业级应用开发必备',
            'english_name': 'Java Basics',
            'subcategories': [
              {
                'id': 21,
                'name': 'Java语法',
                'description': '掌握Java基础语法',
                'highlight': 'OOP入门',
                'english_name': 'Java Syntax',
                'subcategories': [],
              },
            ],
          },
          {
            'id': 3,
            'name': 'JavaScript基础',
            'description': 'Web前端开发必备技能',
            'highlight': 'Web开发基础',
            'english_name': 'JavaScript Basics',
            'subcategories': [],
          },
        ],
      },
      {
        'category': '数据科学',
        'id': 2,
        'courses': [
          {
            'id': 4,
            'name': '数据分析',
            'description': '使用Python进行数据分析',
            'highlight': '热门技能',
            'english_name': 'Data Analysis',
            'subcategories': [
              {
                'id': 41,
                'name': 'NumPy基础',
                'description': '科学计算基础库',
                'highlight': '数据处理利器',
                'english_name': 'NumPy Basics',
                'subcategories': [],
              },
              {
                'id': 42,
                'name': 'Pandas操作',
                'description': '数据处理和分析',
                'highlight': '数据科学必备',
                'english_name': 'Pandas Operations',
                'subcategories': [],
              },
            ],
          },
          {
            'id': 5,
            'name': '机器学习',
            'description': '机器学习算法与应用',
            'highlight': 'AI时代核心技术',
            'english_name': 'Machine Learning',
            'subcategories': [],
          },
        ],
      },
      {
        'category': 'Web开发',
        'id': 3,
        'courses': [
          {
            'id': 6,
            'name': '前端开发',
            'description': 'HTML、CSS、JavaScript全栈',
            'highlight': 'Web开发基础',
            'english_name': 'Frontend Development',
            'subcategories': [],
          },
          {
            'id': 7,
            'name': '后端开发',
            'description': 'Node.js、Express框架开发',
            'highlight': '全栈开发',
            'english_name': 'Backend Development',
            'subcategories': [],
          },
        ],
      },
    ];
  }
}
