import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CacheService {
  // 内存缓存
  static final Map<String, dynamic> _memoryCache = {};
  
  // 缓存数据到内存
  static void cacheData(String key, dynamic data) {
    _memoryCache[key] = data;
  }
  
  // 从内存获取缓存数据
  static dynamic getCachedData(String key) {
    return _memoryCache[key];
  }
  
  // 检查内存中是否有缓存数据
  static bool hasCachedData(String key) {
    return _memoryCache.containsKey(key) && _memoryCache[key] != null;
  }
  
  // 清除特定内存缓存
  static void clearCache(String key) {
    _memoryCache.remove(key);
  }
  
  // 清除所有内存缓存
  static void clearAllCache() {
    _memoryCache.clear();
  }
  
  // 持久化缓存数据
  static Future<bool> persistData(String key, dynamic data) async {
    final prefs = await SharedPreferences.getInstance();
    String jsonData = jsonEncode(data);
    return await prefs.setString(key, jsonData);
  }
  
  // 获取持久化缓存数据
  static Future<dynamic> getPersistedData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    String? jsonData = prefs.getString(key);
    if (jsonData == null) return null;
    return jsonDecode(jsonData);
  }
  
  // 检查是否有持久化缓存数据
  static Future<bool> hasPersistedData(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(key);
  }
  
  // 清除特定持久化缓存
  static Future<bool> clearPersistedCache(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.remove(key);
  }
  
  // 清除所有持久化缓存
  static Future<bool> clearAllPersistedCache() async {
    final prefs = await SharedPreferences.getInstance();
    return await prefs.clear();
  }
} 