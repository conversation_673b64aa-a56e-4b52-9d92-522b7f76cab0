import 'api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// 用户服务类，处理用户身份验证、用户信息等API请求
class UserService extends ApiService {
  /// 单例模式
  static final UserService _instance = UserService._internal();

  factory UserService() {
    return _instance;
  }

  UserService._internal();

  /// 本地存储的token键
  static const String _tokenKey = 'auth_token';

  /// 本地存储的用户信息键
  static const String _userInfoKey = 'user_info';

  /// 当前token
  String? _token;

  /// 设置token
  void setToken(String token) {
    _token = token;
  }

  /// 清除token
  void clearToken() {
    _token = null;
  }

  /// 获取授权token
  Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// 发送验证码到指定手机号
  /// [phone] 手机号
  Future<bool> sendVerificationCode(String phone) async {
    try {
      final response = await post('/user/send_code', body: {'phone': phone});
      return response['success'] == true;
    } catch (e) {
      return false;
    }
  }

  /// 使用手机号和验证码登录
  /// [phone] 手机号
  /// [code] 验证码
  Future<bool> login(String phone, String code) async {
    try {
      final response = await post(
        '/user/login',
        body: {'phone': phone, 'code': code},
      );

      if (response['token'] != null) {
        // 保存token到本地存储
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, response['token']);

        // 设置API服务的token
        setToken(response['token']);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 退出登录
  Future<bool> logout() async {
    try {
      await post('/user/logout', body: {});

      // 清除本地存储中的token和用户信息
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userInfoKey);

      // 清除API服务的token
      clearToken();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 检查用户是否已登录
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);

    if (token != null && token.isNotEmpty) {
      // 设置API服务的token
      setToken(token);
      return true;
    }
    return false;
  }

  /// 获取用户信息
  Future<Map<String, dynamic>?> getUserInfo() async {
    try {
      // 先尝试从本地获取
      final prefs = await SharedPreferences.getInstance();
      final cachedInfo = prefs.getString(_userInfoKey);

      if (cachedInfo != null) {
        return Map<String, dynamic>.from(jsonDecode(cachedInfo) as Map);
      }

      // 如果本地没有，则从服务器获取
      final response = await get('/user/profile');

      if (response['user'] != null) {
        // 保存到本地
        await prefs.setString(_userInfoKey, jsonEncode(response['user']));
        return response['user'];
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// 更新用户信息
  /// [userData] 要更新的用户数据
  Future<bool> updateUserInfo(Map<String, dynamic> userData) async {
    try {
      // 使用POST方法调用 /api/profile 来更新用户信息
      final token = await getAuthToken();
      final response = await post('/profile', body: userData, token: token);

      if (response['success'] == true) {
        // 更新本地存储中的用户信息
        final prefs = await SharedPreferences.getInstance();
        final cachedInfo = prefs.getString(_userInfoKey);

        if (cachedInfo != null) {
          final currentInfo = Map<String, dynamic>.from(
            jsonDecode(cachedInfo) as Map,
          );
          currentInfo.addAll(userData);
          await prefs.setString(_userInfoKey, jsonEncode(currentInfo));
        }

        return true;
      }

      return false;
    } catch (e) {
      print('更新用户信息失败: $e');
      return false;
    }
  }
}
