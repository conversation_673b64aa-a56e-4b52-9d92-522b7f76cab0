import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:collection';
import '../core/state/optimized_notifier.dart';

/// 优化的预加载服务，支持智能缓存和优先级管理
class OptimizedPreloadService {
  static final OptimizedPreloadService _instance =
      OptimizedPreloadService._internal();
  factory OptimizedPreloadService() => _instance;
  OptimizedPreloadService._internal();

  // 缓存管理
  final Map<String, AsyncStateNotifier> _cachePool = {};
  final Map<String, Timer> _cacheExpiry = {};
  final Map<String, int> _priorityMap = {};

  // 配置
  static const Duration _defaultCacheExpiry = Duration(minutes: 10);
  static const int _maxCacheSize = 50;

  /// 获取或创建缓存状态
  AsyncStateNotifier<T> getOrCreateCache<T>(String key) {
    if (!_cachePool.containsKey(key)) {
      _cachePool[key] = AsyncStateNotifier<T>();
      _setPriority(key, 1);
    }
    return _cachePool[key] as AsyncStateNotifier<T>;
  }

  /// 预加载数据
  Future<T?> preload<T>(
    String key,
    Future<T> Function() dataLoader, {
    Duration? cacheExpiry,
    int priority = 1,
    bool forceRefresh = false,
  }) async {
    final cache = getOrCreateCache<T>(key);

    // 设置优先级
    _setPriority(key, priority);

    // 检查是否需要刷新
    if (!forceRefresh && cache.hasData && !_isCacheExpired(key)) {
      return cache.data;
    }

    // 执行数据加载
    await cache.execute(dataLoader);

    // 设置缓存过期时间
    _setCacheExpiry(key, cacheExpiry ?? _defaultCacheExpiry);

    return cache.data;
  }

  /// 批量预加载
  Future<Map<String, dynamic>> batchPreload(
    Map<String, Future Function()> loaders, {
    Duration? cacheExpiry,
    int maxConcurrency = 3,
  }) async {
    final results = <String, dynamic>{};
    final futures = <Future>[];
    final semaphore = Semaphore(maxConcurrency);

    for (final entry in loaders.entries) {
      final future = semaphore.acquire().then((_) async {
        try {
          final result = await preload(
            entry.key,
            entry.value,
            cacheExpiry: cacheExpiry,
          );
          results[entry.key] = result;
        } finally {
          semaphore.release();
        }
      });
      futures.add(future);
    }

    await Future.wait(futures);
    return results;
  }

  /// 优先级预加载
  Future<void> priorityPreload(List<PreloadTask> tasks) async {
    // 按优先级排序
    tasks.sort((a, b) => b.priority.compareTo(a.priority));

    final highPriorityTasks = tasks.where((t) => t.priority >= 8).toList();
    final mediumPriorityTasks =
        tasks.where((t) => t.priority >= 5 && t.priority < 8).toList();
    final lowPriorityTasks = tasks.where((t) => t.priority < 5).toList();

    // 立即执行高优先级任务
    await Future.wait(
      highPriorityTasks.map(
        (task) => preload(task.key, task.loader, priority: task.priority),
      ),
    );

    // 延迟执行中优先级任务
    Timer(const Duration(milliseconds: 100), () {
      for (final task in mediumPriorityTasks) {
        preload(task.key, task.loader, priority: task.priority);
      }
    });

    // 进一步延迟执行低优先级任务
    Timer(const Duration(milliseconds: 500), () {
      for (final task in lowPriorityTasks) {
        preload(task.key, task.loader, priority: task.priority);
      }
    });
  }

  /// 获取缓存数据
  T? getCachedData<T>(String key) {
    final cache = _cachePool[key] as AsyncStateNotifier<T>?;
    return cache?.data;
  }

  /// 检查缓存是否存在且有效
  bool hasCachedData(String key) {
    final cache = _cachePool[key];
    return cache != null && cache.hasData && !_isCacheExpired(key);
  }

  /// 清理过期缓存
  void cleanupExpiredCache() {
    final expiredKeys = <String>[];

    for (final key in _cachePool.keys) {
      if (_isCacheExpired(key)) {
        expiredKeys.add(key);
      }
    }

    for (final key in expiredKeys) {
      _removeCacheEntry(key);
    }

    // 如果缓存过多，清理低优先级的缓存
    if (_cachePool.length > _maxCacheSize) {
      _cleanupLowPriorityCache();
    }
  }

  /// 清理特定缓存
  void clearCache(String key) {
    _removeCacheEntry(key);
  }

  /// 清理所有缓存
  void clearAllCache() {
    for (final key in _cachePool.keys.toList()) {
      _removeCacheEntry(key);
    }
  }

  // 私有方法
  void _setPriority(String key, int priority) {
    _priorityMap[key] = priority;
  }

  void _setCacheExpiry(String key, Duration duration) {
    _cacheExpiry[key]?.cancel();
    _cacheExpiry[key] = Timer(duration, () {
      _removeCacheEntry(key);
    });
  }

  bool _isCacheExpired(String key) {
    return !_cacheExpiry.containsKey(key);
  }

  void _removeCacheEntry(String key) {
    _cachePool[key]?.dispose();
    _cachePool.remove(key);
    _cacheExpiry[key]?.cancel();
    _cacheExpiry.remove(key);
    _priorityMap.remove(key);
  }

  void _cleanupLowPriorityCache() {
    final sortedEntries =
        _priorityMap.entries.toList()
          ..sort((a, b) => a.value.compareTo(b.value));

    final entriesToRemove = sortedEntries.take(
      _cachePool.length - _maxCacheSize + 10,
    );
    for (final entry in entriesToRemove) {
      _removeCacheEntry(entry.key);
    }
  }
}

/// 预加载任务
class PreloadTask {
  final String key;
  final Future Function() loader;
  final int priority;

  const PreloadTask({
    required this.key,
    required this.loader,
    this.priority = 5,
  });
}

/// 信号量，用于控制并发数量
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitingQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitingQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitingQueue.isNotEmpty) {
      final completer = _waitingQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
