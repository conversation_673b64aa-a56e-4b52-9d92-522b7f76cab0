import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/quiz_model.dart';
import '../services/quiz_service.dart';
import '../widgets/quiz_attempt_sheet.dart';

/// 数据预加载服务，管理跨页面数据缓存
class PreloadService {
  static final PreloadService _instance = PreloadService._internal();

  factory PreloadService() {
    return _instance;
  }

  PreloadService._internal();

  // 使用Map<String, Future>缓存异步请求结果
  final Map<String, Future<dynamic>> _cachedRequests = {};

  // 课程详细信息缓存
  final Map<int, Future<List<QuizPageCategoryTab>>> _courseTabsCache = {};

  // 题目集缓存
  final Map<int, Future<List<QuizPageQuestion>>> _questionsCache = {};

  /// 预加载课程分类标签数据
  Future<List<QuizPageCategoryTab>> preloadCategoryTabs(
    int courseId,
    String token,
  ) {
    final cacheKey = 'course_tabs_$courseId';

    if (!_cachedRequests.containsKey(cacheKey)) {
      debugPrint('⏳ [PreloadService] 开始预加载课程标签: courseId=$courseId');

      // 创建带有超时和重试的Future
      final future = () async {
        try {
          // 如果API调用失败或超时，返回默认标签
          final tabs = await QuizService.getCategoryTabs(
            courseId,
            token,
          ).timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              debugPrint('⚠️ [PreloadService] 预加载课程标签超时: courseId=$courseId');
              // 超时时返回默认标签
              return _getDefaultModelTabs(courseId);
            },
          );

          // 将模型的CategoryTab转换为QuizPageCategoryTab
          return tabs
              .map(
                (tab) => QuizPageCategoryTab(
                  id: tab.id,
                  name: tab.name,
                  description: tab.description,
                  level4Id: tab.level4Id,
                  isActive: tab.isActive,
                  tabType: tab.tabType,
                  englishName: tab.englishName,
                ),
              )
              .toList();
        } catch (error) {
          // 提供更详细的错误日志
          String errorType = 'Unknown';
          if (error.toString().contains('timeout') || error.toString().contains('TimeoutException')) {
            errorType = 'Timeout';
          } else if (error.toString().contains('SocketException')) {
            errorType = 'Network';
          } else if (error.toString().contains('FormatException')) {
            errorType = 'Format';
          } else if (error.toString().contains('404')) {
            errorType = 'NotFound';
          } else if (error.toString().contains('500')) {
            errorType = 'ServerError';
          }
          
          debugPrint(
            '❌ [PreloadService] 预加载课程标签失败: courseId=$courseId, 类型:$errorType, 错误: $error',
          );
          // 异常时返回默认标签
          return _getDefaultTabs(courseId);
        }
      }();

      _cachedRequests[cacheKey] = future;
      _courseTabsCache[courseId] = future.then((tabs) {
        debugPrint(
          '✅ [PreloadService] 成功预加载课程标签: courseId=$courseId, 数量: ${tabs.length}',
        );
        return tabs;
      });
    } else {
      debugPrint('➡️ [PreloadService] 使用缓存的课程标签: courseId=$courseId');
    }

    return _courseTabsCache[courseId]!;
  }

  /// 获取原始CategoryTab模型对象（用于API调用）
  List<CategoryTab> _getDefaultModelTabs(int courseId) {
    return [
      CategoryTab(
        id: 0,
        name: "答题",
        description: "练习答题",
        level4Id: courseId,
        isActive: true,
        tabType: "quiz",
        englishName: "Quiz",
      ),
      CategoryTab(
        id: 1,
        name: "错题本",
        description: "查看错题",
        level4Id: courseId,
        isActive: true,
        tabType: "wrong_questions",
        englishName: "Wrong Questions",
      ),
      CategoryTab(
        id: 2,
        name: "数据面板",
        description: "答题统计",
        level4Id: courseId,
        isActive: true,
        tabType: "stats",
        englishName: "Statistics",
      ),
    ];
  }

  /// 创建默认的UI标签页（当API调用失败时使用）
  List<QuizPageCategoryTab> _getDefaultTabs(int courseId) {
    return [
      QuizPageCategoryTab(
        id: 0,
        name: "答题",
        description: "练习答题",
        level4Id: courseId,
        isActive: true,
        tabType: "quiz",
        englishName: "Quiz",
      ),
      QuizPageCategoryTab(
        id: 1,
        name: "错题本",
        description: "查看错题",
        level4Id: courseId,
        isActive: true,
        tabType: "wrong_questions",
        englishName: "Wrong Questions",
      ),
      QuizPageCategoryTab(
        id: 2,
        name: "数据面板",
        description: "答题统计",
        level4Id: courseId,
        isActive: true,
        tabType: "stats",
        englishName: "Statistics",
      ),
    ];
  }

  /// 预加载课程问题数据
  Future<List<QuizPageQuestion>> preloadQuestions(int courseId, String token) {
    final cacheKey = 'questions_$courseId';

    if (!_cachedRequests.containsKey(cacheKey)) {
      debugPrint('⏳ [PreloadService] 开始预加载课程问题: courseId=$courseId');

      // 创建带有超时和恢复的Future
      final future = () async {
        try {
          // 使用带有重试机制的方法获取问题
          final questions = await _fetchQuestionsWithRetry(
            courseId,
            token,
            retryCount: 2, // 最多重试2次
            timeout: const Duration(seconds: 10), // 每次请求超时时间为10秒
          );

          // 将模型的Question转换为QuizPageQuestion
          return questions
              .map(
                (q) => QuizPageQuestion(
                  id: q.id,
                  content: q.content,
                  answer: q.answer,
                  explanation: q.explanation,
                ),
              )
              .toList();
        } catch (error) {
          // 提供更详细的错误日志
          String errorType = 'Unknown';
          if (error.toString().contains('timeout') || error.toString().contains('TimeoutException')) {
            errorType = 'Timeout';
          } else if (error.toString().contains('SocketException')) {
            errorType = 'Network';
          } else if (error.toString().contains('FormatException')) {
            errorType = 'Format';
          } else if (error.toString().contains('404')) {
            errorType = 'NotFound';
          } else if (error.toString().contains('500')) {
            errorType = 'ServerError';
          }
          
          debugPrint(
            '❌ [PreloadService] 预加载课程问题失败: courseId=$courseId, 类型:$errorType, 错误: $error',
          );
          // 出错时返回空列表
          return <QuizPageQuestion>[];
        }
      }();

      _cachedRequests[cacheKey] = future;
      _questionsCache[courseId] = future.then((questions) {
        debugPrint(
          '✅ [PreloadService] 成功预加载课程问题: courseId=$courseId, 数量: ${questions.length}',
        );
        return questions;
      });
    } else {
      debugPrint('➡️ [PreloadService] 使用缓存的课程问题: courseId=$courseId');
    }

    return _questionsCache[courseId]!;
  }

  /// 带重试机制的获取问题方法
  Future<List<Question>> _fetchQuestionsWithRetry(
    int courseId,
    String token, {
    int retryCount = 2,
    Duration timeout = const Duration(seconds: 10),
  }) async {
    int attempts = 0;

    while (attempts <= retryCount) {
      attempts++;
      try {
        return await QuizService.getQuestions(courseId, token).timeout(
          timeout,
          onTimeout: () {
            if (attempts < retryCount) {
              debugPrint('⚠️ [PreloadService] 请求超时，尝试重试 #$attempts');
              throw TimeoutException('请求超时');
            } else {
              debugPrint('⚠️ [PreloadService] 预加载课程问题已达到最大重试次数');
              return <Question>[];
            }
          },
        );
      } catch (e) {
        if (e is TimeoutException && attempts <= retryCount) {
          // 超时异常且未达到最大重试次数，继续循环重试
          continue;
        }
        if (attempts >= retryCount) {
          debugPrint('❌ [PreloadService] 预加载多次失败: $e');
          return <Question>[];
        }
        // 其他异常，短暂延迟后重试
        await Future.delayed(const Duration(milliseconds: 300));
      }
    }

    return <Question>[];
  }

  /// 预加载指定标签的题库内容
  Future<List<QuizPageQuestionSet>> preloadTabContent(
    QuizPageCategoryTab tab,
    String token,
  ) {
    final cacheKey = 'tab_content_${tab.id}';

    if (!_cachedRequests.containsKey(cacheKey)) {
      debugPrint('⏳ 预加载标签内容: tabId=${tab.id}, tabType=${tab.tabType}');

      final future = QuizService.getLevel6Content(
        tab.id,
        token,
        contentType: tab.tabType,
      ).then((questionSets) {
        // 将模型的QuestionSet转换为QuizPageQuestionSet
        return questionSets
            .map(
              (qs) => QuizPageQuestionSet(
                id: qs.id,
                name: qs.title,
                description: '',
                categoryTabId: qs.courseItemId,
              ),
            )
            .toList();
      });

      _cachedRequests[cacheKey] = future;
      return future;
    } else {
      debugPrint('✅ 使用缓存的标签内容: tabId=${tab.id}');
    }

    return _cachedRequests[cacheKey] as Future<List<QuizPageQuestionSet>>;
  }

  /// 清除特定课程的缓存
  void clearCourseCache(int courseId) {
    final courseTabsKey = 'course_tabs_$courseId';
    final questionsKey = 'questions_$courseId';

    // 移除缓存的请求
    _cachedRequests.remove(courseTabsKey);
    _cachedRequests.remove(questionsKey);

    // 移除缓存的结果
    _courseTabsCache.remove(courseId);
    _questionsCache.remove(courseId);

    debugPrint('🗑️ [PreloadService] 已清除课程缓存: courseId=$courseId');
  }

  /// 清除所有缓存
  void clearAllCache() {
    _cachedRequests.clear();
    _courseTabsCache.clear();
    _questionsCache.clear();
    debugPrint('🗑️ [PreloadService] 已清除所有缓存');
  }

  /// 获取缓存状态
  bool isCached(String cacheKey) {
    return _cachedRequests.containsKey(cacheKey);
  }

  /// 获取缓存大小
  int get cacheSize => _cachedRequests.length;
}
