import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/knowledge_card_model.dart';
import '../services/auth_service.dart';
import 'dart:developer' as developer;

class KnowledgeCardService {
  static String get baseUrl => AuthService.baseUrl;

  // 获取用户的知识卡
  Future<List<KnowledgeCard>> getKnowledgeCards(String userId) async {
    try {
      // 这里先使用模拟数据，后续可以连接真实API
      return await _generateMockKnowledgeCards(userId);
    } catch (e) {
      developer.log('获取知识卡失败: $e');
      // 如果API失败，返回模拟数据确保用户体验
      return await _generateMockKnowledgeCards(userId);
    }
  }

  // 根据课程获取知识卡
  Future<List<KnowledgeCard>> getKnowledgeCardsByCourse(
    String userId,
    String courseId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/knowledge-cards/course/$courseId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => KnowledgeCard.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load knowledge cards: ${response.statusCode}');
      }
    } catch (e) {
      developer.log('获取课程知识卡失败: $e');
      // 返回模拟数据
      return await _generateMockKnowledgeCardsForCourse(courseId);
    }
  }

  // 获取用户已加入的课程
  Future<List<Map<String, dynamic>>> _getUserCourses(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/user_course/list/$userId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> courses = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(courses);
      } else {
        return [];
      }
    } catch (e) {
      developer.log('获取用户课程失败: $e');
      return [];
    }
  }

  // 生成模拟知识卡数据
  Future<List<KnowledgeCard>> _generateMockKnowledgeCards(String userId) async {
    // 获取用户课程信息（这里使用模拟数据）
    final mockCourses = [
      {
        'course_id': 1,
        'course_name': '公务员考试',
      },
      {
        'course_id': 2,
        'course_name': '计算机科学',
      },
      {
        'course_id': 3,
        'course_name': '英语四六级',
      },
    ];

    final List<KnowledgeCard> cards = [];
    
    for (final course in mockCourses) {
      cards.addAll(await _generateMockKnowledgeCardsForCourse(
        course['course_id'].toString(),
        courseName: course['course_name'] as String,
      ));
    }

    // 随机打乱顺序，模拟个性化推荐
    cards.shuffle(Random());
    
    // 限制返回数量
    return cards.take(10).toList();
  }

  // 为特定课程生成模拟知识卡
  Future<List<KnowledgeCard>> _generateMockKnowledgeCardsForCourse(
    String courseId, {
    String? courseName,
  }) async {
    final now = DateTime.now();
    final random = Random();
    
    // 根据课程ID生成不同的知识卡内容
    final courseConfigs = {
      '1': { // 公务员考试
        'name': courseName ?? '公务员考试',
        'cards': [
          {
            'title': '申论写作技巧',
            'content': '申论写作是公务员考试的重要组成部分，需要掌握议论文的基本结构和论证方法。\n\n首先要明确文章的立意和主旨，确保论点鲜明、论据充分。其次要注意文章的逻辑结构，采用总分总的布局，每个分论点都要有具体的事例支撑。\n\n在语言表达上，要做到准确、规范、生动，避免口语化表达，多使用公文式的正式语言。',
            'keyPoints': [
              '明确立意，论点鲜明',
              '结构清晰，逻辑严密',
              '论据充分，事例典型',
              '语言规范，表达准确',
              '卷面整洁，字迹工整'
            ],
            'questions': [
              {
                'question': '申论文章的基本结构通常采用什么形式？',
                'options': ['总分总', '分总分', '总分', '分总'],
                'correct': '总分总',
                'explanation': '申论文章通常采用总分总结构，即开头提出论点，中间分层论证，结尾总结升华。'
              },
              {
                'question': '申论写作中最重要的是什么？',
                'options': ['文采飞扬', '立意准确', '字数达标', '引用名言'],
                'correct': '立意准确',
                'explanation': '立意是申论写作的灵魂，只有立意准确，才能确保文章的方向正确。'
              }
            ]
          },
          {
            'title': '行测数量关系解题策略',
            'content': '数量关系是行测考试的难点，也是拉开分差的关键部分。解题时要掌握基本的数学思维和解题技巧。\n\n常用方法包括：代入法、特值法、比例法、十字交叉法等。要根据题目特点选择合适的方法，提高解题效率。\n\n时间分配很重要，建议每题控制在1-2分钟内，遇到复杂题目可以先跳过，最后再回来处理。',
            'keyPoints': [
              '掌握基本数学概念',
              '熟练运用解题技巧',
              '合理分配答题时间',
              '先易后难的策略',
              '多做真题练习'
            ],
            'questions': [
              {
                'question': '行测数量关系每题建议用时多长？',
                'options': ['30秒', '1-2分钟', '3-5分钟', '不限时间'],
                'correct': '1-2分钟',
                'explanation': '考虑到行测考试时间紧张，数量关系每题建议控制在1-2分钟内完成。'
              }
            ]
          }
        ]
      },
      '2': { // 计算机科学
        'name': courseName ?? '计算机科学',
        'cards': [
          {
            'title': '数据结构基础概念',
            'content': '数据结构是计算机科学的基础，它研究数据的逻辑结构、存储结构以及在其上定义的运算。\n\n常见的数据结构包括：线性结构（数组、链表、栈、队列）和非线性结构（树、图）。每种结构都有其特定的应用场景和性能特点。\n\n理解时间复杂度和空间复杂度对于算法设计至关重要，它们帮助我们评估算法的效率。',
            'keyPoints': [
              '理解逻辑结构与存储结构',
              '掌握基本数据结构特点',
              '熟悉常用算法操作',
              '分析时间空间复杂度',
              '选择合适的数据结构'
            ],
            'questions': [
              {
                'question': '栈的操作特点是什么？',
                'options': ['先进先出', '后进先出', '随机访问', '双端操作'],
                'correct': '后进先出',
                'explanation': '栈是一种后进先出(LIFO)的数据结构，只能在栈顶进行插入和删除操作。'
              }
            ]
          }
        ]
      },
      '3': { // 英语四六级
        'name': courseName ?? '英语四六级',
        'cards': [
          {
            'title': '阅读理解解题技巧',
            'content': '阅读理解是四六级考试的重点，占分比重大。掌握正确的解题方法能够显著提高准确率。\n\n解题步骤：首先快速浏览全文，把握文章主旨；然后仔细阅读题目，理解题意；最后回到原文定位，寻找答案依据。\n\n注意区分细节题、推理题、主旨题等不同题型，采用相应的解题策略。',
            'keyPoints': [
              '快速浏览把握主旨',
              '仔细审题理解题意',
              '精确定位答案区域',
              '区分不同题型特点',
              '控制答题时间节奏'
            ],
            'questions': [
              {
                'question': '阅读理解解题的第一步是什么？',
                'options': ['看题目', '看选项', '浏览全文', '逐句翻译'],
                'correct': '浏览全文',
                'explanation': '快速浏览全文可以帮助我们把握文章的整体结构和主要内容，为后续解题做准备。'
              }
            ]
          }
        ]
      }
    };

    final config = courseConfigs[courseId];
    if (config == null) {
      return [];
    }

    final List<KnowledgeCard> cards = [];
    final cardTemplates = config['cards'] as List<Map<String, dynamic>>;

    for (int i = 0; i < cardTemplates.length; i++) {
      final template = cardTemplates[i];
      final questions = (template['questions'] as List<Map<String, dynamic>>)
          .map((q) => KnowledgeQuestion(
                id: '${courseId}_${i}_q${Random().nextInt(1000)}',
                question: q['question'] as String,
                options: List<String>.from(q['options'] as List),
                correctAnswer: q['correct'] as String,
                explanation: q['explanation'] as String,
                difficulty: ['easy', 'medium', 'hard'][random.nextInt(3)],
              ))
          .toList();

      cards.add(KnowledgeCard(
        id: '${courseId}_card_$i',
        title: template['title'] as String,
        content: template['content'] as String,
        courseId: courseId,
        courseName: config['name'] as String,
        keyPoints: List<String>.from(template['keyPoints'] as List),
        relatedQuestions: questions,
        createdAt: now.subtract(Duration(days: random.nextInt(30))),
        difficulty: random.nextInt(3) + 1,
        tags: _generateRandomTags(),
      ));
    }

    return cards;
  }

  // 生成随机标签
  List<String> _generateRandomTags() {
    final allTags = [
      '重点', '基础', '提升', '实用', '技巧',
      '理论', '实践', '考试', '复习', '难点'
    ];
    
    final random = Random();
    final tagCount = random.nextInt(3) + 1; // 1-3个标签
    final selectedTags = <String>[];
    
    while (selectedTags.length < tagCount) {
      final tag = allTags[random.nextInt(allTags.length)];
      if (!selectedTags.contains(tag)) {
        selectedTags.add(tag);
      }
    }
    
    return selectedTags;
  }

  // 提交答题结果
  Future<void> submitQuestionAnswer({
    required String userId,
    required String questionId,
    required String answer,
    required bool isCorrect,
  }) async {
    try {
      await http.post(
        Uri.parse('$baseUrl/api/knowledge-cards/answer'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'user_id': userId,
          'question_id': questionId,
          'answer': answer,
          'is_correct': isCorrect,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      developer.log('提交答题结果失败: $e');
      // 答题结果提交失败不影响用户体验，只记录日志
    }
  }

  // 获取用户答题统计
  Future<Map<String, dynamic>> getUserAnswerStats(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/knowledge-cards/stats/$userId'),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        // 返回默认统计数据
        return {
          'total_questions': 0,
          'correct_answers': 0,
          'accuracy_rate': 0.0,
          'study_days': 0,
        };
      }
    } catch (e) {
      developer.log('获取用户统计失败: $e');
      return {
        'total_questions': 0,
        'correct_answers': 0,
        'accuracy_rate': 0.0,
        'study_days': 0,
      };
    }
  }
}