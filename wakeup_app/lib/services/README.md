# 数据缓存系统

本项目实现了一个多层次的数据缓存系统，用于减少网络请求，提高应用性能。

## 组件

### 1. CacheService

基础缓存服务，提供内存缓存和持久化缓存的功能。

```dart
import '../services/cache_service.dart';

// 内存缓存
CacheService.cacheData('key', data);
dynamic data = CacheService.getCachedData('key');
bool exists = CacheService.hasCachedData('key');

// 持久化缓存 
await CacheService.persistData('key', data);
dynamic data = await CacheService.getPersistedData('key');
bool exists = await CacheService.hasPersistedData('key');
```

### 2. ApiCacheProvider

API请求缓存提供者，集成了缓存功能的HTTP请求。

```dart
import '../providers/api_cache_provider.dart';

final apiCacheProvider = Provider.of<ApiCacheProvider>(context, listen: false);

// 使用缓存的GET请求
final data = await apiCacheProvider.getWithCache(
  'https://api.example.com/data',
  headers: {'Authorization': 'Bearer $token'},
  useMemoryCache: true,     // 是否使用内存缓存
  usePersistentCache: true, // 是否使用持久化缓存
  forceRefresh: false,      // 是否强制刷新
);

// 清除特定缓存
apiCacheProvider.clearCache(
  'https://api.example.com/data',
  clearMemory: true,
  clearPersistent: true
);

// 清除所有缓存
apiCacheProvider.clearAllCache();
```

## 使用方法

1. 在 `main.dart` 中注册 `ApiCacheProvider`
2. 在需要缓存的页面中使用 `getWithCache` 方法
3. 实现刷新功能使用 `forceRefresh: true` 参数

## 优势

- 减少网络请求次数，提高应用响应速度
- 离线模式下可访问缓存数据
- 支持强制刷新，确保数据最新
- 可配置性高，允许按需设置缓存策略

## 并发请求与优先渲染

### 并发请求模式

使用 `Future.wait` 可以同时发起多个请求，提高加载速度：

```dart
// 并发请求多个接口
final results = await Future.wait([
  apiCacheProvider.getWithCache('http://example.com/api1'),
  apiCacheProvider.getWithCache('http://example.com/api2'),
  apiCacheProvider.getWithCache('http://example.com/api3'),
]);

// 分别获取结果
final data1 = results[0];
final data2 = results[1];
final data3 = results[2];
```

### 优先级渲染策略

1. **优先级分组**：将数据请求分为高优先级（必要内容）和低优先级（附加内容）
2. **分批加载**：先加载高优先级数据，后加载低优先级数据
3. **错误处理**：高优先级数据加载失败应显示错误，低优先级数据失败可静默处理

```dart
// 1. 加载高优先级数据（页面标题、主内容）
_loadPrimaryData();

// 2. 延迟加载低优先级数据（推荐、活动等）
Future.delayed(const Duration(milliseconds: 50), () {
  _loadSecondaryData();
});
```

### 渐进式UI渲染

随着数据加载，UI逐步填充，提供更好的用户体验：

1. 骨架屏或加载指示器
2. 显示标题和主要内容
3. 显示次要内容
4. 完成整体加载 