import '../models/article_model.dart';
import '../core/services/unified_api_service.dart';
import 'package:flutter/widgets.dart';

class ArticleService {
  final UnifiedApiService _apiService = UnifiedApiService();

  // 缓存，避免重复请求
  List<Article>? _cachedArticles;
  List<ArticleCategory>? _cachedCategories;
  List<ArticleCollection>? _cachedCollections;

  // 单例模式
  static final ArticleService _instance = ArticleService._internal();
  factory ArticleService() => _instance;
  ArticleService._internal();

  // 获取所有文章
  Future<List<Article>> getArticles({
    bool forceRefresh = false,
    BuildContext? context,
  }) async {
    if (_cachedArticles != null && !forceRefresh) {
      return _cachedArticles!;
    }

    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/articles',
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        final List<Article> articles =
            response.data!.map((x) => Article.fromJson(x)).toList();
        _cachedArticles = articles;
        return articles;
      } else {
        throw Exception(response.error?.message ?? '获取文章失败');
      }
    } catch (e) {
      throw Exception('获取文章失败: $e');
    }
  }

  // 获取特定分类下的文章
  Future<List<Article>> getArticlesByCategory(
    String categoryId, {
    BuildContext? context,
  }) async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/articles/category/$categoryId',
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        return response.data!.map((x) => Article.fromJson(x)).toList();
      } else {
        throw Exception(response.error?.message ?? '获取分类文章失败');
      }
    } catch (e) {
      throw Exception('获取分类文章失败: $e');
    }
  }

  // 获取特定文章详情
  Future<Article?> getArticleById(String id, {BuildContext? context}) async {
    try {
      final response = await _apiService.get<Map<String, dynamic>>(
        '/api/articles/$id',
        context: context,
      );

      if (response.isSuccess && response.data != null) {
        return Article.fromJson(response.data!);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // 获取所有分类
  Future<List<ArticleCategory>> getCategories({BuildContext? context}) async {
    if (_cachedCategories != null) {
      return _cachedCategories!;
    }

    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/article_categories',
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        final List<ArticleCategory> categories =
            response.data!.map((x) => ArticleCategory.fromJson(x)).toList();
        _cachedCategories = categories;
        return categories;
      } else {
        throw Exception(response.error?.message ?? '获取文章分类失败');
      }
    } catch (e) {
      throw Exception('获取文章分类失败: $e');
    }
  }

  // 获取文章合集
  Future<List<ArticleCollection>> getCollections({
    BuildContext? context,
  }) async {
    if (_cachedCollections != null) {
      return _cachedCollections!;
    }

    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/article_collections',
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        final List<ArticleCollection> collections =
            response.data!.map((x) => ArticleCollection.fromJson(x)).toList();
        _cachedCollections = collections;
        return collections;
      } else {
        throw Exception(response.error?.message ?? '获取文章合集失败');
      }
    } catch (e) {
      throw Exception('获取文章合集失败: $e');
    }
  }

  // 获取精选文章
  Future<List<Article>> getFeaturedArticles({BuildContext? context}) async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/articles/featured',
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        return response.data!.map((x) => Article.fromJson(x)).toList();
      } else {
        throw Exception(response.error?.message ?? '获取精选文章失败');
      }
    } catch (e) {
      throw Exception('获取精选文章失败: $e');
    }
  }

  // 搜索文章
  Future<List<Article>> searchArticles(
    String query, {
    BuildContext? context,
  }) async {
    try {
      final response = await _apiService.get<List<dynamic>>(
        '/api/articles/search',
        queryParams: {'q': query},
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (response.isSuccess && response.data != null) {
        return response.data!.map((x) => Article.fromJson(x)).toList();
      } else {
        throw Exception(response.error?.message ?? '搜索文章失败');
      }
    } catch (e) {
      throw Exception('搜索文章失败: $e');
    }
  }

  // 清理缓存
  void clearCache() {
    _cachedArticles = null;
    _cachedCategories = null;
    _cachedCollections = null;
  }
}
