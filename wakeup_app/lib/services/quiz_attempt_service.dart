import 'api_service.dart';
import 'user_service.dart';

/// 测验尝试服务类，处理用户测验记录和成绩相关的API请求
class QuizAttemptService extends ApiService {
  /// 单例模式
  static final QuizAttemptService _instance = QuizAttemptService._internal();
  
  factory QuizAttemptService() {
    return _instance;
  }
  
  QuizAttemptService._internal();
  
  /// 用户服务实例
  final UserService _userService = UserService();
  
  /// 创建新的测验尝试
  /// [questionSetId] 问题集ID
  Future<int?> createQuizAttempt(int questionSetId) async {
    try {
      final token = await _userService.getAuthToken();
      final response = await post('/quiz/attempt', 
        token: token,
        body: {
          'question_set_id': questionSetId
        },
        expectList: false
      );
      
      if (response['attempt_id'] != null) {
        return int.parse(response['attempt_id'].toString());
      }
      
      return null;
    } catch (e) {
      print('创建测验尝试失败: $e');
      return null;
    }
  }
  
  /// 提交答案
  /// [quizAttemptId] 测验尝试ID
  /// [questionId] 问题ID
  /// [answer] 用户答案
  Future<bool> submitAnswer(int quizAttemptId, int questionId, String answer) async {
    try {
      final token = await _userService.getAuthToken();
      final response = await post('/quiz/submit_answer', 
        token: token,
        body: {
          'quiz_attempt_id': quizAttemptId,
          'question_id': questionId,
          'answer': answer
        },
        expectList: false
      );
      
      return response['is_correct'] == true;
    } catch (e) {
      print('提交答案失败: $e');
      return false;
    }
  }
  
  /// 完成测验尝试
  /// [quizAttemptId] 测验尝试ID
  Future<Map<String, dynamic>?> completeQuizAttempt(int quizAttemptId) async {
    try {
      final token = await _userService.getAuthToken();
      final response = await post('/quiz/complete_attempt', 
        token: token,
        body: {
          'quiz_attempt_id': quizAttemptId
        },
        expectList: false
      );
      
      if (response['success'] == true) {
        return {
          'score': response['score'],
          'max_score': response['max_score'],
          'correct_rate': response['correct_rate'],
          'completed': true
        };
      }
      
      return null;
    } catch (e) {
      print('完成测验尝试失败: $e');
      return null;
    }
  }
  
  /// 获取测验尝试历史
  /// [questionSetId] 问题集ID，可选，用于筛选特定问题集的测验记录
  Future<List<Map<String, dynamic>>> getQuizAttemptHistory({int? questionSetId}) async {
    try {
      final token = await _userService.getAuthToken();
      final queryParams = <String, dynamic>{};
      
      if (questionSetId != null) {
        queryParams['question_set_id'] = questionSetId.toString();
      }
      
      final response = await get('/quiz/attempts', 
        token: token,
        queryParams: queryParams,
        expectList: true
      );
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('获取测验尝试历史失败: $e');
      return [];
    }
  }
  
  /// 获取测验尝试详情
  /// [quizAttemptId] 测验尝试ID
  Future<Map<String, dynamic>?> getQuizAttemptDetail(int quizAttemptId) async {
    try {
      final token = await _userService.getAuthToken();
      final response = await get('/quiz/attempt_detail', 
        token: token,
        queryParams: {
          'quiz_attempt_id': quizAttemptId.toString()
        },
        expectList: false
      );
      
      return response;
    } catch (e) {
      print('获取测验尝试详情失败: $e');
      return null;
    }
  }
  
  /// 获取用户统计数据
  /// [courseId] 课程ID
  Future<Map<String, dynamic>?> getUserStats(int courseId) async {
    try {
      final token = await _userService.getAuthToken();
      final response = await get('/quiz/user_stats', 
        token: token,
        queryParams: {
          'course_id': courseId.toString()
        },
        expectList: false
      );
      
      return response;
    } catch (e) {
      print('获取用户统计数据失败: $e');
      return null;
    }
  }
  
  /// 获取用户问题类型统计
  /// [questionSetId] 问题集ID
  Future<Map<String, dynamic>?> getUserQuestionTypeStats(int questionSetId) async {
    try {
      final token = await _userService.getAuthToken();
      final response = await get('/quiz/question_type_stats', 
        token: token,
        queryParams: {
          'question_set_id': questionSetId.toString()
        },
        expectList: false
      );
      
      return response;
    } catch (e) {
      print('获取用户问题类型统计失败: $e');
      return null;
    }
  }
} 