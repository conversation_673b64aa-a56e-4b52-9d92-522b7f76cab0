import 'dart:convert';
import 'package:http/http.dart' as http;
import 'api_service.dart';
import 'auth_service.dart';

/// 登录服务类
/// 提供与用户登录相关的功能
class LoginService extends ApiService {
  /// 单例模式
  static final LoginService _instance = LoginService._internal();

  factory LoginService() {
    return _instance;
  }

  LoginService._internal();

  /// 使用手机号和验证码登录
  Future<Map<String, dynamic>> loginWithPhoneAndCode(String phone, String code) async {
    try {
      final response = await http.post(
        Uri.parse('${AuthService.baseUrl}/api/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'phone': phone, 'code': code}),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);

        // 检查是否有错误消息
        if (jsonData.containsKey('message') &&
            (response.statusCode >= 400 ||
                jsonData['message'].toString().contains('错误'))) {
          return {'success': false, 'message': jsonData['message'] ?? '未知错误'};
        }

        // 检查正确的响应格式
        if (jsonData.containsKey('user_id') && jsonData.containsKey('token')) {
          final userId = jsonData['user_id'];
          final token = jsonData['token'];

          // 保存登录信息
          final authService = AuthService();
          await authService.saveToken(token);
          await authService.savePhone(phone);

          return {
            'success': true,
            'message': '登录成功',
            'userId': userId,
            'token': token,
          };
        }

        // 未知的响应格式
        return {'success': false, 'message': '服务器返回格式异常'};
      } else {
        // 处理非200响应
        try {
          final errorData = json.decode(response.body);
          return {
            'success': false,
            'message': errorData['message'] ?? '服务器错误',
          };
        } catch (e) {
          return {'success': false, 'message': '服务器错误'};
        }
      }
    } catch (e) {
      print('登录请求异常: ');
      return {'success': false, 'message': '网络错误: '};
    }
  }
  
  /// 检查用户是否已登录
  Future<bool> isUserLoggedIn() async {
    final authService = AuthService();
    return await authService.isAuthenticated();
  }
  
  /// 登出
  Future<Map<String, dynamic>> logout() async {
    final authService = AuthService();
    return await authService.logout();
  }
}
