import 'package:flutter/foundation.dart';
import 'dart:async';

/// 优化的 ChangeNotifier，减少不必要的通知
class OptimizedChangeNotifier extends ChangeNotifier {
  bool _isDisposed = false;
  final Set<String> _pendingNotifications = {};
  Timer? _debounceTimer;

  @override
  bool get hasListeners => !_isDisposed && super.hasListeners;

  @override
  void dispose() {
    _isDisposed = true;
    _debounceTimer?.cancel();
    super.dispose();
  }

  /// 安全的通知，检查是否已释放
  void safeNotifyListeners([String? notificationKey]) {
    if (_isDisposed || !hasListeners) return;

    if (notificationKey != null) {
      // 防止重复通知
      if (_pendingNotifications.contains(notificationKey)) return;
      _pendingNotifications.add(notificationKey);
    }

    notifyListeners();

    if (notificationKey != null) {
      _pendingNotifications.remove(notificationKey);
    }
  }

  /// 防抖通知，短时间内多次调用只执行最后一次
  void debouncedNotifyListeners({
    Duration delay = const Duration(milliseconds: 100),
    String? notificationKey,
  }) {
    if (_isDisposed || !hasListeners) return;

    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, () {
      safeNotifyListeners(notificationKey);
    });
  }

  /// 条件性通知，只在值真正改变时才通知
  void conditionalNotifyListeners<T>(T newValue, T currentValue) {
    if (_isDisposed || !hasListeners || newValue == currentValue) return;
    notifyListeners();
  }
}

/// 分片状态管理，支持局部更新
class SegmentedStateNotifier<T> extends OptimizedChangeNotifier {
  final Map<String, T> _segments = {};
  final Map<String, Set<VoidCallback>> _segmentListeners = {};

  /// 获取分片数据
  T? getSegment(String key) => _segments[key];

  /// 设置分片数据
  void setSegment(String key, T value) {
    final oldValue = _segments[key];
    if (oldValue == value) return;

    _segments[key] = value;

    // 只通知订阅该分片的监听器
    final listeners = _segmentListeners[key];
    if (listeners != null) {
      for (final listener in listeners) {
        listener();
      }
    }
  }

  /// 订阅特定分片的变化
  void addSegmentListener(String key, VoidCallback listener) {
    _segmentListeners.putIfAbsent(key, () => {}).add(listener);
  }

  /// 取消订阅分片
  void removeSegmentListener(String key, VoidCallback listener) {
    _segmentListeners[key]?.remove(listener);
    if (_segmentListeners[key]?.isEmpty == true) {
      _segmentListeners.remove(key);
    }
  }

  /// 批量更新多个分片
  void batchUpdateSegments(Map<String, T> updates) {
    final notifiedSegments = <String>{};

    for (final entry in updates.entries) {
      final key = entry.key;
      final value = entry.value;
      final oldValue = _segments[key];

      if (oldValue != value) {
        _segments[key] = value;
        notifiedSegments.add(key);
      }
    }

    // 批量通知所有变更的分片
    for (final key in notifiedSegments) {
      final listeners = _segmentListeners[key];
      if (listeners != null) {
        for (final listener in listeners) {
          listener();
        }
      }
    }
  }

  @override
  void dispose() {
    _segments.clear();
    _segmentListeners.clear();
    super.dispose();
  }
}

/// 异步状态管理器
class AsyncStateNotifier<T> extends OptimizedChangeNotifier {
  T? _data;
  bool _isLoading = false;
  String? _error;
  DateTime? _lastUpdated;

  T? get data => _data;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime? get lastUpdated => _lastUpdated;
  bool get hasData => _data != null;
  bool get hasError => _error != null;

  /// 执行异步操作
  Future<void> execute(Future<T> Function() operation) async {
    setLoading(true);
    setError(null);

    try {
      final result = await operation();
      setData(result);
    } catch (e) {
      setError(e.toString());
    } finally {
      setLoading(false);
    }
  }

  /// 设置数据
  void setData(T data) {
    _data = data;
    _lastUpdated = DateTime.now();
    safeNotifyListeners('data');
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    if (_isLoading == loading) return;
    _isLoading = loading;
    safeNotifyListeners('loading');
  }

  /// 设置错误信息
  void setError(String? error) {
    if (_error == error) return;
    _error = error;
    safeNotifyListeners('error');
  }

  /// 清除状态
  void clear() {
    _data = null;
    _error = null;
    _isLoading = false;
    _lastUpdated = null;
    safeNotifyListeners('clear');
  }

  /// 刷新数据
  Future<void> refresh(Future<T> Function() operation) async {
    await execute(operation);
  }
}
