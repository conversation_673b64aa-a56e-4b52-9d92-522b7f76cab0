import 'package:flutter/material.dart';

/// 懒加载路由管理器，支持代码拆分
class LazyRouteManager {
  static final Map<String, Widget Function()> _lazyWidgets = {};
  static final Map<String, Widget> _cachedWidgets = {};

  /// 注册懒加载路由
  static void registerLazyRoute(String routeName, Widget Function() builder) {
    _lazyWidgets[routeName] = builder;
  }

  /// 获取懒加载的页面
  static Widget getLazyWidget(String routeName) {
    // 检查缓存
    if (_cachedWidgets.containsKey(routeName)) {
      return _cachedWidgets[routeName]!;
    }

    // 懒加载构建
    final builder = _lazyWidgets[routeName];
    if (builder != null) {
      final widget = builder();
      _cachedWidgets[routeName] = widget;
      return widget;
    }

    throw Exception('Lazy route not found: $routeName');
  }

  /// 预加载特定路由
  static void preloadRoute(String routeName) {
    if (!_cachedWidgets.containsKey(routeName)) {
      getLazyWidget(routeName);
    }
  }

  /// 清理缓存的路由
  static void clearRouteCache([String? routeName]) {
    if (routeName != null) {
      _cachedWidgets.remove(routeName);
    } else {
      _cachedWidgets.clear();
    }
  }
}

/// 懒加载页面包装器
class LazyPageWrapper extends StatelessWidget {
  final String routeName;
  final Widget? loadingWidget;

  const LazyPageWrapper({
    super.key,
    required this.routeName,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Widget>(
      future: Future.microtask(() => LazyRouteManager.getLazyWidget(routeName)),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        }

        return loadingWidget ?? const _DefaultLoadingWidget();
      },
    );
  }
}

class _DefaultLoadingWidget extends StatelessWidget {
  const _DefaultLoadingWidget();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}

/// 智能预加载管理器
class IntelligentPreloader {
  static final Set<String> _preloadedRoutes = {};
  static final Map<String, int> _routeAccessCount = {};
  static final Map<String, DateTime> _lastAccessTime = {};

  /// 记录路由访问
  static void recordAccess(String routeName) {
    _routeAccessCount[routeName] = (_routeAccessCount[routeName] ?? 0) + 1;
    _lastAccessTime[routeName] = DateTime.now();
  }

  /// 智能预加载高频路由
  static void intelligentPreload() {
    final now = DateTime.now();
    final popularRoutes =
        _routeAccessCount.entries
            .where((entry) {
              final lastAccess = _lastAccessTime[entry.key];
              return entry.value >= 3 && // 访问次数 >= 3
                  lastAccess != null &&
                  now.difference(lastAccess).inDays <= 7; // 7天内访问过
            })
            .map((entry) => entry.key)
            .toList();

    for (final routeName in popularRoutes) {
      if (!_preloadedRoutes.contains(routeName)) {
        _preloadRoute(routeName);
      }
    }
  }

  static void _preloadRoute(String routeName) {
    try {
      LazyRouteManager.preloadRoute(routeName);
      _preloadedRoutes.add(routeName);
      debugPrint('🚀 [Preloader] 预加载路由: $routeName');
    } catch (e) {
      debugPrint('❌ [Preloader] 预加载失败: $routeName, $e');
    }
  }

  /// 清理预加载缓存
  static void cleanup() {
    final now = DateTime.now();
    final expiredRoutes =
        _lastAccessTime.entries
            .where((entry) => now.difference(entry.value).inDays > 7)
            .map((entry) => entry.key)
            .toList();

    for (final routeName in expiredRoutes) {
      LazyRouteManager.clearRouteCache(routeName);
      _preloadedRoutes.remove(routeName);
      _routeAccessCount.remove(routeName);
      _lastAccessTime.remove(routeName);
    }
  }
}
