import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'dart:async';
import 'dart:developer' as developer;
import '../config/performance_config.dart';

/// 性能监控器
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, PerformanceMetric> _metrics = {};
  final List<PerformanceAlert> _alerts = [];
  Timer? _memoryMonitorTimer;

  /// 启动性能监控
  void startMonitoring() {
    if (kDebugMode && PerformanceConfig.enablePerformanceDebugging) {
      _startMemoryMonitoring();
      _startFPSMonitoring();
      debugPrint('🔍 [Performance] 性能监控已启动');
    }
  }

  /// 停止性能监控
  void stopMonitoring() {
    _memoryMonitorTimer?.cancel();
    debugPrint('🔍 [Performance] 性能监控已停止');
  }

  /// 记录性能指标
  void recordMetric(String name, double value, {String? unit}) {
    final metric = _metrics.putIfAbsent(
      name,
      () => PerformanceMetric(name: name, unit: unit),
    );

    metric.addValue(value);

    // 检查是否需要发出警告
    _checkPerformanceThresholds(metric);
  }

  /// 开始计时
  Stopwatch startTimer(String operationName) {
    final stopwatch = Stopwatch()..start();
    debugPrint('⏱️ [Performance] 开始计时: $operationName');
    return stopwatch;
  }

  /// 结束计时并记录
  void endTimer(String operationName, Stopwatch stopwatch) {
    stopwatch.stop();
    final milliseconds = stopwatch.elapsedMilliseconds.toDouble();
    recordMetric('${operationName}_duration', milliseconds, unit: 'ms');

    if (milliseconds > 100) {
      debugPrint('⚠️ [Performance] 操作耗时过长: $operationName (${milliseconds}ms)');
    }
  }

  /// 监控内存使用
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(
      PerformanceConfig.memoryMonitorInterval,
      (_) => _recordMemoryUsage(),
    );
  }

  /// 监控 FPS
  void _startFPSMonitoring() {
    WidgetsBinding.instance.addTimingsCallback((timings) {
      for (final timing in timings) {
        final frameDuration = timing.totalSpan.inMicroseconds / 1000.0;
        recordMetric('frame_duration', frameDuration, unit: 'ms');

        // 检查掉帧
        if (frameDuration > 16.67) {
          // 60 FPS = 16.67ms per frame
          final fps = 1000 / frameDuration;
          if (fps < PerformanceConfig.fpsAlertThreshold) {
            _addAlert(
              PerformanceAlert(
                type: PerformanceAlertType.lowFPS,
                message: 'FPS 过低: ${fps.toStringAsFixed(1)}',
                timestamp: DateTime.now(),
              ),
            );
          }
        }
      }
    });
  }

  /// 记录内存使用情况
  void _recordMemoryUsage() async {
    try {
      final info = await developer.Service.getInfo();
      // 注意：这个API在生产环境中可能不可用
      if (info.serverUri != null) {
        // 记录内存使用（这里需要调用具体的内存API）
        recordMetric('memory_usage', 0, unit: 'MB'); // 占位符
      }
    } catch (e) {
      // 生产环境中忽略错误
    }
  }

  /// 检查性能阈值
  void _checkPerformanceThresholds(PerformanceMetric metric) {
    switch (metric.name) {
      case 'frame_duration':
        if (metric.average > PerformanceConfig.frameTimeAlertThreshold) {
          _addAlert(
            PerformanceAlert(
              type: PerformanceAlertType.highFrameTime,
              message: '平均帧时间过高: ${metric.average.toStringAsFixed(2)}ms',
              timestamp: DateTime.now(),
            ),
          );
        }
        break;
      case 'memory_usage':
        if (metric.latest > PerformanceConfig.memoryAlertThreshold) {
          _addAlert(
            PerformanceAlert(
              type: PerformanceAlertType.highMemoryUsage,
              message: '内存使用过高: ${metric.latest.toStringAsFixed(1)}MB',
              timestamp: DateTime.now(),
            ),
          );
        }
        break;
    }
  }

  /// 添加性能警告
  void _addAlert(PerformanceAlert alert) {
    _alerts.add(alert);
    debugPrint('🚨 [Performance] ${alert.message}');

    // 保持警告列表不超过 50 条
    if (_alerts.length > 50) {
      _alerts.removeAt(0);
    }
  }

  /// 获取性能报告
  PerformanceReport getReport() {
    return PerformanceReport(
      metrics: Map.from(_metrics),
      alerts: List.from(_alerts),
      generatedAt: DateTime.now(),
    );
  }

  /// 清理旧数据
  void cleanup() {
    final cutoff = DateTime.now().subtract(const Duration(hours: 1));
    _alerts.removeWhere((alert) => alert.timestamp.isBefore(cutoff));

    for (final metric in _metrics.values) {
      metric.cleanup();
    }
  }
}

/// 性能指标
class PerformanceMetric {
  final String name;
  final String? unit;
  final List<double> _values = [];
  final List<DateTime> _timestamps = [];

  PerformanceMetric({required this.name, this.unit});

  void addValue(double value) {
    _values.add(value);
    _timestamps.add(DateTime.now());

    // 保持最近 100 个值
    if (_values.length > 100) {
      _values.removeAt(0);
      _timestamps.removeAt(0);
    }
  }

  double get latest => _values.isNotEmpty ? _values.last : 0;
  double get average =>
      _values.isNotEmpty ? _values.reduce((a, b) => a + b) / _values.length : 0;
  double get max =>
      _values.isNotEmpty ? _values.reduce((a, b) => a > b ? a : b) : 0;
  double get min =>
      _values.isNotEmpty ? _values.reduce((a, b) => a < b ? a : b) : 0;

  void cleanup() {
    final cutoff = DateTime.now().subtract(const Duration(minutes: 30));
    while (_timestamps.isNotEmpty && _timestamps.first.isBefore(cutoff)) {
      _timestamps.removeAt(0);
      _values.removeAt(0);
    }
  }
}

/// 性能警告
class PerformanceAlert {
  final PerformanceAlertType type;
  final String message;
  final DateTime timestamp;

  const PerformanceAlert({
    required this.type,
    required this.message,
    required this.timestamp,
  });
}

enum PerformanceAlertType {
  lowFPS,
  highFrameTime,
  highMemoryUsage,
  slowOperation,
}

/// 性能报告
class PerformanceReport {
  final Map<String, PerformanceMetric> metrics;
  final List<PerformanceAlert> alerts;
  final DateTime generatedAt;

  const PerformanceReport({
    required this.metrics,
    required this.alerts,
    required this.generatedAt,
  });

  /// 生成可读的报告文本
  String toReadableString() {
    final buffer = StringBuffer();
    buffer.writeln('=== 性能报告 (${generatedAt.toString()}) ===');

    // 关键指标
    buffer.writeln('\n关键指标:');
    for (final metric in metrics.values) {
      buffer.writeln(
        '  ${metric.name}: ${metric.average.toStringAsFixed(2)} ${metric.unit ?? ''}',
      );
    }

    // 最近警告
    if (alerts.isNotEmpty) {
      buffer.writeln('\n最近警告:');
      for (final alert in alerts.take(5)) {
        buffer.writeln('  ${alert.message}');
      }
    }

    return buffer.toString();
  }
}
