/// 应用程序异常基类
abstract class AppException implements Exception {
  final String message;
  final int? statusCode;

  const AppException(this.message, [this.statusCode]);

  @override
  String toString() => message;
}

/// API请求异常
class ApiException extends AppException {
  const ApiException(String message, [int? statusCode]) 
      : super(message, statusCode);
}

/// 网络连接异常
class NetworkException extends AppException {
  const NetworkException(String message) : super(message);
}

/// 请求超时异常
class TimeoutException extends AppException {
  const TimeoutException(String message) : super(message);
}

/// 认证异常
class AuthException extends AppException {
  const AuthException(String message, [int? statusCode]) 
      : super(message, statusCode);
}

/// 未知异常
class UnknownException extends AppException {
  const UnknownException(String message) : super(message);
}

/// 数据解析异常
class ParseException extends AppException {
  const ParseException(String message) : super(message);
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException(String message) : super(message);
}