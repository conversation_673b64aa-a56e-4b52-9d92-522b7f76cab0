{"permissions": {"allow": ["Bash(flutter pub:*)", "Bash(flutter gen-l10n:*)", "Bash(flutter analyze:*)", "WebSearch", "WebFetch(domain:api.flutter.dev)", "WebFetch(domain:developer.apple.com)", "WebFetch(domain:medium.com)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(flutter run:*)", "<PERSON><PERSON>(flutter clean:*)", "Bash(rm:*)", "Bash(pod cache clean:*)", "Bash(pod install:*)", "<PERSON><PERSON>(pkill:*)", "Ba<PERSON>(flutter:*)"], "defaultMode": "acceptEdits"}}